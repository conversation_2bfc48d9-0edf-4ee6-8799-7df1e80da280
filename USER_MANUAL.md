# ADANI BOG (Boots on Ground) Angular Application - User Manual

## Table of Contents
1. [Project Overview](#project-overview)
2. [System Requirements](#system-requirements)
3. [Installation Guide](#installation-guide)
4. [User Roles and Permissions](#user-roles-and-permissions)
5. [Authentication System](#authentication-system)
6. [Feature Documentation](#feature-documentation)
7. [Form Validation Rules](#form-validation-rules)
8. [API Integration](#api-integration)
9. [Troubleshooting](#troubleshooting)
10. [Technical Architecture](#technical-architecture)

---

## Project Overview

The ADANI BOG (Boots on Ground) Angular application is a comprehensive digital platform designed to streamline and manage various operational and administrative activities across Adani facilities. Built with Angular 19.2.0, the application enhances efficiency, safety, and reporting through dedicated modules for key workflows.

### Main Purpose
- **Safety Management**: Track and manage safety training, incidents, and observations
- **Operational Efficiency**: Streamline field tours, QR code management, and plant operations
- **Administrative Control**: Comprehensive user management and role-based access control
- **Reporting & Analytics**: Real-time dashboards and comprehensive reporting capabilities
- **Crisis Management**: Digisafe reports with approval workflows and emergency response

### Key Business Domains
- Safety Training Management
- QR Code and Tour Management
- Incident and Observation Management
- Plant and Administrative Management
- Digisafe Crisis Management
- Master Data Management

---

## System Requirements

### Prerequisites
- **Node.js**: Version 18+ (recommended)
- **npm**: Version 8+ or **yarn**: Version 1.22+
- **Angular CLI**: Version 19+ (`npm install -g @angular/cli@^19`)
- **Modern Web Browser**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+

### Development Environment
- **Operating System**: Windows 10+, macOS 10.15+, or Linux Ubuntu 18.04+
- **Memory**: Minimum 8GB RAM (16GB recommended)
- **Storage**: At least 2GB free space for dependencies

### Production Environment
- **Web Server**: Nginx, Apache, or IIS
- **HTTPS**: SSL certificate required for production deployment
- **API Backend**: Compatible REST API server

---

## Installation Guide

### Step 1: Clone the Repository
```bash
git clone <repository_url>
cd bog-adani-angular
```

### Step 2: Install Dependencies
```bash
# Using npm
npm install

# Using yarn (recommended)
yarn install
```

### Step 3: Environment Configuration
Configure the API endpoint in `src/app/enviornments/enviornments.ts`:
```typescript
export const environment = {
    production: false,
    apiUrl: 'https://your-api-url.com/'
};
```

### Step 4: Development Server
```bash
# Start development server
ng serve
# or
npm start

# Application will be available at http://localhost:4200/
```

### Step 5: Production Build
```bash
# Build for production
ng build --configuration production

# Build artifacts will be in dist/bog-adani-angular/
```

---

## User Roles and Permissions

The application implements a comprehensive role-based access control system with three primary user types:

### 1. Super Admin (Role ID: 1)
**Full System Access**
- **User Management**: Create, edit, delete all user types including other Super Admins
- **Plant Management**: Manage all plants across the organization
- **Master Data**: Complete access to all master data management modules
- **Reports**: Access to all reports and analytics across all plants
- **System Settings**: Configure application settings and crisis management
- **Special Privileges**: 
  - Can change record states in any status
  - Edit records regardless of approval status
  - Access to all navigation menu items

### 2. Plant Admin (Role ID: 2)
**Plant-Specific Administrative Access**
- **User Management**: 
  - Create and manage regular users (Role ID: 3) only
  - Transfer regular users between plants (requires access to multiple plants)
  - Cannot edit Super Admin users or access role dropdown when editing
- **Plant Operations**: Limited to assigned plants only
- **Data Access**: Filtered by assigned plant IDs
- **Special Privileges**:
  - Department ID 32 has special edit privileges for certain records
  - Can approve/reject reports with approveId/rejectedId
- **Restrictions**:
  - Cannot access Master Management modules
  - Cannot create Plant Admins or Super Admins
  - Pre-filtered data based on assigned plants

### 3. Regular User (Role ID: 3)
**Operational Access**
- **Field Operations**: Tour management, QR code scanning, observation recording
- **Incident Reporting**: Create and manage incident reports
- **Safety Training**: Participate in and view training records
- **Limited Access**: Cannot access administrative functions or user management

### Role-Based Navigation
The sidebar navigation dynamically filters based on user roles:
- **ALL_ADMIN_ROLES**: Accessible to both Super Admin and Plant Admin
- **SUPER_ADMIN_ONLY**: Restricted to Super Admin users only

---

## Authentication System

### Login Methods

#### 1. OTP-Based Authentication
- **Email Verification**: Must use @adani.com domain
- **OTP Generation**: System sends OTP to registered email
- **OTP Validation**: Enter 6-digit OTP for verification
- **Session Management**: JWT token stored in localStorage

#### 2. SAML SSO Authentication
- **Enterprise Integration**: Single Sign-On with corporate identity provider
- **Email Validation**: Checks user permissions before SAML redirect
- **Automatic Redirect**: Seamless integration with existing corporate login

### Security Features
- **Auth Guard**: Protects all authenticated routes
- **Token Management**: Automatic token refresh and validation
- **Role Verification**: Server-side role validation
- **Session Timeout**: Automatic logout on token expiration

### User Registration
New users can register through a two-step process:
1. **Personal Information**: Name, DOB, gender, contact number
2. **Professional Details**: Email, department, designation, plant assignment

---

## Feature Documentation

### Dashboard
**Central Command Center**
- **Key Metrics**: User count, tour statistics, incident tracking, observation summary
- **Visual Analytics**: Bubble charts, cluster-wise graphs, status indicators
- **Real-time Data**: Auto-refreshing dashboard with current operational status
- **Role-based Filtering**: Data filtered by user's assigned plants

### Safety Training Management
**Comprehensive Training Oversight**
- **Training Dashboard**: Visual analytics with charts and graphs
- **Record Management**: Add, edit, filter training records
- **Validation Rules**:
  - Faculty name: Alphabets only, max 30 characters
  - Topic/Description: Max 200 characters
  - Date validation with min/max date constraints
- **Reporting**: Excel export with month-wise data

### QR Code Management
**Asset and Location Tracking**
- **QR Code Generation**: Create permanent and temporary QR codes
- **Scan Tracking**: Monitor QR code usage and scan counts
- **Plant-based Filtering**: Role-based access to QR codes
- **Bulk Operations**: Mass QR code generation and management

### Tour Management
**Field Tour Coordination**
- **Tour Planning**: Define routes and scan points
- **Progress Tracking**: Monitor tour completion and observations
- **User Assignment**: Assign tours to specific personnel
- **Real-time Updates**: Live tour status and location tracking

### Incident Management
**Safety Event Tracking**
- **Incident Reporting**: Structured incident recording process
- **Status Management**: Draft, Submitted, Approved, Rejected states
- **Approval Workflow**: Multi-level approval process
- **Image/Video Support**: Media upload for incident documentation
- **Role-based Actions**: Different permissions for different user roles

### Plant Management
**Facility Administration**
- **Plant Information**: Manage plant details, emergency contacts
- **Transfer Requests**: Handle plant transfer workflows
- **Admin Assignment**: Assign Plant Admins to facilities
- **Validation Rules**:
  - Plant name: Alphabets only
  - Emergency contact: 10-digit numbers only

### Observation Management
**Field Observation Tracking**
- **Observation Types**: Safe Act, Unsafe Act, Unsafe Conditions
- **Assignment System**: Assign observations to specific personnel
- **Reward System**: Integrated reward mechanism for positive observations
- **Status Tracking**: Complete observation lifecycle management

### Digisafe Crisis Management
**Emergency Response System**
- **Crisis Reporting**: Structured crisis event documentation
- **Approval Workflow**: Multi-stage approval process
- **MIS Dashboard**: Management Information System with key metrics
- **GMR Dashboard**: Graphical Management Reports
- **Property Damage Calculation**: Automatic calculation of fire and non-fire incidents
- **Email Notifications**: Automated crisis communication

### Master Data Management
**System Configuration** (Super Admin Only)
- **Organizational Structure**: Departments, Designations, Clusters
- **Operational Data**: Plant Types, Locations, Equipment
- **Safety Data**: Incident Types, Body Parts, Root Causes
- **System References**: QR Types, Inspection Tools, Recommended Types

---

## Form Validation Rules

### Input Field Validation

#### Name Fields
- **Pattern**: Alphabets only with spaces
- **Length**: Maximum 30 characters
- **Required**: First name and last name mandatory
- **Real-time Counter**: Character count display

#### Email Validation
- **Domain Restriction**: Must end with @adani.com
- **Format Validation**: Standard email format required
- **Local Part**: Must contain at least one alphabet character
- **Uniqueness Check**: Server-side duplicate email validation

#### Contact Numbers
- **Pattern**: Exactly 10 digits
- **Format**: Numbers only, no special characters
- **Validation**: Real-time format checking

#### Text Areas
- **Character Limit**: Maximum 200 characters for descriptions
- **Character Counter**: Visible only when typing
- **Trimming**: Automatic whitespace removal

#### Date Fields
- **Format**: ISO date format (YYYY-MM-DD)
- **Range Validation**: Min/max date constraints
- **Required Fields**: DOB and other date fields mandatory

#### File Uploads
- **Image Types**: PNG, JPEG, GIF only
- **Size Limit**: Maximum 5MB per file
- **Multiple Files**: Support for multiple image/video uploads
- **Preview**: Image preview before upload

### Auto-calculated Fields
- **Default Values**: Zero when input values are zero
- **Null Handling**: Other fields default to null
- **Real-time Calculation**: Automatic updates on input change

---

## API Integration

### Core API Service
The application uses Axios for HTTP communication with automatic:
- **Authentication**: Bearer token injection
- **Business Unit Filtering**: Automatic businessUnitId=0 filter
- **Error Handling**: Centralized error response management
- **Request Interceptors**: Automatic header configuration

### Key Endpoints
- **Authentication**: `/admins/superadmin-otp`, `/admins/verify-otp`
- **User Management**: `/admins`, `/admins/add-admin-web`
- **Plant Operations**: `/plant`, `/plant-transfer-request`
- **Safety Training**: `/training`, `/training/male-female-count`
- **Incident Management**: `/incident`, `/incident-master`
- **Digisafe**: `/digisafe`, `/digisafe/mis-report`
- **Notifications**: `/Notifications/send-notification-plant-wise`

### Data Flow
1. **Request Processing**: Automatic businessUnitId injection
2. **Authentication**: JWT token validation
3. **Role-based Filtering**: Server-side permission checking
4. **Response Handling**: Standardized error format {message, responseCode}

---

## Troubleshooting

### Common Issues

#### Authentication Problems
- **Invalid Token**: Clear localStorage and re-login
- **OTP Not Received**: Check email spam folder, verify @adani.com domain
- **SAML Issues**: Verify corporate SSO configuration

#### Permission Errors
- **Access Denied**: Verify user role and plant assignments
- **Missing Menu Items**: Check role-based navigation permissions
- **Data Not Loading**: Confirm businessUnitId filtering

#### Form Validation Issues
- **Email Validation**: Ensure @adani.com domain
- **Character Limits**: Check field-specific length restrictions
- **Required Fields**: All mandatory fields must be completed

#### Performance Issues
- **Slow Loading**: Check network connection and API response times
- **Memory Usage**: Clear browser cache and restart application
- **Chart Rendering**: Verify ApexCharts library loading

### Error Codes
- **200**: Success
- **400**: Bad Request - Check form validation
- **401**: Unauthorized - Re-authenticate
- **403**: Forbidden - Check user permissions
- **404**: Not Found - Verify API endpoints
- **500**: Server Error - Contact system administrator

---

## Technical Architecture

### Frontend Architecture
- **Framework**: Angular 19.2.0 with standalone components
- **State Management**: Service-based with RxJS observables
- **Routing**: Angular Router with auth guards
- **UI Components**: Angular Material + Bootstrap 5.3.3
- **Charts**: ApexCharts for data visualization
- **HTTP Client**: Axios for API communication

### Key Dependencies
- **UI Libraries**: @angular/material, @ng-bootstrap/ng-bootstrap
- **Charts**: ng-apexcharts, d3
- **Forms**: @angular/forms with reactive forms
- **Rich Text**: ngx-quill for text editing
- **File Handling**: file-saver, xlsx for exports
- **Authentication**: ng-otp-input for OTP handling

### Component Structure
```
src/app/
├── components/          # Feature-specific components
├── core/               # Core services and utilities
├── model/              # TypeScript interfaces
├── services/           # Business logic services
├── shared/             # Reusable components
└── environments/       # Configuration files
```

### Service Architecture
- **ApiService**: Centralized HTTP communication
- **AuthService**: Authentication and session management
- **Feature Services**: Domain-specific business logic
- **Utility Services**: Helper functions and validators

### Security Implementation
- **Auth Guard**: Route protection
- **Role-based Access**: Component-level permission checking
- **Input Validation**: Client and server-side validation
- **XSS Protection**: Angular's built-in sanitization

---

*This manual provides comprehensive guidance for using the ADANI BOG Angular application. For technical support or additional information, please contact the system administrator.*
