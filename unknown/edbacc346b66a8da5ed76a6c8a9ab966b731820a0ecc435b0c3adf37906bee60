<app-toast-message></app-toast-message>
<div class="card" id="activated-qrcode">
    <div class="card-header d-flex align-items-center justify-content-between">
        <div>
            <h6 class="mb-0">Root Cause</h6>
        </div>
        <div class="d-flex align-items-center">
                <!-- *** REPLACE the existing simple Download button with this Dropdown *** -->
                <div ngbDropdown class="d-inline-block me-2">
                    <button type="button" class="btn btn-sm adani-btn dropdown-toggle" id="downloadRCExcelDropdown"
                        ngbDropdownToggle [disabled]="isDownloadingExcel || listLoading">
                        <span *ngIf="!isDownloadingExcel">
                            <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
                        </span>
                        <span *ngIf="isDownloadingExcel">
                            <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                            Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '')
                            }}...
                        </span>
                    </button>
                    <ul ngbDropdownMenu aria-labelledby="downloadRCExcelDropdown">
                        <li>
                            <button ngbDropdownItem (click)="downloadExcel('current')"
                                [disabled]="isDownloadingExcel || listLoading || (rootCauseList.length === 0)">
                                <i class="bi bi-download me-1"></i> Download Current Page ({{ rootCauseList.length
                                }})
                            </button>
                        </li>
                        <li>
                            <button ngbDropdownItem (click)="downloadExcel('all')"
                                [disabled]="isDownloadingExcel || listLoading || totalItems === 0">
                                <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
                            </button>
                        </li>
                    </ul>
                </div>
                <!-- *** END REPLACEMENT *** -->
                <!-- *** NEW: Create Button *** -->
                <button class="btn-sm adani-btn ms-2" (click)="openCreateModal()" title="Create New Root Cause">
                    <i class="bi bi-plus-circle"></i> Create New
                </button>
                <img src="../../../assets/svg/filter.svg" class="filter-button ms-3" (click)="openFilterModal()"
                    alt="Filter" style="width: 35px;" />
            </div>
        </div>
    <div class="card-body">
        <div class="table-container">
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-header">
                        <tr class="text-center">
                            <th scope="col">Id</th>
                            <th scope="col">Enabled/Disabled</th>
                            <th scope="col">Root Cause Name</th> <!-- Changed header -->
                            <th scope="col">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Loading Indicator -->
                        <tr *ngIf="listLoading">
                            <td colspan="4" class="text-center">
                                <div class="spinner-border spinner-border-sm" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                Loading Root Causes...
                            </td>
                        </tr>
                        <!-- No Data Message -->
                        <tr *ngIf="!listLoading && rootCauseList.length === 0">
                            <td colspan="4" class="text-center">No root causes found.</td>
                        </tr>
                        <!-- Data Rows -->
                        <tr *ngFor="let item of rootCauseList">
                            <td class="text-center">{{item.id}}</td>
                            <td>
                                <app-switch [(checked)]="item.enabled" [requireConfirmation]="true"
                                    (checkedChange)="onSwitchToggle($event, item)" onLabel="Active" offLabel="Inactive">
                                </app-switch>
                            </td>
                            <td>{{item.title}}</td>
                            <td class="actions text-center"> <!-- Added text-center -->
                                <!-- *** UPDATED: Edit Button Click Handler *** -->
                                <button class="adani-btn" (click)="openEditModal(item)" title="Edit Root Cause">
                                    <i class="bi bi-pencil edit"></i>
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div class="card-footer text-muted text-center">
        <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
            (pageChange)="onPageChange($event)"></app-pagination>
    </div>
</div>

<!-- Filter Offcanvas (Updated) -->
<app-offcanvas [title]="'Filter Root Causes'" *ngIf="isFilterModalOpen" (onClickCross)="closeFilterModal()">
    <div class="filter-container p-3">
        <form #filterForm="ngForm" (ngSubmit)="applyFilters()">
            <div class="row g-3">

                <div class="col-12">
                    <label class="form-label" for="filterRootCauseName">Root Cause Name</label>
                    <input type="text" id="filterRootCauseName" class="form-control"
                        placeholder="Search by Root Cause Name" [(ngModel)]="filters.name" name="name"
                        maxlength="30" pattern="^[a-zA-Z\s]*$" #filterNameInput="ngModel"
                        [ngClass]="{'is-invalid': filterNameInput.invalid && (filterNameInput.dirty || filterNameInput.touched)}">
                    <!-- Character count display - only visible when there's text -->
                    <small *ngIf="filters.name" class="text-muted d-block text-end mt-1">
                        {{ filters.name.length }}/30 characters
                    </small>
                    <div *ngIf="filterNameInput.invalid && (filterNameInput.dirty || filterNameInput.touched)" class="text-danger small mt-1">
                        <div *ngIf="filterNameInput.errors?.['pattern']">Root cause name should contain only alphabets.</div>
                    </div>
                </div>

                <div class="col-12">
                    <label class="form-label" for="filterEnabledRC">Enabled Status</label>
                    <select id="filterEnabledRC" class="form-select" [(ngModel)]="filters.enabled" name="enabled">
                        <!-- Bind to filters.enabled -->
                        <option [ngValue]="null">Any</option>
                        <option value="true">Yes</option>
                        <option value="false">No</option>
                    </select>
                </div>

                <div class="col-12">
                    <label class="form-label" for="filterSortByRC">Sort By</label>
                    <select id="filterSortByRC" class="form-select" [(ngModel)]="filters.sortField" name="sortField">
                        <!-- Bind to filters.sortField -->
                        <option [ngValue]="null">Default Sort (Name ASC)</option>
                        <option *ngFor="let field of availableSortFields" [value]="field.value">{{ field.label }}
                        </option>
                    </select>
                    <label class="form-label mt-2" for="filterSortDirRC">Sort Direction</label>
                    <select id="filterSortDirRC" class="form-select" [(ngModel)]="filters.sortDirection"
                        name="sortDirection"> <!-- Bind to filters.sortDirection -->
                        <option value="ASC">Ascending</option>
                        <option value="DESC">Descending</option>
                    </select>
                </div>

                <div class="col-12 mt-4 d-grid gap-2">
                    <button type="submit" class="btn adani-btn">
                        <i class="bi bi-search me-1"></i> Search
                    </button>
                    <button type="button" class="btn btn-secondary" (click)="resetFilters()">
                        <i class="bi bi-arrow-clockwise me-1"></i> Reset Filters
                    </button>
                </div>
            </div>
        </form>
    </div>
</app-offcanvas>


<!-- ********** NEW: Edit Offcanvas ********** -->
<app-offcanvas [title]="'Edit Root Cause'" *ngIf="isEditModalOpen" (onClickCross)="closeEditModal()">
    <div class="edit-container p-3">
        <form *ngIf="selectedRootCause" #editForm="ngForm" (ngSubmit)="submitEditForm()">
            <div class="row g-3">

                <!-- Root Cause ID (Readonly) -->
                <div class="col-12">
                    <label class="form-label" for="editRCId">Root Cause ID</label>
                    <input type="text" id="editRCId" class="form-control" [value]="selectedRootCause.id" name="id"
                        readonly disabled>
                </div>

                <!-- Root Cause Name (Editable) -->
                <div class="col-12">
                    <label class="form-label" for="editRCName">Root Cause Name</label>
                    <input type="text" id="editRCName" class="form-control" placeholder="Enter Root Cause Name"
                        [(ngModel)]="selectedRootCause.title" name="title" required #titleInput="ngModel"
                        maxlength="30" pattern="^[a-zA-Z\s]*$"
                        [ngClass]="{'is-invalid': titleInput.invalid && (titleInput.dirty || titleInput.touched)}">
                    <!-- Character count display - only visible when there's text -->
                    <small *ngIf="selectedRootCause.title" class="text-muted d-block text-end mt-1">
                        {{ selectedRootCause.title.length }}/30 characters
                    </small>
                    <div *ngIf="titleInput.invalid && (titleInput.dirty || titleInput.touched)"
                        class="text-danger small mt-1">
                        <div *ngIf="titleInput.errors?.['required']">Root cause name is required.</div>
                        <div *ngIf="titleInput.errors?.['pattern']">Root cause name should contain only alphabets.</div>
                        <div *ngIf="titleInput.errors?.['maxlength']">Root cause name cannot exceed 30 characters.</div>
                    </div>
                </div>

                <!-- Enabled Status -->
                <div class="col-12">
                    <label class="form-label d-block mb-2">Status</label>
                    <app-switch [(checked)]="selectedRootCause.enabled" name="enabled" onLabel="Active"
                        offLabel="Inactive">
                    </app-switch>
                </div>

                <!-- Action Buttons -->
                <div class="col-12 mt-4 d-flex justify-content-end gap-2">
                    <button type="button" class="btn btn-secondary" (click)="closeEditModal()">
                        Cancel
                    </button>
                    <button type="submit" class="btn adani-btn" [disabled]="editForm.invalid || editLoading">
                        <span *ngIf="!editLoading"><i class="bi bi-save me-1"></i> Save Changes</span>
                        <span *ngIf="editLoading" class="spinner-border spinner-border-sm" role="status"
                            aria-hidden="true"></span>
                        <span *ngIf="editLoading"> Saving...</span>
                    </button>
                </div>
            </div>
        </form>
        <div *ngIf="!selectedRootCause && isEditModalOpen" class="text-center p-5">
            <div class="spinner-border spinner-border-sm" role="status">
                <span class="visually-hidden">Loading form...</span>
            </div>
        </div>
    </div>
</app-offcanvas>
<!-- ********** END: Edit Offcanvas ********** -->

<!-- ********** NEW: Create Offcanvas ********** -->
<app-offcanvas [title]="'Create New Root Cause'" *ngIf="isCreateModalOpen" (onClickCross)="closeCreateModal()">
    <div class="create-container p-3">
        <form #createForm="ngForm" (ngSubmit)="submitCreateForm()">
            <div class="row g-3">

                <!-- Root Cause Name (Required) -->
                <div class="col-12">
                    <label class="form-label" for="createRCName">Root Cause Name</label>
                    <input type="text" id="createRCName" class="form-control" placeholder="Enter New Root Cause Name"
                        [(ngModel)]="newRootCauseData.title" name="title" required #createTitleInput="ngModel"
                        maxlength="30" pattern="^[a-zA-Z\s]*$"
                        [ngClass]="{'is-invalid': createTitleInput.invalid && (createTitleInput.dirty || createTitleInput.touched)}">
                    <!-- Character count display - only visible when there's text -->
                    <small *ngIf="newRootCauseData.title" class="text-muted d-block text-end mt-1">
                        {{ newRootCauseData.title.length }}/30 characters
                    </small>
                    <div *ngIf="createTitleInput.invalid && (createTitleInput.dirty || createTitleInput.touched)"
                        class="text-danger small mt-1">
                        <div *ngIf="createTitleInput.errors?.['required']">Root cause name is required.</div>
                        <div *ngIf="createTitleInput.errors?.['pattern']">Root cause name should contain only alphabets.</div>
                        <div *ngIf="createTitleInput.errors?.['maxlength']">Root cause name cannot exceed 30 characters.</div>
                    </div>
                </div>

                <!-- Enabled Status (Default to Active/true) -->
                <div class="col-12">
                    <label class="form-label d-block mb-2">Status</label>
                    <app-switch [(checked)]="newRootCauseData.enabled" name="enabled" onLabel="Active"
                        offLabel="Inactive">
                    </app-switch>
                </div>

                <!-- Action Buttons -->
                <div class="col-12 mt-4 d-flex justify-content-end gap-2">
                    <button type="button" class="btn btn-secondary" (click)="closeCreateModal()">
                        Cancel
                    </button>
                    <button type="submit" class="btn adani-btn" [disabled]="createForm.invalid || createLoading">
                        <span *ngIf="!createLoading"><i class="bi bi-plus-circle-fill me-1"></i> Create Root
                            Cause</span>
                        <span *ngIf="createLoading" class="spinner-border spinner-border-sm" role="status"
                            aria-hidden="true"></span>
                        <span *ngIf="createLoading"> Creating...</span>
                    </button>
                </div>
            </div>
        </form>
    </div>
</app-offcanvas>
<!-- ********** END: Create Offcanvas ********** -->
