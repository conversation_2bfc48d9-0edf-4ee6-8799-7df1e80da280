import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TabComponent } from '../../../shared/tab/tab.component';
import { PaginationComponent } from '../../../shared/pagination/pagination.component';
import { OffcanvasComponent } from '../../../shared/offcanvas/offcanvas.component';
import { DigisafeService } from '../../../services/digisafe/digisafe.service';
import { PlantManagementService } from '../../../services/plant-management/plant-management.service';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { ClusterService } from '../../../services/master-management/cluster/cluster.service';
import { OpcoService } from '../../../services/master-management/opco/opco.service';
import { PlantTypeService } from '../../../services/master-management/plant-type/plant-type.service';
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap';
import { ToastMessageComponent } from '../../../shared/toast-message/toast-message.component';
import * as XLSX from 'xlsx';

interface Plant {
  id: number;
  name: string;
  clusterId?: number;
  opcoId?: number;
  plantTypeId?: number;
}

interface Cluster {
  id: number;
  title: string;
}

interface Company {
  id: number;
  title: string;
}

interface PlantType {
  id: number;
  title: string;
}

interface GmrFilter {
  year: number | null;
  plantId: number | null;
  clusterId: number | null;
  opcoId: number | null; // Assuming 'Company' maps to OpCo
  plantTypeId: number | null;
}

interface GmrMonthlyData {
  month: number;
  Fatal: string;
  RLTI: string;
  MTC: string;
  RWC: string;
  MDL: string;
  MWD: string;
  ManHoursWorked: string;
  FAC: string;
  propertyDamage: string;
  recordableInjury: string;
  LTIFR: string;
  TIFR: string;
}

@Component({
  selector: 'app-gmr-dashboard',
  standalone: true,
  imports: [CommonModule, FormsModule, PaginationComponent, OffcanvasComponent, NgbDropdownModule, ToastMessageComponent],
  templateUrl: './gmr-dashboard.component.html',
  styleUrls: ['./gmr-dashboard.component.scss']
})
export class GmrDashboardComponent implements OnInit {
  @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;

  gmrData: GmrMonthlyData[] = [];
  transformedData: any = {}; // Will hold metrics as rows with month values
  months: number[] = []; // Will hold unique months
  isDownloadingExcel = false;
  downloadType: 'current' | 'all' | null = null;
  metricLabels: { [key: string]: string } = {
    'Fatal': 'Fatality',
    'RLTI': 'LTI (Reportable Lost Time Injury)',
    'MTC': 'MTC (Medical Treatment Case)',
    'RWC': 'RWC (Restricted Work Case)',
    'MDL': 'Days lost (LTI)',
    'MWD': 'Modified work days',
    'ManHoursWorked': 'Manhours',
    'FAC': 'First Aid',
    'propertyDamage': 'Property Damage',
    'recordableInjury': 'Recordable Injury',
    'LTIFR': 'LTIFR',
    'TIFR': 'TIFR'
  };
  isLoading: boolean = false;
  currentPage: number = 1;
  itemsPerPage: number = 100; // Adjust as needed
  totalItems: number = 0;

  filters: GmrFilter = {
    year: new Date().getFullYear(),
    plantId: null,
    clusterId: null,
    opcoId: null,
    plantTypeId: null,
  };
  isFilterModalOpen: boolean = false;

  availablePlants: Plant[] = [];
  availableClusters: Cluster[] = [];
  availableCompanies: Company[] = [];
  availablePlantTypes: PlantType[] = [];

  // User role properties
  currentUserRole: string = '';
  loggedInAdminId: number | null = null;
  loggedInPlantIds: number[] = [];
  componentRoles = {
    SUPER_ADMIN: 'super_admin',
    PLANT_ADMIN: 'plant_admin'
  };
  isPlantAdmin: boolean = false;

  constructor(
    private digisafeService: DigisafeService,
    private plantManagementService: PlantManagementService,
    private clusterService: ClusterService,
    private opcoService: OpcoService,
    private plantTypeService: PlantTypeService
  ) {}

  ngOnInit(): void {
    // Initialize with default months to ensure they're displayed immediately
    this.months = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];

    // Initialize the transformed data structure with empty values
    this.initializeTransformedData();

    // Check user role
    this.checkUserRole();

    // Load the actual data
    this.loadInitialData();
  }

  // Check if the current user is a plant admin and get their assigned plants
  checkUserRole(): void {
    try {
      const userString = localStorage.getItem('user');
      if (userString) {
        const currentUser = JSON.parse(userString);
        console.log('Current User:', currentUser);

        this.loggedInAdminId = currentUser?.id ?? null;
        this.loggedInPlantIds = (Array.isArray(currentUser?.plantIds) && currentUser.plantIds.length > 0)
          ? currentUser.plantIds.map((id: any) => Number(id)).filter((id: number) => !isNaN(id))
          : [];

        const roleId = currentUser?.adminsRoleId;
        if (roleId === 1) {
          this.currentUserRole = this.componentRoles.SUPER_ADMIN;
          this.isPlantAdmin = false;
        } else if (roleId === 2) {
          this.currentUserRole = this.componentRoles.PLANT_ADMIN;
          this.isPlantAdmin = true;
          if (this.loggedInPlantIds.length === 0) {
            console.error('Plant Admin has no assigned plants!');
            this.toast?.showErrorToast('User configuration error: Plant Admin has no assigned plants.');
          }
        } else {
          this.currentUserRole = '';
          this.isPlantAdmin = false;
        }

        console.log('User Role:', this.currentUserRole);
        console.log('Is Plant Admin:', this.isPlantAdmin);
        console.log('Assigned Plant IDs:', this.loggedInPlantIds);
      }
    } catch (error) {
      console.error('Error checking user role:', error);
    }
  }

  // Initialize the transformed data structure with default months
  initializeTransformedData(): void {
    Object.keys(this.metricLabels).forEach(metric => {
      this.transformedData[metric] = {
        label: this.metricLabels[metric],
        values: {}
      };

      // Initialize with empty values for all months
      this.months.forEach(month => {
        this.transformedData[metric].values[month] = '';
      });
    });
  }

  async loadInitialData(): Promise<void> {
    console.log('Loading initial data for GMR dashboard...');
    try {
      if (this.isPlantAdmin) {
        // For plant admin, only load plants assigned to them
        console.log('Loading data for Plant Admin...');
        await this.getPlants();
      } else {
        // For super admin, load all filter data
        console.log('Loading data for Super Admin...');
        await Promise.all([
          this.getClusters(),
          this.getCompanies(),
          this.getPlantTypes()
        ]);

        // Then load plants (which might depend on clusters)
        await this.getPlants();
      }

      // Finally load the main GMR data
      await this.loadGmrData();

      console.log('Initial data loading complete');
    } catch (error) {
      console.error('Error loading initial data:', error);
      this.toast?.showErrorToast('Failed to load some filter data. Please try refreshing the page.');
    }
  }

  async loadGmrData(): Promise<void> {
    if (this.isLoading) return;
    this.isLoading = true;
    this.gmrData = []; // Clear previous data
    this.totalItems = 0;

    // Don't clear transformedData and months on initial load
    // This ensures months are always displayed even if API call is slow

    const filterQueryParams: any = {};

    // Construct query parameters based on the filters object
    if (this.filters.year !== null) {
      filterQueryParams.year = this.filters.year;
      console.log("Filtering by Year:", this.filters.year);
    }

    if (this.filters.plantId !== null) {
      filterQueryParams.plantId = this.filters.plantId;
      console.log("Filtering by Plant ID:", this.filters.plantId);
    }

    if (this.filters.clusterId !== null && this.filters.clusterId !== undefined) {
      filterQueryParams.clusterId = this.filters.clusterId;
      console.log("Filtering by Cluster ID:", this.filters.clusterId);
    }

    if (this.filters.opcoId !== null && this.filters.opcoId !== undefined) {
      filterQueryParams.opcoId = this.filters.opcoId;
      console.log("Filtering by OpCo ID:", this.filters.opcoId);
    }

    if (this.filters.plantTypeId !== null && this.filters.plantTypeId !== undefined) {
      filterQueryParams.plantTypeId = this.filters.plantTypeId;
      console.log("Filtering by Plant Type ID:", this.filters.plantTypeId);
    }

    // Add pagination parameters
    filterQueryParams.page = this.currentPage;
    filterQueryParams.limit = this.itemsPerPage;

    try {
      console.log("GMR API Request Query Params:", filterQueryParams);

      // Call the actual API service method
      const response = await this.digisafeService.getGmrCount({ params: filterQueryParams });
      console.log("API Response:", response);

      // Handle different response structures
      if (response && typeof response === 'object') {
        if (Array.isArray(response)) {
          // If response is directly an array
          this.gmrData = response;
        } else if (response.data && Array.isArray(response.data)) {
          // If response has a data property that is an array
          this.gmrData = response.data;
        } else if (response.responseCode === 200 && response.data && Array.isArray(response.data)) {
          // If response has a responseCode and data property
          this.gmrData = response.data;
        } else {
          // Try to find an array in the response
          const possibleDataArrays = Object.values(response).filter(val => Array.isArray(val));
          if (possibleDataArrays.length > 0) {
            // Use the first array found
            this.gmrData = possibleDataArrays[0] as GmrMonthlyData[];
          } else {
            this.gmrData = [];
          }
        }
      } else {
        this.gmrData = [];
      }

      this.totalItems = response?.total ?? this.gmrData.length;
      console.log("Processed GMR Data:", this.gmrData);

      // If no data is returned, create sample data for testing
      if (!this.gmrData || this.gmrData.length === 0) {
        console.log("No data returned from API, creating sample data");
        this.createSampleData();
      }

      // Transform data for the new table structure
      this.transformDataForTable();

    } catch (error: any) {
      console.error("Error fetching GMR data:", error);
      this.gmrData = [];
      this.totalItems = 0;

      // Don't clear transformedData or months on error
      // This ensures the table structure remains intact

      // Create sample data for testing in case of error
      console.log("Error occurred, creating sample data");
      this.createSampleData();
      this.transformDataForTable();

      // Assuming a toast service is available
      // this.toast?.showErrorToast(error?.response?.data?.message || 'Failed to load GMR data.');
    } finally {
      this.isLoading = false;
      // Assuming ChangeDetectorRef is injected and needed
      // this.cdr.detectChanges();
    }
  }

  // Create sample data for testing when API returns no data
  createSampleData(): void {
    const sampleMonths = [1, 2, 3, 4, 5, 6];

    // Create regular monthly data
    const regularMonthData = sampleMonths.map(month => {
      const item: any = { month };

      // Add sample values for each metric in the specified order
      this.getMetrics().forEach(metric => {
        // Generate numeric sample data for better testing
        item[metric] = `${Math.floor(Math.random() * 100)}`;
      });

      return item as GmrMonthlyData;
    });

    // Create a total entry (month 13)
    const totalItem: any = { month: 13 };

    // Calculate totals for each metric from the regular month data
    this.getMetrics().forEach(metric => {
      let total = 0;
      regularMonthData.forEach(monthData => {
        const value = monthData[metric as keyof GmrMonthlyData];
        if (value && !isNaN(Number(value))) {
          total += Number(value);
        }
      });
      totalItem[metric] = total.toString();
    });

    // Combine regular month data with the total
    this.gmrData = [...regularMonthData, totalItem as GmrMonthlyData];

    console.log("Created sample data:", this.gmrData);
  }

  // Transform data for the new table structure (months as columns, metrics as rows)
  transformDataForTable(): void {
    console.log('Starting data transformation with gmrData:', this.gmrData);

    // Store current months to preserve them if needed
    const currentMonths = [...this.months];

    // Only update months if we have valid data
    if (this.gmrData && Array.isArray(this.gmrData) && this.gmrData.length > 0) {
      try {
        // Check if month property exists in the data
        const hasMonthProperty = this.gmrData.some(item => 'month' in item);

        if (hasMonthProperty) {
          // Extract months from data, but filter out month 13 (total) for regular months display
          const dataMonths = [...new Set(this.gmrData.map(item => item.month))]
            .filter(month => month !== 13) // Filter out month 13 (total) from regular months
            .sort((a, b) => a - b);

          if (dataMonths.length > 0) {
            // Only update months if we found some in the data
            this.months = dataMonths;
            console.log('Months extracted from data:', this.months);
          } else {
            // Keep current months if no new ones found
            console.log('No months found in data, keeping current months:', currentMonths);
          }
        } else {
          // Keep current months if no month property
          console.log('No month property found in data items, keeping current months');
        }
      } catch (error) {
        console.error('Error extracting months:', error);
        // Keep current months in case of error
        this.months = currentMonths;
      }
    }

    // If months array is somehow empty, use default months
    if (this.months.length === 0) {
      this.months = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
      console.log('Using default months after all checks:', this.months);
    }

    // Add a special 'total' identifier to the months array
    // Using -1 as a special value to represent the total column
    if (!this.months.includes(-1)) {
      this.months.push(-1);
    }

    // Initialize transformed data structure with all metrics
    Object.keys(this.metricLabels).forEach(metric => {
      this.transformedData[metric] = {
        label: this.metricLabels[metric],
        values: {}
      };

      // Initialize with empty values for all months
      this.months.forEach(month => {
        this.transformedData[metric].values[month] = '';
      });
    });

    // Fill in the values from gmrData if it's valid
    if (Array.isArray(this.gmrData) && this.gmrData.length > 0) {
      this.gmrData.forEach(item => {
        if (item && typeof item === 'object' && 'month' in item) {
          // Handle month 13 (total) data separately
          if (item.month === 13) {
            // Store the total values from month 13 in the special -1 index
            Object.keys(this.metricLabels).forEach(metric => {
              if (metric in item) {
                this.transformedData[metric].values[-1] = item[metric as keyof GmrMonthlyData];
              }
            });
          } else {
            // Handle regular months (1-12)
            Object.keys(this.metricLabels).forEach(metric => {
              if (metric in item) {
                this.transformedData[metric].values[item.month] = item[metric as keyof GmrMonthlyData];
              }
            });
          }
        }
      });
    }

    // If no total data was found in the API response (month 13), calculate totals
    const hasTotalData = this.gmrData.some(item => item.month === 13);
    if (!hasTotalData) {
      console.log('No total data (month 13) found in API response, calculating totals manually');

      // Calculate totals for each metric
      Object.keys(this.metricLabels).forEach(metric => {
        let total = 0;
        let hasValidValues = false;

        // Get all regular months (excluding the total column)
        const regularMonths = this.months.filter(month => month !== -1);

        regularMonths.forEach(month => {
          const value = this.transformedData[metric].values[month];
          if (value !== '' && !isNaN(Number(value))) {
            total += Number(value);
            hasValidValues = true;
          }
        });

        // Set the total value for this metric only if it wasn't already set from API data
        if (this.transformedData[metric].values[-1] === '') {
          this.transformedData[metric].values[-1] = hasValidValues ? total.toString() : '';
        }
      });
    }

    console.log('Transformed data structure:', this.transformedData);
    console.log('Final months array:', this.months);
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadGmrData();
  }

  openFilterModal(): void {
    this.isFilterModalOpen = true;
  }

  closeFilterModal(): void {
    this.isFilterModalOpen = false;
  }

  async getPlants(clusterId: number | null = null): Promise<void> {
    console.log('Fetching plants...');
    const plantParams: any = { sort: 'name,ASC', filter: ['enabled||eq||true'], limit: 1000 };

    // For plant admin, we'll filter the results after API call
    // But for super admin with cluster filter, add it to the API params
    if (!this.isPlantAdmin && clusterId !== null) {
      plantParams.filter.push(`clusterId||eq||${clusterId}`);
      console.log("Filtering plants by Cluster ID:", clusterId);
    }

    let allEnabledPlants: Plant[] = [];
    try {
      const axiosConfig = createAxiosConfig(plantParams);
      console.log("Plant API Request Params:", JSON.stringify(plantParams, null, 2));

      const response = await this.plantManagementService.getPlants(axiosConfig);
      console.log("Plant API Response:", response);

      // Handle different response structures
      if (response && typeof response === 'object') {
        if (Array.isArray(response)) {
          allEnabledPlants = response;
        } else if (response.data && Array.isArray(response.data)) {
          allEnabledPlants = response.data;
        } else {
          console.error("Unexpected response format for plants:", response);
          allEnabledPlants = [];
        }
      } else {
        console.error("Invalid response for plants:", response);
        allEnabledPlants = [];
      }

      console.log("Processed plants data:", allEnabledPlants);

      // Filter plants based on user role
      if (this.isPlantAdmin && this.loggedInPlantIds.length > 0) {
        // For plant admin, only show assigned plants
        allEnabledPlants = allEnabledPlants.filter(plant =>
          this.loggedInPlantIds.includes(plant.id)
        );

        console.log("Filtered plants for Plant Admin:", allEnabledPlants);

        // If a cluster filter is applied, further filter the plants
        if (clusterId !== null) {
          allEnabledPlants = allEnabledPlants.filter(plant =>
            plant.clusterId === clusterId
          );
          console.log("Further filtered by cluster:", allEnabledPlants);
        }

        // If no plants are available after filtering, show a message
        if (allEnabledPlants.length === 0) {
          this.toast?.showErrorToast('No plants available for the selected filters.');
        }
      }
    } catch (error) {
      console.error("Error fetching plants:", error);
      allEnabledPlants = [];
      this.toast?.showErrorToast('Failed to load plant list.');
    }

    this.availablePlants = allEnabledPlants;
    console.log(`Loaded ${this.availablePlants.length} plants.`);

    // For plant admin with only one plant, auto-select it
    if (this.isPlantAdmin && this.availablePlants.length === 1) {
      this.filters.plantId = this.availablePlants[0].id;
      console.log("Auto-selected the only available plant:", this.filters.plantId);
    } else {
      // Otherwise reset plant selection
      this.filters.plantId = null;
    }
  }

  async getClusters(): Promise<void> {
    console.log('Fetching clusters...');
    try {
      const clusterParams = { sort:'title,ASC', filter:['enabled||eq||true'], limit: 1000 };
      const axiosConfig = createAxiosConfig(clusterParams);
      const response = await this.clusterService.getCluster(axiosConfig);
      this.availableClusters = response?.data ?? response ?? [];
      console.log('Clusters loaded:', this.availableClusters);
    } catch (error) {
      console.error("Error fetching clusters:", error);
      this.availableClusters = [];
      this.toast?.showErrorToast('Failed to load cluster list.');
    }
  }

  async getCompanies(): Promise<void> {
    console.log('Fetching companies (OpCos)...');
    try {
      const companyParams = { sort:'title,ASC', filter:['enabled||eq||true'], limit: 1000 };
      const axiosConfig = createAxiosConfig(companyParams);
      const response = await this.opcoService.getOpco(axiosConfig);
      this.availableCompanies = response?.data ?? response ?? [];
      console.log('Companies loaded:', this.availableCompanies);
    } catch (error) {
      console.error("Error fetching companies:", error);
      this.availableCompanies = [];
      this.toast?.showErrorToast('Failed to load company list.');
    }
  }

  async getPlantTypes(): Promise<void> {
    console.log('Fetching plant types...');
    try {
      const plantTypeParams = { sort:'title,ASC', filter:['enabled||eq||true'], limit: 1000 };
      const axiosConfig = createAxiosConfig(plantTypeParams);
      const response = await this.plantTypeService.getPlantType(axiosConfig);
      this.availablePlantTypes = response?.data ?? response ?? [];
      console.log('Plant types loaded:', this.availablePlantTypes);
    } catch (error) {
      console.error("Error fetching plant types:", error);
      this.availablePlantTypes = [];
      this.toast?.showErrorToast('Failed to load plant type list.');
    }
  }





  // Triggered when filters are confirmed
  applyFilters(): void {
    this.currentPage = 1; // Reset to first page when applying filters
    console.log("Applying filters:", this.filters);
    this.loadGmrData();
    this.closeFilterModal(); // Close modal after applying
  }

  // Reset Filters
  resetFilters(): void {
    this.filters = {
      year: new Date().getFullYear(),
      plantId: null,
      clusterId: null,
      opcoId: null,
      plantTypeId: null,
    };

    this.currentPage = 1;
    this.loadGmrData();
    this.closeFilterModal(); // Close modal after reset
  }

  // Handle cluster selection change
  async onClusterChange(clusterId: number | null): Promise<void> {
    this.filters.clusterId = clusterId;
    console.log("Cluster selection changed to:", clusterId);

    try {
      // Show loading indicator or message if needed
      // this.isClusterLoading = true;

      // Fetch plants based on the selected cluster
      await this.getPlants(clusterId);

      // Reset selected plant when cluster changes
      this.filters.plantId = null;

      console.log("Plants updated based on cluster selection");
    } catch (error) {
      console.error("Error updating plants for selected cluster:", error);
      this.toast?.showErrorToast('Failed to update plants for the selected cluster.');
    } finally {
      // Hide loading indicator if needed
      // this.isClusterLoading = false;
    }
  }

  /**
   * Converts a month number (1-12) to its full name.
   * @param monthNumber The month number (1 for January, 12 for December).
   * @returns The full month name.
   */
  /**
   * Converts a month number (1-12) to its full name and formats it with the year.
   * Special case: if monthNumber is -1, returns "Total" as this is the total column.
   * @param monthNumber The month number (1 for January, 12 for December) or -1 for Total.
   * @param year The year.
   * @returns The formatted string "YYYY-Month Name" or "Total".
   */
  getMonthYearFormat(monthNumber: number, year: number | null): string {
    // Special case for the total column
    if (monthNumber === -1) {
      return 'Total';
    }

    if (year === null) {
      year = new Date().getFullYear(); // Default to current year if not provided
    }
    const date = new Date(year, monthNumber - 1, 1); // Month is 0-indexed
    const monthName = date.toLocaleString('en-US', { month: 'long' });
    return `${year}-${monthName}`;
  }

  // Format date for display
  formatDate(date: string | Date | undefined, format: string): string {
    if (!date) return 'N/A';

    const d = new Date(date);
    if (isNaN(d.getTime())) return 'N/A';

    if (format === 'MMM YYYY') {
      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      return `${months[d.getMonth()]} ${d.getFullYear()}`;
    }

    return d.toLocaleDateString();
  }

  // Get metrics as an array for template iteration in the specified order
  getMetrics(): string[] {
    // Return metrics in the specified order:
    // Fatality, LTI, MTC, RWC, Days lost (LTI), Modified work days, Manhours, First Aid, Property Damage, Recordable Injury, LTIFR, TIFR
    return [
      'Fatal',       // Fatality
      'RLTI',        // LTI (Reportable Lost Time Injury)
      'MTC',         // MTC (Medical Treatment Case)
      'RWC',         // RWC (Restricted Work Case)
      'MDL',         // Days lost (LTI) - MDL (Man Days Lost)
      'MWD',         // Modified work days
      'ManHoursWorked', // Manhours
      'FAC',         // First Aid
      'propertyDamage', // Property Damage
      'recordableInjury', // Recordable Injury
      'LTIFR',       // LTIFR
      'TIFR'         // TIFR
    ];
  }

  // Track by functions for ngFor to improve performance
  trackByMonth(_index: number, month: number): number {
    return month;
  }

  trackByMetric(_index: number, metric: string): string {
    return metric;
  }

  // Download Excel file
  async downloadExcel(type: 'current' | 'all'): Promise<void> {
    if (this.isDownloadingExcel) {
      return;
    }

    this.isDownloadingExcel = true;
    this.downloadType = type;
    this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current' : 'all filtered'} GMR data...`);

    try {
      let dataToExport: GmrMonthlyData[] = [];

      if (type === 'all' && this.totalItems > this.gmrData.length) {
        // For 'all' option, we need to fetch all data if there's pagination
        const allFilterQueryParams: any = { ...this.filters };
        allFilterQueryParams.limit = this.totalItems;
        allFilterQueryParams.page = 1;

        try {
          const response = await this.digisafeService.getGmrCount({ params: allFilterQueryParams });
          if (response && response.data && Array.isArray(response.data)) {
            dataToExport = response.data;
          } else if (Array.isArray(response)) {
            dataToExport = response;
          } else {
            // Try to find an array in the response
            const possibleDataArrays = Object.values(response).filter(val => Array.isArray(val));
            if (possibleDataArrays.length > 0) {
              dataToExport = possibleDataArrays[0] as GmrMonthlyData[];
            }
          }
        } catch (error) {
          console.error("Error fetching all GMR data for download:", error);
          this.toast?.showErrorToast("Failed to fetch all data. Downloading current page instead.");
          dataToExport = this.gmrData;
        }
      } else {
        // For 'current' option or if all data is already loaded
        dataToExport = this.gmrData;
      }

      if (!dataToExport || dataToExport.length === 0) {
        this.toast?.showErrorToast(`No GMR data available to download.`);
        return;
      }

      // Transform data for Excel export with months as columns and metrics as rows
      // First, extract all unique months from the data, excluding month 13 (total)
      const uniqueMonths = [...new Set(dataToExport.map(item => item.month))]
        .filter(month => month !== 13) // Filter out month 13 (total) from regular months
        .sort((a, b) => a - b);

      // Create a structure similar to the table display
      const excelData: any[] = [];

      // Add header row with month names
      const headerRow: any = { 'Metric': 'Metric' };
      uniqueMonths.forEach(month => {
        headerRow[month.toString()] = this.getMonthYearFormat(month, this.filters.year);
      });
      // Add Total column to header
      headerRow['total'] = 'Total';
      excelData.push(headerRow);

      // Add data rows for each metric in the specified order
      this.getMetrics().forEach(metric => {
        const metricRow: any = { 'Metric': this.metricLabels[metric] };
        let total = 0;
        let hasValidValues = false;

        // Add values for each month
        uniqueMonths.forEach(month => {
          // Find the data for this month
          const monthData = dataToExport.find(item => item.month === month);
          const value = monthData ? (monthData[metric as keyof GmrMonthlyData] || 'N/A') : 'N/A';
          metricRow[month.toString()] = value;

          // Calculate total if value is numeric (we'll use this if no month 13 data exists)
          if (value !== 'N/A' && !isNaN(Number(value))) {
            total += Number(value);
            hasValidValues = true;
          }
        });

        // Try to find the total from month 13 data
        const totalData = dataToExport.find(item => item.month === 13);
        if (totalData && metric in totalData) {
          // Use the total from month 13 data
          metricRow['total'] = totalData[metric as keyof GmrMonthlyData] || 'N/A';
        } else {
          // Fall back to calculated total if no month 13 data exists
          metricRow['total'] = hasValidValues ? total.toString() : 'N/A';
        }

        excelData.push(metricRow);
      });

      // Create worksheet with specific column order to ensure Metric is first
      // Define the column order with Metric first, then months, then total
      const columnOrder = ['Metric', ...uniqueMonths.map(month => month.toString()), 'total'];

      // Create worksheet with the specified column order
      const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(excelData, { header: columnOrder });
      const wb: XLSX.WorkBook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'GMR Dashboard');

      // Auto-size columns (optional enhancement)
      const colWidths = [];
      for (let i = 0; i < Object.keys(excelData[0] || {}).length; i++) {
        colWidths.push({ wch: 20 }); // Set default width
      }
      ws['!cols'] = colWidths;

      // Generate filename with date
      const dateStr = new Date().toISOString().slice(0, 10);
      const yearStr = this.filters.year ? `_${this.filters.year}` : '';
      const plantStr = this.filters.plantId ? `_Plant${this.filters.plantId}` : '';
      const fileName = `GMR_Dashboard${yearStr}${plantStr}_${dateStr}.xlsx`;

      // Write file and download
      XLSX.writeFile(wb, fileName);
      this.toast?.showSuccessToast(`Excel file download started.`);
    } catch (error) {
      console.error(`Error generating Excel file:`, error);
      this.toast?.showErrorToast(`An error occurred while generating the Excel file.`);
    } finally {
      this.isDownloadingExcel = false;
      this.downloadType = null;
    }
  }


}