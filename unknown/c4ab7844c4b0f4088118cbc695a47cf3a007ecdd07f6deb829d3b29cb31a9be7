import { CommonModule } from '@angular/common';
import { Component, OnDestroy, OnInit, ViewChild, inject } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms'; // Removed Validators unless needed
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

// Import Services using inject
import { ReportManagementService } from '../../../../services/report-management/report-management.service';
import { PlantManagementService } from '../../../../services/plant-management/plant-management.service';
import { ClusterService } from '../../../../services/master-management/cluster/cluster.service';

// Import Shared Components
import { PaginationComponent } from "../../../../shared/pagination/pagination.component";
import { OffcanvasComponent } from "../../../../shared/offcanvas/offcanvas.component";

// Import NgSelectModule
import { NgSelectModule } from '@ng-select/ng-select';

// Import Utilities (if needed for filter fetching)
import { createAxiosConfig } from '../../../../core/utilities/axios-param-config';
import { ToastMessageComponent } from '../../../../shared/toast-message/toast-message.component'; // Adjust path if needed
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap'; // Correct import for dropdown
import * as XLSX from 'xlsx';

// Define ROLES constant
const ROLES = {
    SUPER_ADMIN: 'super_admin',
    PLANT_ADMIN: 'plant_admin',
};

// Interface for Report Item structure (Update based on actual API response)
interface PlantwiseReportItem {
  plantId: number;
  name: string;
  zoneArea: string;
  noOfTimesCovered: number;
}

// Interface for Plant dropdown options
interface Plant {
  id: number;
  name: string;
  clusterId?: number;
}

// Interface for Cluster dropdown options
interface Cluster {
  id: number;
  title: string; // Assuming 'name', adjust if 'title'
}


@Component({
  selector: 'app-plantwise-report', // Keep your selector
  standalone: true,
  imports: [
    CommonModule,
    PaginationComponent,
    OffcanvasComponent,
    ReactiveFormsModule, // For filter form
    NgSelectModule,
    NgbDropdownModule,
    ToastMessageComponent
  ],
  templateUrl: './plantwise-report.component.html',
  styleUrl: './plantwise-report.component.scss'
})
export class PlantwiseReportComponent implements OnInit, OnDestroy {
  @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;

  private readonly reportService = inject(ReportManagementService);
  private readonly fb = inject(FormBuilder);
  private readonly plantService = inject(PlantManagementService);
  private readonly clusterService = inject(ClusterService);

  private destroy$ = new Subject<void>();

  isFilterModalOpen = false;
  isLoadingReport = false;
  isLoadingClusters = false;
  isLoadingPlants = false;
  errorMessage: string | null = null;

  list: PlantwiseReportItem[] = [];
  currentPage = 1;
  itemsPerPage = 20;
  totalItems = 0;
  isDownloadingExcel = false;
  downloadType: 'current' | 'all' | null = null;

  // Role-Based Access Control Properties
  currentUserRole: string = '';
  loggedInAdminId: number | null = null;
  loggedInPlantIds: number[] = [];

  filterData = {
    startDate: null as string | null,
    endDate: null as string | null,
    plantIds: [] as number[], // Use array for applied filter
    clusterId: null as number | null // Keep single ID for applied cluster
  };

  filterForm!: FormGroup;

  clusterOptions: Cluster[] = []; // Use Cluster interface
  plantOptions: Plant[] = []; // Use Plant interface

  constructor() {
    this.buildFilterForm();
  }

  ngOnInit(): void {
    this.setCurrentUserRoleAndDetailsById(); // Set role first
    this.loadInitialFilters();
    this.subscribeToFilterChanges();
    this.getReport(this.currentPage); // Initial load applies role filter
  }

   private setCurrentUserRoleAndDetailsById(): void {
        try {
            const userString = localStorage.getItem('user');
            if (!userString || userString.trim() === "" || ['null', 'undefined', '""', '" "'].includes(userString.trim().toLowerCase())) {
                this.currentUserRole = ''; this.loggedInAdminId = null; this.loggedInPlantIds = [];
                this.toast?.showErrorToast("User session invalid."); return;
            }
            const currentUser = JSON.parse(userString);
            this.loggedInAdminId = currentUser?.id ?? null;
            this.loggedInPlantIds = (Array.isArray(currentUser?.plantIds) && currentUser.plantIds.length > 0)
                ? currentUser.plantIds.map((id: any) => Number(id)).filter((id: number) => !isNaN(id)) : [];
            const roleId = currentUser?.adminsRoleId;
            if (roleId === 1) { this.currentUserRole = ROLES.SUPER_ADMIN; }
            else if (roleId === 2) {
                this.currentUserRole = ROLES.PLANT_ADMIN;
                if (this.loggedInPlantIds.length === 0) { this.toast?.showErrorToast("Plant Admin has no assigned plants."); }
            } else { this.currentUserRole = ''; this.toast?.showErrorToast("Invalid user role."); }
            console.log(`Role: ${this.currentUserRole}, UserID: ${this.loggedInAdminId}, Plants: [${this.loggedInPlantIds.join(', ')}]`);
        } catch (error) {
            console.error("Error parsing user data:", error);
            this.currentUserRole = ''; this.loggedInAdminId = null; this.loggedInPlantIds = [];
            this.toast?.showErrorToast("Error reading user session.");
        }
    }

  getCurrentListData(): PlantwiseReportItem[] | undefined {
    return this.list;
  }

  async fetchAllFilteredPlantZoneReports(): Promise<PlantwiseReportItem[] | null> {
    let plantIdsToSend: number[] | null = null;

    // Determine plant IDs based on role and *applied* filter
    if (this.currentUserRole === ROLES.PLANT_ADMIN) {
        if (this.loggedInPlantIds.length > 0) {
            if (this.filterData.plantIds.length > 0) {
                 plantIdsToSend = this.filterData.plantIds.filter(id => this.loggedInPlantIds.includes(id));
                 if(plantIdsToSend.length === 0) { plantIdsToSend = this.loggedInPlantIds; }
            } else { plantIdsToSend = this.loggedInPlantIds; }
        } else { return []; }
    } else if (this.currentUserRole === ROLES.SUPER_ADMIN) {
        plantIdsToSend = this.filterData.plantIds.length > 0 ? this.filterData.plantIds : null;
    } else { return null; }

    const payload: { [key: string]: any } = {
        pageSize: 1000000,
        pageIndex: 1,
    };
    if (this.filterData.startDate) payload['startDate'] = this.filterData.startDate;
    if (this.filterData.endDate) payload['endDate'] = this.filterData.endDate;
    if (plantIdsToSend) payload['plantIds'] = plantIdsToSend; // Send as array
    // Cluster ID is usually not needed for the report itself, only for filtering plants
    // if (this.filterData.clusterId) payload['clusterId'] = this.filterData.clusterId;

    console.log('Request Payload for ALL Plantwise Zone Report Data:', payload);
    this.isLoadingReport = true;
    try {
        const res = await this.reportService.zonePlantWiseReport(payload);
        return res?.data ?? [];
    } catch (error) {
        console.error("Error fetching all plantwise zone reports for download:", error);
        this.toast?.showErrorToast("Failed to retrieve full data for download.");
        return null;
    } finally {
        this.isLoadingReport = false;
    }
  }

  async downloadExcel(type: 'current' | 'all') {
    if (this.isDownloadingExcel || this.isLoadingReport) return;

    this.isDownloadingExcel = true;
    this.downloadType = type;
    this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} reports...`);

    let dataToExport: PlantwiseReportItem[] | null = null;

    try {
        if (type === 'all') { dataToExport = await this.fetchAllFilteredPlantZoneReports(); }
        else { dataToExport = this.getCurrentListData() ?? null; if (dataToExport === undefined) { dataToExport = null; } }

        if (dataToExport === null) { this.toast?.showErrorToast("Failed to retrieve data for download."); return; }
        if (!dataToExport || dataToExport.length === 0) { this.toast?.showErrorToast(`No reports available to download.`); return; }

        console.log(`Fetched ${dataToExport.length} reports for Excel export (${type}).`);

        const dataForExcel = dataToExport.map(item => ({
            'Plant': item.name, 'Zone Area': item.zoneArea, 'Total Scans': item.noOfTimesCovered,
        }));

        const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
        const wb: XLSX.WorkBook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'PlantwiseZoneReport');

        const dateStr = new Date().toISOString().slice(0, 10);
        const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
        const fileName = `PlantwiseZoneReport_${typeStr}_${dateStr}.xlsx`;

        XLSX.writeFile(wb, fileName);
        this.toast?.showSuccessToast(`Excel file download started (${type}).`);

    } catch (error) {
        console.error(`Error generating Excel file (${type}):`, error);
        this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
    } finally {
        this.isDownloadingExcel = false; this.downloadType = null;
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  buildFilterForm(): void {
    this.filterForm = this.fb.group({
      startDate: [this.filterData.startDate],
      endDate: [this.filterData.endDate],
      plantIds: [this.filterData.plantIds || []],
      clusterId: [this.filterData.clusterId]
    });
  }

  async loadInitialFilters() {
    await this.getClusters();
    // Load plants based on role and potentially pre-selected cluster
    await this.getPlants(this.filterData.clusterId);
    // Patch form after options are loaded
    this.filterForm.patchValue({
        clusterId: this.filterData.clusterId,
        plantIds: this.filterData.plantIds || []
    }, { emitEvent: false });
  }

  async getClusters() {
    this.isLoadingClusters = true;
    this.clusterOptions = [];
    const data = { sort: 'title,ASC', filter: ['enabled||eq||true'] };
    const params = createAxiosConfig ? createAxiosConfig(data) : data;

    try {
      const response: any = await this.clusterService.getCluster(params);
      let clusters: any[] = [];
      if (response && Array.isArray(response.data)) { clusters = response.data; }
      else if (Array.isArray(response)) { clusters = response; }
      // Map to Cluster interface, assuming 'title' is the name field
      this.clusterOptions = clusters.map(c => ({ id: c.id, title: c.title }));
    } catch (error) {
      console.error("Error fetching clusters:", error);
    } finally {
      this.isLoadingClusters = false;
    }
  }

  async getPlants(clusterId: number | string | null) {
    this.isLoadingPlants = true;
    this.plantOptions = [];

    const plantFilters = ['isDeleted||eq||false', 'enabled||eq||true'];
    if (clusterId) { plantFilters.push(`clusterId||eq||${clusterId}`); }

    // Apply role-based filtering for the dropdown options
    if (this.currentUserRole === ROLES.PLANT_ADMIN && this.loggedInPlantIds.length > 0) {
        plantFilters.push(`id||$in||${this.loggedInPlantIds.join(',')}`);
    } else if (this.currentUserRole === ROLES.PLANT_ADMIN && this.loggedInPlantIds.length === 0) {
        this.isLoadingPlants = false; return; // No plants to show
    }
    // Super Admin sees all plants (optionally filtered by cluster)

    const data = { sort: 'name,ASC', filter: plantFilters, limit: 1000 }; // Added limit
    const params = createAxiosConfig ? createAxiosConfig(data) : data;

    try {
      const response: any = await this.plantService.getPlants(params);
      let plants: any[] = [];
       if (response && Array.isArray(response.data)) { plants = response.data; }
       else if (Array.isArray(response)) { plants = response; }
       this.plantOptions = plants.map(plant => ({ id: plant.id, name: plant.name }));
    } catch (error) {
      console.error(`Error fetching plants for cluster ${clusterId}:`, error);
    } finally {
      this.isLoadingPlants = false;
    }
  }

  subscribeToFilterChanges(): void {
    this.filterForm.get('clusterId')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(selectedClusterId => {
        console.log('Cluster changed:', selectedClusterId);
        this.filterForm.get('plantIds')?.reset([], { emitEvent: false });
        this.getPlants(selectedClusterId);
      });
  }

  async getReport(pageIndex: number) {
    if (this.isLoadingReport) return;
    this.isLoadingReport = true;
    this.errorMessage = null;
    this.list = []; // Clear list

    let plantIdsToSend: number[] | null = null;

    // Determine plant IDs based on role and *applied* filter data
    if (this.currentUserRole === ROLES.PLANT_ADMIN) {
        if (this.loggedInPlantIds.length > 0) {
            if (this.filterData.plantIds.length > 0) {
                 plantIdsToSend = this.filterData.plantIds.filter(id => this.loggedInPlantIds.includes(id));
                 if(plantIdsToSend.length === 0) { plantIdsToSend = this.loggedInPlantIds; }
            } else { plantIdsToSend = this.loggedInPlantIds; }
        } else {
            console.warn("Plant Admin has no plants, skipping report fetch.");
            this.totalItems = 0; this.isLoadingReport = false; return;
        }
    } else if (this.currentUserRole === ROLES.SUPER_ADMIN) {
        plantIdsToSend = this.filterData.plantIds.length > 0 ? this.filterData.plantIds : null;
    } else {
         console.error("Unknown user role, cannot fetch report.");
         this.totalItems = 0; this.isLoadingReport = false; return;
    }

    const payload: { [key: string]: any } = {
      startDate: this.filterData.startDate,
      endDate: this.filterData.endDate,
      plantIds: plantIdsToSend, // Use determined IDs
      // clusterId is not sent to this report endpoint
      pageSize: this.itemsPerPage,
      pageIndex: pageIndex
    };
    const cleanedPayload: { [key: string]: any } = {};
    for (const key in payload) {
        const value = payload[key];
        if (value !== null && value !== undefined) {
            if (Array.isArray(value)) { if (value.length > 0) { cleanedPayload[key] = value; } }
            else if (typeof value === 'string' && value.trim() === '') { /* Skip */ }
            else { cleanedPayload[key] = value; }
        }
    }

    console.log('Sending Plant-wise Zone API Payload:', cleanedPayload);

    try {
      const res = await this.reportService.zonePlantWiseReport(cleanedPayload);
      this.list = res?.data || [];
      this.totalItems = res?.total || 0;
    } catch (error: any) {
      console.error("Error fetching plant-wise zone report:", error);
      this.errorMessage = `Failed to load report: ${error.message || 'Server error'}`;
      this.list = []; this.totalItems = 0;
      this.toast?.showErrorToast(this.errorMessage);
    } finally {
      this.isLoadingReport = false;
    }
  }

  openFilterModal(): void {
    this.filterForm.patchValue(this.filterData, { emitEvent: false });
    this.getPlants(this.filterData.clusterId).then(() => {
        this.filterForm.patchValue({ plantIds: this.filterData.plantIds || [] }, { emitEvent: false });
        this.isFilterModalOpen = true;
    });
  }

  closeModal(): void { this.isFilterModalOpen = false; }

  applyFilters(): void {
    if (this.filterForm.invalid) { this.filterForm.markAllAsTouched(); return; }
    const formValues = this.filterForm.value;
    this.filterData = {
      startDate: formValues.startDate || null,
      endDate: formValues.endDate || null,
      plantIds: Array.isArray(formValues.plantIds) ? formValues.plantIds : [],
      clusterId: formValues.clusterId || null
    };
    console.log('Applied filters:', this.filterData);
    this.currentPage = 1;
    this.getReport(this.currentPage);
    this.closeModal();
  }

  onPageChange(page: number): void {
    if (this.currentPage !== page) {
      this.currentPage = page;
      this.getReport(this.currentPage);
    }
  }

  calculatePlantPlaceholder(): string {
    if (this.isLoadingPlants) { return 'Loading plants...'; }
    if (!this.filterForm.get('clusterId')?.value) { return 'Select a cluster first'; }
    return 'Select Plant(s)';
  }

  calculateNotFoundText(type: 'cluster' | 'plant'): string {
      if (type === 'cluster' && !this.isLoadingClusters && this.clusterOptions.length === 0) { return "No clusters found"; }
      if (type === 'plant' && !this.isLoadingPlants && this.filterForm.get('clusterId')?.value && this.plantOptions.length === 0) { return "No plants found for selected cluster"; }
      return '';
   }
}