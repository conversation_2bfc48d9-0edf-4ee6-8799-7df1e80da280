import { CommonModule } from '@angular/common';
import { Component, OnDestroy, OnInit, ViewChild, inject } from '@angular/core';
import { ReportManagementService } from '../../../../services/report-management/report-management.service';
import { PaginationComponent } from "../../../../shared/pagination/pagination.component";
import { OffcanvasComponent } from "../../../../shared/offcanvas/offcanvas.component";
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms'; // Keep ReactiveFormsModule
import { PlantManagementService } from '../../../../services/plant-management/plant-management.service';
import { ClusterService } from '../../../../services/master-management/cluster/cluster.service';
import { createAxiosConfig } from '../../../../core/utilities/axios-param-config';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ToastMessageComponent } from '../../../../shared/toast-message/toast-message.component'; // Adjust path
import * as XLSX from 'xlsx';

// Import NgSelectModule
import { NgSelectModule } from '@ng-select/ng-select';
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap';

// Define ROLES constant
const ROLES = {
    SUPER_ADMIN: 'super_admin',
    PLANT_ADMIN: 'plant_admin',
};

// Interface for clearer option structure (optional but recommended)
interface SelectOption {
  id: number | string;
  name: string;
}

// Interface for Report Item structure (Update based on actual API response)
interface ZoneUserReportItem {
  adminId: number;
  firstName: string;
  lastName: string;
  email: string;
  contactNumber: string;
  name: string; // Plant Name
  zoneArea: string; // Zone Area Name
  noOfTimesCovered: number;
  plantId?: number; // Added for potential use
}

// Interface for Plant dropdown options
interface Plant {
  id: number;
  name: string;
  clusterId?: number;
}

// Interface for Cluster dropdown options
interface Cluster {
  id: number;
  title: string; // Assuming 'name', adjust if 'title'
}


@Component({
  selector: 'app-userwise-report', // Renamed component selector
  standalone: true,
  imports: [
    CommonModule,
    PaginationComponent,
    OffcanvasComponent,
    ReactiveFormsModule, // Keep ReactiveFormsModule
    NgbDropdownModule,
    ToastMessageComponent,
    NgSelectModule
  ],
  templateUrl: './userwise-report.component.html', // Renamed file
  styleUrl: './userwise-report.component.scss'    // Renamed file
})
export class UserwiseReportComponent implements OnInit, OnDestroy { // Renamed class
  @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;

  private readonly reportService = inject(ReportManagementService);
  private readonly fb = inject(FormBuilder);
  private readonly plantService = inject(PlantManagementService);
  private readonly clusterService = inject(ClusterService);

  private destroy$ = new Subject<void>();

  isFilterModalOpen = false;
  isLoadingClusters = false;
  isLoadingPlants = false;
  list:ZoneUserReportItem[] = []; // Use specific type
  currentPage = 1;
  itemsPerPage = 10; // Adjusted page size
  totalItems = 0;
  isDownloadingExcel = false;
  listLoading = false; // Added loading state for report list
  downloadType: 'current' | 'all' | null = null;

  // Role-Based Access Control Properties
  currentUserRole: string = '';
  loggedInAdminId: number | null = null;
  loggedInPlantIds: number[] = [];

  // Holds the *applied* filter values
  filterData = {
    startDate: null as string | null,
    endDate: null as string | null,
    plantIds: [] as number[], // Use array for applied filter
    clusterId: null as number | null // Keep single ID for applied cluster
  };

  filterForm!: FormGroup;

  clusterOptions: Cluster[] = []; // Use Cluster interface
  plantOptions: Plant[] = []; // Use Plant interface
  errorMessage: string | null = null;

   constructor() {
     this.buildFilterForm();
   }

   ngOnInit() {
     this.setCurrentUserRoleAndDetailsById(); // Set role first
     this.loadInitialFilters();
     this.subscribeToClusterChanges();
     this.getReport(this.currentPage); // Initial load applies role filter
   }

   ngOnDestroy(): void {
     this.destroy$.next();
     this.destroy$.complete();
   }

   private setCurrentUserRoleAndDetailsById(): void {
        try {
            const userString = localStorage.getItem('user');
            if (!userString || userString.trim() === "" || ['null', 'undefined', '""', '" "'].includes(userString.trim().toLowerCase())) {
                this.currentUserRole = ''; this.loggedInAdminId = null; this.loggedInPlantIds = [];
                this.toast?.showErrorToast("User session invalid."); return;
            }
            const currentUser = JSON.parse(userString);
            this.loggedInAdminId = currentUser?.id ?? null;
            this.loggedInPlantIds = (Array.isArray(currentUser?.plantIds) && currentUser.plantIds.length > 0)
                ? currentUser.plantIds.map((id: any) => Number(id)).filter((id: number) => !isNaN(id)) : [];
            const roleId = currentUser?.adminsRoleId;
            if (roleId === 1) { this.currentUserRole = ROLES.SUPER_ADMIN; }
            else if (roleId === 2) {
                this.currentUserRole = ROLES.PLANT_ADMIN;
                if (this.loggedInPlantIds.length === 0) { this.toast?.showErrorToast("Plant Admin has no assigned plants."); }
            } else { this.currentUserRole = ''; this.toast?.showErrorToast("Invalid user role."); }
            console.log(`Role: ${this.currentUserRole}, UserID: ${this.loggedInAdminId}, Plants: [${this.loggedInPlantIds.join(', ')}]`);
        } catch (error) {
            console.error("Error parsing user data:", error);
            this.currentUserRole = ''; this.loggedInAdminId = null; this.loggedInPlantIds = [];
            this.toast?.showErrorToast("Error reading user session.");
        }
    }

   getCurrentListData(): ZoneUserReportItem[] | undefined {
    return this.list;
   }

    async fetchAllFilteredZoneUserReports(): Promise<ZoneUserReportItem[] | null> {
        let plantIdsToSend: number[] | null = null;

        // Determine plant IDs based on role and *applied* filter data
        if (this.currentUserRole === ROLES.PLANT_ADMIN) {
            if (this.loggedInPlantIds.length > 0) {
                if (this.filterData.plantIds.length > 0) {
                     plantIdsToSend = this.filterData.plantIds.filter(id => this.loggedInPlantIds.includes(id));
                     if(plantIdsToSend.length === 0) { plantIdsToSend = this.loggedInPlantIds; }
                } else { plantIdsToSend = this.loggedInPlantIds; }
            } else { return []; }
        } else if (this.currentUserRole === ROLES.SUPER_ADMIN) {
            plantIdsToSend = this.filterData.plantIds.length > 0 ? this.filterData.plantIds : null;
        } else { return null; }

        const payload: { [key: string]: any } = {
            pageSize: 100000,
            pageIndex: 1,
        };
        if (this.filterData.startDate) payload['startDate'] = this.filterData.startDate;
        if (this.filterData.endDate) payload['endDate'] = this.filterData.endDate;
        if (plantIdsToSend) payload['plantIds'] = plantIdsToSend;
        // if (this.filterData.clusterId) payload['clusterId'] = this.filterData.clusterId; // Cluster usually not needed for report API

        console.log('Request Payload for ALL Zone User Report Data:', payload);
        this.listLoading = true; // Indicate loading
        try {
            const res = await this.reportService.zoneUserWiseReport(payload);
            return res?.data ?? [];
        } catch (error) {
            console.error("Error fetching all zone user reports for download:", error);
            this.toast?.showErrorToast("Failed to retrieve full data for download.");
            return null;
        } finally {
            this.listLoading = false; // Stop loading
        }
    }

    async downloadExcel(type: 'current' | 'all') {
        if (this.isDownloadingExcel || this.listLoading) return;

        this.isDownloadingExcel = true;
        this.downloadType = type;
        this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} reports...`);

        let dataToExport: ZoneUserReportItem[] | null = null;

        try {
            if (type === 'all') { dataToExport = await this.fetchAllFilteredZoneUserReports(); }
            else { dataToExport = this.getCurrentListData() ?? null; if (dataToExport === undefined) { dataToExport = null; } }

            if (dataToExport === null) { this.toast?.showErrorToast("Failed to retrieve data for download."); return; }
            if (!dataToExport || dataToExport.length === 0) { this.toast?.showErrorToast(`No reports available to download.`); return; }

            console.log(`Fetched ${dataToExport.length} reports for Excel export (${type}).`);

            const dataForExcel = dataToExport.map(item => ({
                'User ID': item.adminId, 'User Name': `${item.firstName ?? ''} ${item.lastName ?? ''}`.trim(),
                'Email': item.email, 'Contact': item.contactNumber, 'Plant Name': item.name, 'Zone Area': item.zoneArea,
                'Times Scanned': item.noOfTimesCovered,
            }));

            const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
            const wb: XLSX.WorkBook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'ZoneUserReport');

            const dateStr = new Date().toISOString().slice(0, 10);
            const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
            const fileName = `ZoneUserReport_${typeStr}_${dateStr}.xlsx`;

            XLSX.writeFile(wb, fileName);
            this.toast?.showSuccessToast(`Excel file download started (${type}).`);

        } catch (error) {
            console.error(`Error generating Excel file (${type}):`, error);
            this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
        } finally {
            this.isDownloadingExcel = false; this.downloadType = null;
        }
    }

    async loadInitialFilters() {
     await this.getClusters();
     // Load plants based on role and potentially pre-selected cluster
     await this.getPlants(this.filterData.clusterId);
     this.filterForm.patchValue({
         clusterId: this.filterData.clusterId,
         plantIds: this.filterData.plantIds || []
     }, { emitEvent: false });
   }

   async getClusters() {
     this.isLoadingClusters = true;
     this.clusterOptions = [];
     const data = { sort: 'title,ASC', filter: ['enabled||eq||true'] }; // Sort by name
     const params = createAxiosConfig ? createAxiosConfig(data) : data;

     try {
       const response: any = await this.clusterService.getCluster(params);
       let clusters: any[] = [];
       if (response && Array.isArray(response.data)) { clusters = response.data; }
       else if (Array.isArray(response)) { clusters = response; }
       else { console.warn('Unexpected response format for clusters:', response); }
       // Map using 'name' to match Cluster interface
       this.clusterOptions = clusters.map(c => ({ id: c.id, title: c.title })); // Use c.name
     } catch (error) {
       console.error("Error fetching clusters:", error);
       this.toast?.showErrorToast("Failed to load clusters.");
     } finally {
       this.isLoadingClusters = false;
     }
   }

   async getPlants(clusterId: number | string | null) {
     this.isLoadingPlants = true;
     this.plantOptions = [];

     const plantFilters = ['isDeleted||eq||false', 'enabled||eq||true'];
     if (clusterId) { plantFilters.push(`clusterId||eq||${clusterId}`); }

     // Apply role-based filtering for the dropdown options
     if (this.currentUserRole === ROLES.PLANT_ADMIN && this.loggedInPlantIds.length > 0) {
         plantFilters.push(`id||$in||${this.loggedInPlantIds.join(',')}`);
     } else if (this.currentUserRole === ROLES.PLANT_ADMIN && this.loggedInPlantIds.length === 0) {
         this.isLoadingPlants = false; return; // No plants to show
     }
     // Super Admin sees all plants (optionally filtered by cluster)

     const data = { sort: 'name,ASC', filter: plantFilters, limit: 1000 };
     const params = createAxiosConfig ? createAxiosConfig(data) : data;

     try {
       const response: any = await this.plantService.getPlants(params);
       let plants: any[] = [];
        if (response && Array.isArray(response.data)) { plants = response.data; }
        else if (Array.isArray(response)) { plants = response; }
        this.plantOptions = plants.map(plant => ({ id: plant.id, name: plant.name }));
     } catch (error) {
       console.error(`Error fetching plants for cluster ${clusterId}:`, error);
       this.toast?.showErrorToast("Failed to load plants for filter.");
     } finally {
       this.isLoadingPlants = false;
     }
   }

    buildFilterForm(): void {
     this.filterForm = this.fb.group({
       startDate: [this.filterData.startDate, Validators.required],
       endDate: [this.filterData.endDate, Validators.required],
       plantIds: [this.filterData.plantIds || []],
       clusterId: [this.filterData.clusterId]
     });
   }

   subscribeToClusterChanges(): void {
     this.filterForm.get('clusterId')?.valueChanges
       .pipe(takeUntil(this.destroy$))
       .subscribe(async selectedClusterId => {
         console.log('Cluster changed:', selectedClusterId);
         this.filterForm.get('plantIds')?.reset([], { emitEvent: false });
         await this.getPlants(selectedClusterId); // Fetch plants for the new cluster (respects role)
       });
   }

   calculatePlantPlaceholder(): string {
     if (this.isLoadingPlants) { return 'Loading plants...'; }
     if (!this.filterForm.get('clusterId')?.value && this.currentUserRole === ROLES.SUPER_ADMIN) { return 'Select a cluster first'; }
     if (this.plantOptions.length === 0 && (this.filterForm.get('clusterId')?.value || this.currentUserRole === ROLES.PLANT_ADMIN)) { return 'No plants available'; }
     return 'Select Plant(s)';
   }

   calculateNotFoundText(): string {
      if (!this.isLoadingPlants && this.filterForm.get('clusterId')?.value && this.plantOptions.length === 0) { return "No plants found for selected cluster"; }
      return '';
   }

   closeModal() { this.isFilterModalOpen = false; }

   openFilterModal() {
     this.filterForm.patchValue(this.filterData, { emitEvent: false });
     this.getPlants(this.filterData.clusterId).then(() => {
         // Ensure selected plants are still valid within the reloaded options
         const currentPlantIds = this.filterForm.get('plantIds')?.value || [];
         const validPlantIds = this.plantOptions.map(p => p.id);
         const validSelectedPlantIds = currentPlantIds.filter((id: number) => validPlantIds.includes(id));
         this.filterForm.patchValue({ plantIds: validSelectedPlantIds }, { emitEvent: false });
         this.isFilterModalOpen = true;
     });
   }

   applyFilters(): void {
    if (this.listLoading) return;
     // Add validation check for endDate not before startDate
     if (this.filterForm.invalid || (this.filterForm.value.endDate && this.filterForm.value.startDate && this.filterForm.value.endDate < this.filterForm.value.startDate)) {
         this.filterForm.markAllAsTouched();
         // Optionally show a toast message for date range error
         if (this.filterForm.value.endDate && this.filterForm.value.startDate && this.filterForm.value.endDate < this.filterForm.value.startDate) {
             this.toast?.showErrorToast("To Date cannot be before From Date.");
         }
         return;
     }
      const formValues = this.filterForm.value;
      this.filterData = {
        startDate: formValues.startDate || null,
        endDate: formValues.endDate || null,
        plantIds: Array.isArray(formValues.plantIds) ? formValues.plantIds : [],
        clusterId: formValues.clusterId || null
      };
     console.log('Applied filters:', this.filterData);
     this.currentPage = 1;
     this.getReport(this.currentPage);
     this.closeModal();
   }

   async getReport(pageIndex: number) {
    if (this.listLoading) return;
    this.listLoading = true;
    this.errorMessage = null;
    this.list = [];

    let plantIdsToSend: number[] | null = null;

    // Determine plant IDs based on role and *applied* filter data
    if (this.currentUserRole === ROLES.PLANT_ADMIN) {
        if (this.loggedInPlantIds.length > 0) {
            if (this.filterData.plantIds.length > 0) {
                 plantIdsToSend = this.filterData.plantIds.filter(id => this.loggedInPlantIds.includes(id));
                 if(plantIdsToSend.length === 0) { plantIdsToSend = this.loggedInPlantIds; }
            } else { plantIdsToSend = this.loggedInPlantIds; }
        } else {
            console.warn("Plant Admin has no plants, skipping report fetch.");
            this.totalItems = 0; this.listLoading = false; return;
        }
    } else if (this.currentUserRole === ROLES.SUPER_ADMIN) {
        plantIdsToSend = this.filterData.plantIds.length > 0 ? this.filterData.plantIds : null;
    } else {
         console.error("Unknown user role, cannot fetch report.");
         this.totalItems = 0; this.listLoading = false; return;
    }

    const payload: { [key: string]: any } = {
      startDate: this.filterData.startDate,
      endDate: this.filterData.endDate,
      plantIds: plantIdsToSend, // Use determined IDs
      // clusterId is not sent to this report endpoint
      pageSize: this.itemsPerPage,
      pageIndex: pageIndex
    };
    const cleanedPayload: { [key: string]: any } = {};
    for (const key in payload) {
        const value = payload[key];
        if (value !== null && value !== undefined) {
            if (Array.isArray(value)) { if (value.length > 0) { cleanedPayload[key] = value; } }
            else if (typeof value === 'string' && value.trim() === '') { /* Skip */ }
            else { cleanedPayload[key] = value; }
        }
    }

    console.log('Sending Zone User Wise API Payload:', cleanedPayload);

    try {
      const res = await this.reportService.zoneUserWiseReport(cleanedPayload); // Use correct service method
      this.list = res?.data || [];
      this.totalItems = res?.total || 0;
    } catch (error: any) {
      console.error("Error fetching zone user wise report:", error);
      this.errorMessage = `Failed to load report: ${error.message || 'Server error'}`;
      this.list = []; this.totalItems = 0;
      this.toast?.showErrorToast(this.errorMessage);
    } finally {
      this.listLoading = false;
    }
  }

   onPageChange(page: number) {
    if (this.currentPage === page || this.listLoading) return;
     this.currentPage = page;
     this.getReport(this.currentPage);
   }
}