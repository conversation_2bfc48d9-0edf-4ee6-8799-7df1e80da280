import { Component, OnInit, ViewChild } from '@angular/core'; // Added ViewChild
import { FormsModule, NgForm } from '@angular/forms'; // Added FormsModule, NgForm
import { RootCauseService } from '../../../services/master-management/root-cause/root-cause.service';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { CommonModule } from '@angular/common';
import { PaginationComponent } from "../../../shared/pagination/pagination.component";
import { OffcanvasComponent } from "../../../shared/offcanvas/offcanvas.component";
import { SwitchComponent } from "../../../shared/switch/switch.component";
import { UpdateService } from '../../../services/update/update.service';
// Optional: Import ToastrService or similar
// import { ToastrService } from 'ngx-toastr';
import { ToastMessageComponent } from '../../../shared/toast-message/toast-message.component'; // Adjust path if needed
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap'; // For dropdown button
import * as XLSX from 'xlsx'; // For Excel generation


// *** NEW: Interface for Filter structure ***
interface RootCauseFilter {
    name?: string | null;
    enabled?: string | null;
    sortField?: string | null;
    sortDirection?: 'ASC' | 'DESC';
}

// *** NEW: Interface for Root Cause Data ***
export interface RootCause {
    id: number;
    title: string;
    enabled: boolean;
    createdAt?: string; // Optional
    updatedAt?: string; // Optional
}

// *** NEW: Interface for Create Form Data ***
interface NewRootCauseData {
    title: string;
    enabled: boolean;
}

@Component({
    selector: 'app-root-cause',
    standalone: true,
    // Added FormsModule
    imports: [CommonModule,
        FormsModule,
        PaginationComponent,
        OffcanvasComponent,
        NgbDropdownModule,
        ToastMessageComponent,
        SwitchComponent],
    templateUrl: './root-cause.component.html',
    styleUrl: './root-cause.component.scss'
})
export class RootCauseComponent implements OnInit {
    // Access the form template variables
    @ViewChild('createForm') createForm?: NgForm;
    @ViewChild('editForm') editForm?: NgForm;
    @ViewChild('filterForm') filterForm?: NgForm;
    @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;

    // --- Modal States ---
    isFilterModalOpen = false; // Renamed from isEditUserModalOpen
    isEditModalOpen = false;   // <-- New state for edit modal
    isCreateModalOpen = false; // <-- New state for create modal

    // --- Data & Loading States ---
    rootCauseList: RootCause[] = []; // Use RootCause interface
    listLoading = false; // Added loading state
    editLoading = false;     // <-- New state for edit form submission
    createLoading = false;   // <-- New state for create form submission

    // --- Pagination ---
    currentPage = 1;
    itemsPerPage = 10;
    totalItems = 0;
    isDownloadingExcel = false;
    downloadType: 'current' | 'all' | null = null;

    // --- Filtering ---
    filters: RootCauseFilter = { // Added filters object
        name: null,
        enabled: 'true', // Default based on original getRootCauseList call
        sortField: 'title', // Default based on original getRootCauseList call
        sortDirection: 'ASC' // Default based on original getRootCauseList call
    };
    availableSortFields = [ // Added sort fields for dropdown
        { value: 'id', label: 'ID' },
        { value: 'title', label: 'Root Cause Name' },
        { value: 'enabled', label: 'Enabled Status' },
        { value: 'createdAt', label: 'Created Date' }
    ];

    // --- Editing ---
    selectedRootCause: RootCause | null = null; // <-- Object to hold data for editing

    // --- Creating ---
    newRootCauseData: NewRootCauseData = { // <-- Object to hold new root cause form data
        title: '',
        enabled: true // Default new root causes to active
    };


    constructor(
        readonly rootCauseService: RootCauseService,
        readonly updateService: UpdateService,
        // Optional: Inject ToastrService
        // private toastr: ToastrService
    ) { }

    ngOnInit(): void {
        // Changed method name and passed current page
        this.loadRootCauses(this.currentPage);
    }

    // Helper to get current list data
    getCurrentListData(): RootCause[] | undefined {
        return this.rootCauseList;
    }

    // Fetch ALL root causes matching current filters (no pagination)
    async fetchAllFilteredRootCauses(): Promise<RootCause[] | null> {
        this.listLoading = true; // Indicate loading
        const filterParams: string[] = [];
        if (this.filters.name) { filterParams.push(`title||$contL||${this.filters.name}`); }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
             filterParams.push(`enabled||$eq||${this.filters.enabled}`);
        }

        const data = {
            limit: 10000, // Large limit for "all"
            sort: `${this.filters.sortField || 'title'},${this.filters.sortDirection || 'ASC'}`, // Match default sort
            filter: filterParams
        };

        try {
            const params = createAxiosConfig(data);
            console.log('API Request Params (Root Causes Download - All Data):', JSON.stringify(params, null, 2));
            const response = await this.rootCauseService.getRootCause(params); // Use existing service method
            return response ?? [];
        } catch (error: any) {
            console.error("Error fetching all root causes for download:", error);
            this.toast?.showErrorToast(error?.response?.data?.message || "Failed to retrieve full data for download.");
            return null;
        } finally {
             this.listLoading = false; // Reset loading indicator
        }
    }

    // Main download function
    async downloadExcel(type: 'current' | 'all') {
        if (this.isDownloadingExcel || this.listLoading) return; // Prevent concurrent actions

        this.isDownloadingExcel = true;
        this.downloadType = type;
        this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} root causes...`);

        let dataToExport: RootCause[] | null = null;

        try {
            // 1. Get Data
            if (type === 'all') {
                dataToExport = await this.fetchAllFilteredRootCauses();
            } else { // 'current'
                dataToExport = this.getCurrentListData() ?? null;
                if (dataToExport === undefined) { dataToExport = null; }
            }

            // 2. Check data
            if (dataToExport === null) { this.toast?.showErrorToast("Failed to retrieve data for download."); return; }
            if (!dataToExport || dataToExport.length === 0) { this.toast?.showErrorToast(`No root causes available to download.`); return; }

            console.log(`Fetched ${dataToExport.length} root causes for Excel export (${type}).`);

            // 3. Transform data
            const dataForExcel = dataToExport.map(rc => ({
                'Root Cause ID': rc.id,
                'Root Cause Name': rc.title,
                'Status': rc.enabled ? 'Active' : 'Inactive',
                'Created At': rc.createdAt ? new Date(rc.createdAt).toLocaleString() : 'N/A',
                'Updated At': rc.updatedAt ? new Date(rc.updatedAt).toLocaleString() : 'N/A',
            }));

            // 4. Create Worksheet and Workbook
            const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
            const wb: XLSX.WorkBook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'RootCauses'); // Sheet name

            // 5. Generate File Name
            const dateStr = new Date().toISOString().slice(0, 10);
            const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
            const fileName = `RootCauses_${typeStr}_${dateStr}.xlsx`;

            // 6. Trigger Download
            XLSX.writeFile(wb, fileName);
            this.toast?.showSuccessToast(`Excel file download started (${type}).`);

        } catch (error) {
            console.error(`Error generating Excel file (${type}):`, error);
            this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
        } finally {
            this.isDownloadingExcel = false;
            this.downloadType = null;
        }
    }

    // --- Filter Modal Methods ---
    openFilterModal(): void { this.isFilterModalOpen = true; }
    closeFilterModal(): void { this.isFilterModalOpen = false; } // Renamed from closeModal

    // Filter form is already declared at the top of the class

    applyFilters(): void {
        // Check for validation errors
        if (this.filterForm?.invalid) {
            Object.values(this.filterForm.controls).forEach(control => control.markAsTouched());
            console.warn('Filter form submitted while invalid.');
            this.toast?.showErrorToast('Please correct the validation errors before applying filters.');
            return;
        }

        this.currentPage = 1; // Reset page on new filter apply
        this.loadRootCauses(this.currentPage);
        this.closeFilterModal(); // Close modal after applying
    }
    resetFilters(): void {
        // Reset filter object to defaults
        this.filters = {
            name: null,
            enabled: 'true',
            sortField: 'title',
            sortDirection: 'ASC'
        };
        this.currentPage = 1; // Reset page
        this.loadRootCauses(this.currentPage);
        // Optionally close modal: this.closeFilterModal();
    }


    // --- *** NEW: Edit Modal Methods *** ---
    /** Opens the edit root cause offcanvas modal. */
    openEditModal(rootCause: RootCause): void {
        this.selectedRootCause = { ...rootCause }; // Create a copy
        this.isEditModalOpen = true;
        this.editLoading = false;
    }

    /** Closes the edit root cause offcanvas modal. */
    closeEditModal(): void {
        this.isEditModalOpen = false;
        this.selectedRootCause = null;
    }

    /** Handles the submission of the edit root cause form. */
    async submitEditForm(): Promise<void> {
        if (!this.selectedRootCause || this.selectedRootCause.id == null) {
            console.error("Cannot save, selected root cause is null or has no ID.");
            this.toast?.showErrorToast('Cannot save, no root cause selected.');
            return;
        }

        // Check for validation errors
        if (this.editForm?.invalid) {
            Object.values(this.editForm.controls).forEach(control => control.markAsTouched());
            console.warn('Edit form submitted while invalid.');
            this.toast?.showErrorToast('Please correct the validation errors before submitting.');
            return;
        }

        this.editLoading = true;
        const updatePayload = {
            tableName: 'root-cause-master', // Critical: Ensure this matches your backend/service expectation
            id: this.selectedRootCause.id,
            data: {
                title: this.selectedRootCause.title,
                enabled: this.selectedRootCause.enabled
                // Add other editable fields if necessary
            }
        };

        try {
            await this.update(updatePayload); // Use the generic update method
            // this.toastr.success('Root cause updated successfully!', 'Saved');

            // Update list locally (optional)
            const index = this.rootCauseList.findIndex(rc => rc.id === this.selectedRootCause?.id);
            if (index !== -1 && this.selectedRootCause) {
                this.rootCauseList[index] = { ...this.selectedRootCause };
            }

            this.closeEditModal();
            // Consider reloading if necessary: this.loadRootCauses(this.currentPage);

        } catch (error) {
            console.error("Error submitting root cause update:", error);
            // this.toastr.error('Failed to update root cause.', 'Error');
        } finally {
            this.editLoading = false;
        }
    }


    // --- *** NEW: Create Modal Methods *** ---

    /** Opens the create root cause offcanvas modal and resets the form data. */
    openCreateModal(): void {
        this.newRootCauseData = {
            title: '',
            enabled: true
        };
        this.createForm?.resetForm({ enabled: true }); // Reset form state with default
        this.isCreateModalOpen = true;
        this.createLoading = false;
    }

    /** Closes the create root cause offcanvas modal. */
    closeCreateModal(): void {
        this.isCreateModalOpen = false;
    }

    /** Handles the submission of the create root cause form. */
    async submitCreateForm(): Promise<void> {
        if (this.createForm?.invalid) {
            Object.values(this.createForm.controls).forEach(control => control.markAsTouched());
            console.warn('Create form submitted while invalid.');
            this.toast?.showErrorToast('Please correct the validation errors before submitting.');
            return;
        }

        this.createLoading = true;
        console.log('Submitting new root cause:', this.newRootCauseData);

        try {
            // *** ASSUMPTION: rootCauseService has a createRootCause method ***
            const createdRootCause = await this.rootCauseService.createRootCause(this.newRootCauseData);
            console.log('Root cause created successfully:', createdRootCause);

            // this.toastr.success(`Root cause "${createdRootCause.title}" created successfully!`, 'Created');

            this.closeCreateModal();
            // Reload list, go to page 1
            this.currentPage = 1;
            this.loadRootCauses(this.currentPage);

        } catch (error) {
            console.error("Error creating root cause:", error);
            // this.toastr.error('Failed to create root cause. Please try again.', 'Creation Error');
        } finally {
            this.createLoading = false;
        }
    }


    // --- Data Loading & Update Methods ---

    /** Fetches root causes from the backend. (Refactored from getRootCauseList) */
    async loadRootCauses(page: number): Promise<void> {
        this.listLoading = true; // Set loading true
        // this.rootCauseList = []; // Clear only after loading starts

        // Build filter params dynamically
        const filterParams: string[] = [];
        if (this.filters.name) {
            filterParams.push(`title||$contL||${this.filters.name}`);
        }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
            filterParams.push(`enabled||$eq||${this.filters.enabled}`);
        }

        const requestData = {
            page: page,
            limit: this.itemsPerPage,
            sort: `${this.filters.sortField || 'title'},${this.filters.sortDirection || 'ASC'}`,
            filter: filterParams // Use dynamic filters
        };

        try {
            const param = createAxiosConfig(requestData); // Use utility function
            // Ensure service method returns { data: RootCause[], total: number }
            const response = await this.rootCauseService.getRootCause(param); // Pass params object
            this.rootCauseList = response?.data ?? []; // Use response.data
            this.totalItems = response?.total ?? 0; // Use response.total
        } catch (error) {
            console.error("Error fetching root causes:", error);
            this.rootCauseList = []; // Clear list on error
            this.totalItems = 0;
            // this.toastr.error('Failed to load root causes.', 'Error');
        } finally {
            this.listLoading = false; // Set loading false
        }
    }

    /** Handles page changes. */
    onPageChange(page: number): void {
        if (page !== this.currentPage) {
            this.currentPage = page;
            this.loadRootCauses(this.currentPage); // Call refactored method
        }
    }

    /** Handles the toggle switch change event in the table row. */
    async onSwitchToggle(isEnabled: boolean, rootCause: RootCause): Promise<void> {
        const originalState = rootCause.enabled;
        rootCause.enabled = isEnabled; // Optimistic update

        const updatePayload = {
            tableName: 'root-cause-master', // Use correct table name
            id: rootCause.id,
            data: {
                enabled: isEnabled
            }
        };
        console.log(`Attempting to update root cause ${rootCause.id} enabled status to: ${isEnabled}`);

        try {
            await this.update(updatePayload);
            console.log(`Successfully updated root cause ${rootCause.id} enabled status.`);
            // this.toastr.success('Root cause status updated.', 'Success');
        } catch (error) {
            console.error(`Error updating enabled status for root cause ${rootCause.id}:`, error);
            // this.toastr.error('Failed to update root cause status.', 'Update Error');
            // Revert UI change on error
            rootCause.enabled = originalState;
            // Force update detection if needed
            const index = this.rootCauseList.findIndex(rc => rc.id === rootCause.id);
            if (index !== -1) {
                this.rootCauseList[index] = { ...this.rootCauseList[index], enabled: originalState };
            }
        }
    }

    // Generic update via UpdateService (used by toggle and edit form)
    async update(data: { tableName: string, id: number, data: any }): Promise<void> {
        try {
            await this.updateService.update(data);
        } catch (error) {
            console.error("Update service call failed:", error);
            throw error; // Re-throw
        }
    }

}