import { Component, OnInit, ViewChild } from '@angular/core'; // Added ViewChild
import { FormsModule, NgForm } from '@angular/forms'; // Added NgForm
import { DesignationService } from '../../../services/master-management/designation/designation.service';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { CommonModule } from '@angular/common';
import { SwitchComponent } from "../../../shared/switch/switch.component";
import { PaginationComponent } from "../../../shared/pagination/pagination.component";
import { OffcanvasComponent } from "../../../shared/offcanvas/offcanvas.component";
import { UpdateService } from '../../../services/update/update.service';
// Optional: Import ToastrService or similar
// import { ToastrService } from 'ngx-toastr';
import { ToastMessageComponent } from '../../../shared/toast-message/toast-message.component'; // Adjust path if needed
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap'; // For dropdown button
import * as XLSX from 'xlsx'; // For Excel generation

// Interface for filter structure
interface DesignationFilter {
    name?: string | null;
    enabled?: string | null;
    sortField?: string | null;
    sortDirection?: 'ASC' | 'DESC';
}

// *** NEW: Interface for Designation Data ***
export interface Designation {
    id: number;
    title: string;
    enabled: boolean;
    createdAt?: string; // Optional based on your API response
    updatedAt?: string; // Optional based on your API response
}

// *** NEW: Interface for Create Form Data ***
interface NewDesignationData {
    title: string;
    enabled: boolean;
}


@Component({
    selector: 'app-designation',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        SwitchComponent,
        PaginationComponent,
        OffcanvasComponent,
        NgbDropdownModule,
        ToastMessageComponent
    ],
    templateUrl: './designation.component.html',
    styleUrl: './designation.component.scss'
})
export class DesignationComponent implements OnInit {
    // Access the form template variable
    @ViewChild('createForm') createForm?: NgForm; // Optional: If you need to reset the form state
    @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;

    // --- Modal States ---
    isFilterModalOpen = false; // Renamed from isEditUserModalOpen
    isEditModalOpen = false;   // <-- New state for edit modal
    isCreateModalOpen = false; // <-- New state for create modal

    // --- Data & Loading States ---
    designationList: Designation[] = []; // Use Designation interface
    listLoading = false;
    editLoading = false;     // <-- New state for edit form submission
    createLoading = false;   // <-- New state for create form submission

    // --- Pagination ---
    currentPage = 1;
    itemsPerPage = 10;
    totalItems = 0;
    isDownloadingExcel = false;
    downloadType: 'current' | 'all' | null = null;

    // --- Filtering ---
    filters: DesignationFilter = {
        name: null,
        enabled: 'true',
        sortField: 'title',
        sortDirection: 'ASC'
    };
    availableSortFields = [
        { value: 'id', label: 'ID' },
        { value: 'title', label: 'Designation Name' },
        { value: 'enabled', label: 'Enabled Status' },
        { value: 'createdAt', label: 'Created Date' }
    ];

    // --- Editing ---
    selectedDesignation: Designation | null = null; // <-- Object to hold data for editing

    // --- Creating ---
    newDesignationData: NewDesignationData = { // <-- Object to hold new designation form data
        title: '',
        enabled: true // Default new designations to active
    };


    constructor(
        readonly designationService: DesignationService, // Service to fetch/create/etc.
        readonly updateService: UpdateService,          // Service for generic updates (like toggle/edit)
        // Optional: Inject ToastrService
        // private toastr: ToastrService
    ) { }

    ngOnInit() {
        this.loadDesignations(this.currentPage);
    }

    // Helper to get current list data
    getCurrentListData(): Designation[] | undefined {
        return this.designationList;
    }

    // Fetch ALL designations matching current filters (no pagination)
    async fetchAllFilteredDesignations(): Promise<Designation[] | null> {
        this.listLoading = true; // Indicate loading
        const filterParams: string[] = [];
        if (this.filters.name) { filterParams.push(`title||$contL||${this.filters.name}`); }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
             filterParams.push(`enabled||$eq||${this.filters.enabled}`);
        }

        const data = {
            limit: 10000, // Large limit for "all"
            sort: `${this.filters.sortField || 'title'},${this.filters.sortDirection || 'ASC'}`, // Match default sort
            filter: filterParams
        };

        try {
            const params = createAxiosConfig(data);
            console.log('API Request Params (Designations Download - All Data):', JSON.stringify(params, null, 2));
            const response = await this.designationService.getDesignation(params); // Use existing service method
            return response; // Handle potential structure differences
        } catch (error: any) {
            console.error("Error fetching all designations for download:", error);
            this.toast?.showErrorToast(error?.response?.data?.message || "Failed to retrieve full data for download.");
            return null;
        } finally {
             this.listLoading = false; // Reset loading indicator
        }
    }

    // Main download function
    async downloadExcel(type: 'current' | 'all') {
        if (this.isDownloadingExcel || this.listLoading) return; // Prevent concurrent actions

        this.isDownloadingExcel = true;
        this.downloadType = type;
        this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} designations...`);

        let dataToExport: Designation[] | null = null;

        try {
            // 1. Get Data
            if (type === 'all') {
                dataToExport = await this.fetchAllFilteredDesignations();
            } else { // 'current'
                dataToExport = this.getCurrentListData() ?? null;
                if (dataToExport === undefined) { dataToExport = null; }
            }

            // 2. Check data
            if (dataToExport === null) { this.toast?.showErrorToast("Failed to retrieve data for download."); return; }
            if (!dataToExport || dataToExport.length === 0) { this.toast?.showErrorToast(`No designations available to download.`); return; }

            console.log(`Fetched ${dataToExport.length} designations for Excel export (${type}).`);

            // 3. Transform data
            const dataForExcel = dataToExport.map(desig => ({
                'Designation ID': desig.id,
                'Designation Name': desig.title,
                'Status': desig.enabled ? 'Active' : 'Inactive',
                'Created At': desig.createdAt ? new Date(desig.createdAt).toLocaleString() : 'N/A',
                'Updated At': desig.updatedAt ? new Date(desig.updatedAt).toLocaleString() : 'N/A',
            }));

            // 4. Create Worksheet and Workbook
            const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
            const wb: XLSX.WorkBook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'Designations'); // Sheet name

            // 5. Generate File Name
            const dateStr = new Date().toISOString().slice(0, 10);
            const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
            const fileName = `Designations_${typeStr}_${dateStr}.xlsx`;

            // 6. Trigger Download
            XLSX.writeFile(wb, fileName);
            this.toast?.showSuccessToast(`Excel file download started (${type}).`);

        } catch (error) {
            console.error(`Error generating Excel file (${type}):`, error);
            this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
        } finally {
            this.isDownloadingExcel = false;
            this.downloadType = null;
        }
    }

    // --- Filter Modal Methods ---
    openFilterModal(): void { this.isFilterModalOpen = true; }
    closeFilterModal(): void { this.isFilterModalOpen = false; }

    @ViewChild('filterForm') filterForm!: NgForm;

    applyFilters(): void {
        // Check if form is valid before applying filters
        if (this.filterForm && this.filterForm.invalid) {
            this.toast?.showErrorToast("Please correct the validation errors before applying filters.");
            return;
        }

        // Trim string values to prevent whitespace-only searches
        if (this.filters.name) {
            this.filters.name = this.filters.name.trim();
        }

        this.currentPage = 1;
        this.loadDesignations(this.currentPage);
        this.closeFilterModal();
    }

    resetFilters(): void {
        this.filters = {
            name: null,
            enabled: 'true',
            sortField: 'title',
            sortDirection: 'ASC'
        };
        this.currentPage = 1;
        this.loadDesignations(this.currentPage);
        // Optionally close modal: this.closeFilterModal();
    }

    // --- *** NEW: Edit Modal Methods *** ---
    /** Opens the edit designation offcanvas modal. */
    openEditModal(designation: Designation): void {
        // Create a copy to avoid modifying the list directly
        this.selectedDesignation = { ...designation };
        this.isEditModalOpen = true;
        this.editLoading = false; // Reset loading state
    }

    /** Closes the edit designation offcanvas modal. */
    closeEditModal(): void {
        this.isEditModalOpen = false;
        this.selectedDesignation = null; // Clear selected data
    }

    @ViewChild('editForm') editForm!: NgForm;

    /** Handles the submission of the edit designation form. */
    async submitEditForm(): Promise<void> {
        if (!this.selectedDesignation || this.selectedDesignation.id == null) {
            console.error("Cannot save, selected designation is null or has no ID.");
            this.toast?.showErrorToast('Cannot save, no designation selected.');
            return;
        }

        // Check form validity
        if (this.editForm && this.editForm.invalid) {
            this.toast?.showErrorToast("Please correct the validation errors before saving.");
            // Mark fields as touched to show validation errors
            Object.values(this.editForm.controls).forEach(control => {
                control.markAsTouched();
            });
            return;
        }

        // Trim the title to remove any leading/trailing whitespace
        if (this.selectedDesignation.title) {
            this.selectedDesignation.title = this.selectedDesignation.title.trim();
        }

        this.editLoading = true;
        const updatePayload = {
            tableName: 'designation', // Ensure this matches your backend expectation
            id: this.selectedDesignation.id,
            data: {
                title: this.selectedDesignation.title,
                enabled: this.selectedDesignation.enabled
                // Add other editable fields here if needed
            }
        };

        try {
            // Use the generic update service
            await this.update(updatePayload);
            this.toast?.showSuccessToast('Designation updated successfully!');

            // Update the list locally for immediate feedback (optional but good UX)
            const index = this.designationList.findIndex(d => d.id === this.selectedDesignation?.id);
            if (index !== -1 && this.selectedDesignation) {
                 this.designationList[index] = { ...this.selectedDesignation };
            }

            this.closeEditModal();
            // Reloading might be necessary if backend modifies data (e.g., updatedAt)
            // or if local update is not preferred. Uncomment below if needed.
            // this.loadDesignations(this.currentPage);

        } catch (error) {
            console.error("Error submitting designation update:", error);
            this.toast?.showErrorToast('Failed to update designation.');
            // Keep modal open on error
        } finally {
            this.editLoading = false;
        }
    }


    // --- *** NEW: Create Modal Methods *** ---

    /** Opens the create designation offcanvas modal and resets the form data. */
    openCreateModal(): void {
        // Reset form data to defaults
        this.newDesignationData = {
            title: '',
            enabled: true
        };
        // Optionally reset form validation state
        this.createForm?.resetForm({ enabled: true }); // Keep default value after reset

        this.isCreateModalOpen = true;
        this.createLoading = false; // Ensure loading state is reset
    }

    /** Closes the create designation offcanvas modal. */
    closeCreateModal(): void {
        this.isCreateModalOpen = false;
    }

    /** Handles the submission of the create designation form. */
    async submitCreateForm(): Promise<void> {
        if (this.createForm?.invalid) {
             Object.values(this.createForm.controls).forEach(control => {
                 control.markAsTouched();
             });
             this.toast?.showErrorToast("Please correct the validation errors before creating.");
             console.warn('Create form submitted while invalid.');
            return;
        }

        // Trim the title to remove any leading/trailing whitespace
        if (this.newDesignationData.title) {
            this.newDesignationData.title = this.newDesignationData.title.trim();
        }

        this.createLoading = true;
        console.log('Submitting new designation:', this.newDesignationData);

        try {
            // *** ASSUMPTION: designationService has a createDesignation method ***
             const createdDesig = await this.designationService.createDesignation(this.newDesignationData);
             console.log('Designation created successfully:', createdDesig);

            this.toast?.showSuccessToast(`Designation "${createdDesig.title}" created successfully!`);

            this.closeCreateModal();

            // Reload the list, go to page 1 to see the new item easily
            this.currentPage = 1;
            this.loadDesignations(this.currentPage);

        } catch (error) {
            console.error("Error creating designation:", error);
            this.toast?.showErrorToast('Failed to create designation. Please try again.');
            // Keep modal open on error
        } finally {
            this.createLoading = false;
        }
    }


    // --- Data Loading & Update Methods ---

    /** Fetches designations from the backend based on current filters and pagination. */
    async loadDesignations(page: number): Promise<void> {
        this.listLoading = true;
        // Clear list only after confirming loading starts, prevents flickering
        // this.designationList = [];

        const filterParams: string[] = [];
        if (this.filters.name) {
            filterParams.push(`title||$contL||${this.filters.name}`);
        }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
             // Ensure value sent is boolean 'true' or 'false' if backend expects that, adjust 'eq' if needed
            filterParams.push(`enabled||$eq||${this.filters.enabled}`);
        }

        const requestData = {
            page: page,
            limit: this.itemsPerPage,
            sort: `${this.filters.sortField || 'title'},${this.filters.sortDirection || 'ASC'}`,
            filter: filterParams
        };

        try {
            const params = createAxiosConfig(requestData);
            // Ensure service method returns { data: Designation[], total: number }
            const response = await this.designationService.getDesignation(params);
            this.designationList = response?.data ?? [];
            this.totalItems = response?.total ?? 0;
        } catch (error) {
            console.error("Error fetching designations:", error);
            this.designationList = []; // Clear list on error
            this.totalItems = 0;
            // this.toastr.error('Failed to load designations.', 'Error');
        } finally {
            this.listLoading = false;
        }
    }

    /** Handles page changes from the pagination component. */
    onPageChange(page: number): void {
        if (page !== this.currentPage) {
            this.currentPage = page;
            this.loadDesignations(this.currentPage);
        }
    }

     /** Handles the toggle switch change event in the table row. */
     async onSwitchToggle(isEnabled: boolean, designation: Designation): Promise<void> {
        const originalState = designation.enabled; // Store original state for revert on error
        designation.enabled = isEnabled; // Optimistic UI update

        const updatePayload = {
            tableName: 'designation',
            id: designation.id,
            data: {
                enabled: isEnabled
            }
        };
        console.log(`Attempting to update designation ${designation.id} enabled status to: ${isEnabled}`);

        try {
            await this.update(updatePayload);
            console.log(`Successfully updated designation ${designation.id} enabled status.`);
            // this.toastr.success('Designation status updated.', 'Success');
            // No need to update list again if optimistic update worked
        } catch (error) {
            console.error(`Error updating enabled status for designation ${designation.id}:`, error);
            // this.toastr.error('Failed to update designation status.', 'Update Error');
             // Revert UI change on error
            designation.enabled = originalState;
             // Find the item in the list again and force update detection if needed
             const index = this.designationList.findIndex(d => d.id === designation.id);
             if (index !== -1) {
                 this.designationList[index] = {...this.designationList[index], enabled: originalState };
             }
        }
    }

    // Generic update via UpdateService (used by toggle and edit form)
    async update(data: { tableName: string, id: number, data: any }): Promise<void> {
        try {
            await this.updateService.update(data);
        } catch (error) {
            console.error("Update service call failed:", error);
            throw error; // Re-throw to be caught by calling function
        }
    }

    // --- Removed Placeholder/Unused Methods ---
    // Removed handleCreate, handleUpdate, toggleEnabled, handleDelete, sortBy, getSortClass
    // as their functionality is now covered by the modal forms and the central loadDesignations method.

}