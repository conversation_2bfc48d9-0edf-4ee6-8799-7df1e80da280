import { CommonModule } from '@angular/common';
import { Component, OnInit, ViewChild, inject } from '@angular/core'; // Removed unused imports
import { FormsModule } from '@angular/forms'; // Import FormsModule
import { ReportManagementService } from '../../../../services/report-management/report-management.service';
import { PaginationComponent } from "../../../../shared/pagination/pagination.component";
import { OffcanvasComponent } from "../../../../shared/offcanvas/offcanvas.component";
import { NgSelectModule } from '@ng-select/ng-select'; // Import NgSelectModule
import { PlantManagementService } from '../../../../services/plant-management/plant-management.service'; // Import Plant service
import { createAxiosConfig } from '../../../../core/utilities/axios-param-config'; // If you use this helper
import { ClusterService } from '../../../../services/master-management/cluster/cluster.service'; // Keep if used elsewhere
import { ToastMessageComponent } from '../../../../shared/toast-message/toast-message.component'; // Adjust path if needed
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap'; // For dropdown button
import * as XLSX from 'xlsx'; // For Excel generation

// Define ROLES constant
const ROLES = {
    SUPER_ADMIN: 'super_admin',
    PLANT_ADMIN: 'plant_admin',
};

// Interfaces (adjust properties based on your actual data)
interface Cluster {
  id: number;
  name: string;
}

interface Plant {
  id: number;
  name: string;
  clusterId?: number;
}

interface PlantwiseReportFilter {
    startDate?: string | null;
    endDate?: string | null;
    plantIds?: number[] | null; // Allow multiple plant selections in filter modal
}

interface PlantReportItem {
    name: string;
    totalUserInPlant?: number;
    totalNumberOfTours?: number;
    totalObservation?: number;
    totalOpenObservation?: number;
    totalClosedObservation?: number;
    totalFixedItObservation?: number;
    totalSafeObservation?: number;
    totalUnSafeObservation?: number;
    totalUnSafeConditionObservation?: number;
    totalTourTime?: number | string;
    avgTourTime?: number | string;
    plantId?: number; // Ensure plantId is available if needed
}

@Component({
    selector: 'app-plantwise-report',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        NgSelectModule,
        PaginationComponent,
        OffcanvasComponent,
        NgbDropdownModule,
        ToastMessageComponent
    ],
    templateUrl: './plantwise-report.component.html',
    styleUrl: './plantwise-report.component.scss'
})
export class PlantwiseReportComponent implements OnInit {
    @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;
    isEditUserModalOpen = false; // Use for filter offcanvas
    list: PlantReportItem[] = []; // Use specific type
    listLoading = true;

    istotalUserInPlant = true;
    istotalNumberOfTours = true;
    istotalObservation = true;
    istotalOpenObservation = true;
    istotalClosedObservation = true;
    istotalFixedItObservation = true;
    istotalSafeObservation = true;
    istotalUnSafeObservation = true;
    istotalUnSafeConditionObservation = true;

    currentPage = 1;
    itemsPerPage = 10; // Default page size
    totalItems = 0;
    isDownloadingExcel = false;
    downloadType: 'current' | 'all' | null = null;

    filters: PlantwiseReportFilter = {
        startDate: null,
        endDate: null,
        plantIds: null // Single plant selection model
    };

    // Role-Based Access Control Properties
    currentUserRole: string = '';
    loggedInAdminId: number | null = null;
    loggedInPlantIds: number[] = [];

    availablePlants: Plant[] = []; // Filtered based on role for dropdown

    // Select All checkbox state
    isAllPlantsSelected = false;

    // --- Inject Services ---
    private reportService = inject(ReportManagementService);
    private plantService = inject(PlantManagementService);
    isLoadingReport: boolean = false;
    // ClusterService not used directly, removed inject
    // private clusterService = inject(ClusterService);

    constructor( ) { }

    ngOnInit() {
        this.setCurrentUserRoleAndDetailsById(); // Set role first
        this.setDefaultDates(); // Set default date range (1 month)
        this.initializeDefaultFilters(); // Initialize default filters based on role
    }

    /**
     * Initialize default filters based on user role and load data
     */
    async initializeDefaultFilters() {
        await this.getPlants(); // Fetch plants based on role

        // Set default plant selection (all available plants)
        if (this.availablePlants.length > 0) {
            this.filters.plantIds = this.availablePlants.map(plant => plant.id);
            this.isAllPlantsSelected = true;
            console.log('Default plant selection:', this.filters.plantIds);
        }

        // Load report data with default filters
        this.loadReportData(this.currentPage);
    }

    /**
     * Set default date range to last 30 days
     */
    setDefaultDates(): void {
        const today = new Date();
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(today.getDate() - 30); // Last 30 days

        // Format dates as YYYY-MM-DD for input fields
        this.filters.startDate = this.formatDateForInput(startDate);
        this.filters.endDate = this.formatDateForInput(endDate);

        console.log('Set default date range:', this.filters.startDate, 'to', this.filters.endDate);
    }

    /**
     * Format date as YYYY-MM-DD for input fields
     */
    formatDateForInput(date: Date): string {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    private setCurrentUserRoleAndDetailsById(): void {
        try {
            const userString = localStorage.getItem('user');
            if (!userString || userString.trim() === "" || ['null', 'undefined', '""', '" "'].includes(userString.trim().toLowerCase())) {
                this.currentUserRole = ''; this.loggedInAdminId = null; this.loggedInPlantIds = [];
                this.toast?.showErrorToast("User session invalid."); return;
            }
            const currentUser = JSON.parse(userString);
            this.loggedInAdminId = currentUser?.id ?? null;
            this.loggedInPlantIds = (Array.isArray(currentUser?.plantIds) && currentUser.plantIds.length > 0)
                ? currentUser.plantIds.map((id: any) => Number(id)).filter((id: number) => !isNaN(id)) : [];
            const roleId = currentUser?.adminsRoleId;
            if (roleId === 1) { this.currentUserRole = ROLES.SUPER_ADMIN; }
            else if (roleId === 2) {
                this.currentUserRole = ROLES.PLANT_ADMIN;
                if (this.loggedInPlantIds.length === 0) { this.toast?.showErrorToast("Plant Admin has no assigned plants."); }
            } else { this.currentUserRole = ''; this.toast?.showErrorToast("Invalid user role."); }
            console.log(`Role: ${this.currentUserRole}, UserID: ${this.loggedInAdminId}, Plants: [${this.loggedInPlantIds.join(', ')}]`);
        } catch (error) {
            console.error("Error parsing user data:", error);
            this.currentUserRole = ''; this.loggedInAdminId = null; this.loggedInPlantIds = [];
            this.toast?.showErrorToast("Error reading user session.");
        }
    }

    getCurrentListData(): PlantReportItem[] | undefined {
        return this.list;
    }

    async fetchAllFilteredPlantReports(): Promise<PlantReportItem[] | null> {
        let plantIdsToSend: number[] | null = null;

        // Determine plant IDs based on role and *applied* filter
        if (this.currentUserRole === ROLES.PLANT_ADMIN) {
            if (this.loggedInPlantIds.length > 0) {
                // If plant admin has selected specific plants from their assigned list, use those
                if (this.filters.plantIds && this.filters.plantIds.length > 0) {
                    // Ensure selected plants are within the assigned plants
                    plantIdsToSend = this.filters.plantIds.filter(id => this.loggedInPlantIds.includes(id));
                } else {
                    // Otherwise, use all assigned plants
                    plantIdsToSend = this.loggedInPlantIds;
                }
            } else { return []; } // No plants assigned, return empty
        } else if (this.currentUserRole === ROLES.SUPER_ADMIN) {
            // Super admin uses selected plants, or null if none selected
            plantIdsToSend = (this.filters.plantIds && this.filters.plantIds.length > 0) ? this.filters.plantIds : null;
        } else { return null; } // Unknown role

        const payload: any = {
            pageSize: 100000,
            pageIndex: 1,
        };
        if (this.filters.startDate) payload.startDate = this.filters.startDate;
        if (this.filters.endDate) payload.endDate = this.filters.endDate;
        if (plantIdsToSend) payload.plantIds = plantIdsToSend; // Send as array

        console.log('Request Payload for ALL Plantwise Report Data:', payload);
        this.isLoadingReport = true;
        try {
            const res = await this.reportService.tourPlantWiseReport(payload);
            return res.data ?? [];
        } catch (error) {
            console.error("Error fetching all plantwise reports for download:", error);
            this.toast?.showErrorToast("Failed to retrieve full data for download.");
            return null;
        } finally {
            this.isLoadingReport = false;
        }
    }

    async downloadExcel(type: 'current' | 'all') {
        if (this.isDownloadingExcel || this.isLoadingReport) return;

        this.isDownloadingExcel = true;
        this.downloadType = type;
        this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} reports...`);

        let dataToExport: PlantReportItem[] | null = null;

        try {
            if (type === 'all') { dataToExport = await this.fetchAllFilteredPlantReports(); }
            else { dataToExport = this.getCurrentListData() ?? null; if (dataToExport === undefined) { dataToExport = null; } }

            if (dataToExport === null) { this.toast?.showErrorToast("Failed to retrieve data for download."); return; }
            if (!dataToExport || dataToExport.length === 0) { this.toast?.showErrorToast(`No reports available to download.`); return; }

            console.log(`Fetched ${dataToExport.length} plant reports for Excel export (${type}).`);

            const headers: { [key: string]: string } = { 'Plant': 'name' };
            if (this.istotalUserInPlant) headers['Users In Plant'] = 'totalUserInPlant';
            if (this.istotalNumberOfTours) headers['Number of Tours'] = 'totalNumberOfTours';
            if (this.istotalObservation) headers['Total Observations'] = 'totalObservation';
            if (this.istotalOpenObservation) headers['Open Observations'] = 'totalOpenObservation';
            if (this.istotalClosedObservation) headers['Closed Observations'] = 'totalClosedObservation';
            if (this.istotalFixedItObservation) headers['FixedIt Observations'] = 'totalFixedItObservation';
            if (this.istotalSafeObservation) headers['Safe Act Obs'] = 'totalSafeObservation';
            if (this.istotalUnSafeObservation) headers['Unsafe Act Obs'] = 'totalUnSafeObservation';
            if (this.istotalUnSafeConditionObservation) headers['Unsafe Cond. Obs'] = 'totalUnSafeConditionObservation';
            headers['Total Tour Time (Hours)'] = 'totalTourTime';
            headers['Avg Tour Time (Hours)'] = 'avgTourTime';

            const dataForExcel = dataToExport.map(item => {
                const row: any = {};
                for (const headerKey in headers) {
                    const dataKey = headers[headerKey] as keyof PlantReportItem;
                    if (dataKey === 'totalTourTime' || dataKey === 'avgTourTime') {
                        // Convert minutes to hours for Excel export (same logic as table display)
                        const timeValue = item[dataKey];
                        if (timeValue !== null && timeValue !== undefined && timeValue !== '') {
                            const numericValue = +timeValue;
                            row[headerKey] = !isNaN(numericValue) ? (numericValue / 60).toFixed(2) : '';
                        } else {
                            row[headerKey] = '';
                        }
                    } else {
                        row[headerKey] = item[dataKey] ?? '';
                    }
                }
                return row;
            });

            const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
            const wb: XLSX.WorkBook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'PlantwiseReport');

            const dateStr = new Date().toISOString().slice(0, 10);
            const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
            const fileName = `PlantwiseReport_${typeStr}_${dateStr}.xlsx`;

            XLSX.writeFile(wb, fileName);
            this.toast?.showSuccessToast(`Excel file download started (${type}).`);

        } catch (error) {
            console.error(`Error generating Excel file (${type}):`, error);
            this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
        } finally {
            this.isDownloadingExcel = false; this.downloadType = null;
        }
    }

    async getPlants(): Promise<void> {
        const data = { sort: 'name,ASC', filter: ['enabled||eq||true'], limit: 1000 };
        const param = createAxiosConfig(data);
        let allEnabledPlants: Plant[] = [];
        try {
            const response = await this.plantService.getPlants(param);
            allEnabledPlants = response?.data ?? response ?? [];
        } catch (error) {
            console.error("Error fetching plants:", error);
            this.availablePlants = [];
            this.toast?.showErrorToast("Failed to load plants list.");
            return;
        }

        // Filter based on role
        if (this.currentUserRole === ROLES.PLANT_ADMIN && this.loggedInPlantIds.length > 0) {
            this.availablePlants = allEnabledPlants.filter(plant => this.loggedInPlantIds.includes(plant.id));
        } else if (this.currentUserRole === ROLES.SUPER_ADMIN) {
            this.availablePlants = allEnabledPlants;
        } else {
            this.availablePlants = [];
        }
        console.log(`Loaded ${this.availablePlants.length} plants accessible to current user for filter.`);

        // Update Select All Plants checkbox state
        this.updateSelectAllPlantsState();
    }

    openFilterModal() { this.isEditUserModalOpen = true; }
    closeModal() { this.isEditUserModalOpen = false; }

    // Toggle Select All Plants checkbox
    toggleSelectAllPlants(event: Event): void {
        const checkbox = event.target as HTMLInputElement;
        if (checkbox.checked) {
            // Select all plants
            this.filters.plantIds = this.availablePlants.map(plant => plant.id);
            this.isAllPlantsSelected = true;
        } else {
            // Deselect all plants
            this.filters.plantIds = [];
            this.isAllPlantsSelected = false;
        }
    }

    // Update Select All Plants checkbox state based on current selection
    updateSelectAllPlantsState(): void {
        if (!this.filters.plantIds || this.filters.plantIds.length === 0) {
            this.isAllPlantsSelected = false;
        } else if (this.filters.plantIds.length === this.availablePlants.length) {
            this.isAllPlantsSelected = true;
        } else {
            this.isAllPlantsSelected = false;
        }
    }

    async loadReportData(page: number) {
        this.listLoading = true;
        this.list = [];

        let plantIdsToSend: number[] | null = null;

        // Determine plant IDs based on role and filter selection
        if (this.currentUserRole === ROLES.PLANT_ADMIN) {
            if (this.loggedInPlantIds.length > 0) {
                // If plant admin has selected specific plants from their assigned list, use those
                if (this.filters.plantIds && this.filters.plantIds.length > 0) {
                    // Ensure selected plants are within the assigned plants
                    plantIdsToSend = this.filters.plantIds.filter(id => this.loggedInPlantIds.includes(id));
                } else {
                    // Otherwise, use all assigned plants
                    plantIdsToSend = this.loggedInPlantIds;
                }
            } else {
                console.warn("Plant Admin has no plants, skipping report fetch.");
                this.totalItems = 0; this.listLoading = false; return;
            }
        } else if (this.currentUserRole === ROLES.SUPER_ADMIN) {
            // Super admin uses selected plants, or null if none selected
            plantIdsToSend = (this.filters.plantIds && this.filters.plantIds.length > 0) ? this.filters.plantIds : null;
        } else {
             console.error("Unknown user role, cannot fetch report.");
             this.totalItems = 0; this.listLoading = false; return;
        }

        const payload: any = {
            pageSize: this.itemsPerPage,
            pageIndex: page,
        };
        if (this.filters.startDate) payload.startDate = this.filters.startDate;
        if (this.filters.endDate) payload.endDate = this.filters.endDate;
        if (plantIdsToSend) payload.plantIds = plantIdsToSend; // Send as array

        console.log('Request Payload (Plantwise):', payload);

        try {
            const res = await this.reportService.tourPlantWiseReport(payload);
            this.totalItems = res.total ?? 0;
            this.list = res.data ?? [];
        } catch (error: any) {
            console.error("Error fetching plantwise report:", error);
            this.totalItems = 0; this.list = [];
            this.toast?.showErrorToast(error?.response?.data?.message || 'Failed to load plantwise report.');
        } finally {
            this.listLoading = false;
        }
    }

    onPageChange(page: number) {
        this.currentPage = page;
        this.loadReportData(this.currentPage);
    }

    applyFilters() {
        // Validation for dates can be added here if needed
        this.currentPage = 1;
        this.loadReportData(this.currentPage);
        this.closeModal();
    }

    resetFilters() {
        this.filters = { startDate: null, endDate: null, plantIds: null };
        // Reset Select All checkbox state
        this.isAllPlantsSelected = false;
        // Role filter applied implicitly in loadReportData
        this.currentPage = 1;
        this.loadReportData(this.currentPage);
        // Optionally close modal: this.closeModal();
    }

    formatDisplayDate(dateString: string | null): string {
       if (!dateString) return 'N/A';
       try {
           const date = new Date(dateString);
           return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
       } catch (e) { return 'Invalid Date'; }
   }
}
