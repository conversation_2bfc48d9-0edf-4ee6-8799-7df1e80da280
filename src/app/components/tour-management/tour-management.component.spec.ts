import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ChangeDetectorRef } from '@angular/core';
import { TourManagementComponent } from './tour-management.component';

describe('TourManagementComponent', () => {
  let component: TourManagementComponent;
  let fixture: ComponentFixture<TourManagementComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TourManagementComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(TourManagementComponent);
    component = fixture.componentInstance;

    // Disable automatic change detection to prevent ExpressionChangedAfterItHasBeenCheckedError
    fixture.autoDetectChanges(false);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should handle change detection properly', async () => {
    // Manually trigger change detection
    fixture.detectChanges();
    await fixture.whenStable();

    expect(component).toBeTruthy();
  });
});
