import { UploadService } from '../../services/upload/upload.service';
import { CommonModule } from '@angular/common';
import { Component, OnInit, ViewChild, ChangeDetectorRef, ElementRef, AfterViewInit, OnDestroy } from '@angular/core';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ManageObservationService } from '../../services/manage-observation/manage-observation.service';
import { createAxiosConfig } from '../../core/utilities/axios-param-config';
import { PaginationComponent } from "../../shared/pagination/pagination.component";
import { OffcanvasComponent } from "../../shared/offcanvas/offcanvas.component";
import { PlantManagementService } from '../../services/plant-management/plant-management.service';
import { AdminService } from '../../services/admin/admin.service';
import { Carousel } from "../../shared/carousel/carousel.component";
import { ToastMessageComponent } from "../../shared/toast-message/toast-message.component";
import { UpdateService } from '../../services/update/update.service';
import { RelatesToService } from '../../services/master-management/relates-to/relates-to.service';
import { QrCodeService } from '../../services/qr-code/qr-code.service';
import { ZoneService } from '../../services/zone/zone.service';
import { TabComponent } from '../../shared/tab/tab.component';
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap';
import * as XLSX from 'xlsx';
import { Modal } from 'bootstrap';
import { DeleteService } from '../../services/delete/delete.service';

const ROLES = {
    SUPER_ADMIN: 'super_admin',
    PLANT_ADMIN: 'plant_admin',
};

interface ObservationFilter {
    firstName?: string | null;
    lastName?: string | null;
    email?: string | null;
    contactNumber?: string | null;
    startDate?: string | null;
    endDate?: string | null;
    plantId?: number | null;
    enabled?: boolean | null;
    sortDirection?: 'ASC' | 'DESC';
    sortField?: string;
    updatedTimestamp?: string | Date;
}

enum ObservationStatus {
    Open = 0,
    Closed = 1,
}

interface Plant { id: number; name: string; }
interface Zone { id: number; zoneName: string; }
interface QrCodeStub { id: number; zoneArea: string | null; }
interface SimpleMaster { id: number; title: string; }
interface AdminUser { id: number; firstName: string; lastName: string; contactNumber?: string | null; email?: string | null; }

interface Observation {
    id: number;
    admin: AdminUser | null;
    fixedByAdminId?: number | null;
    assignToAdmin?: AdminUser | null;
    assignToAdminId?: number | null;
    plant: Plant;
    plantId?: number;
    zone: Zone;
    zoneId?: number;
    qrCode: QrCodeStub | null;
    qrCodeId?: number | null;
    zoneArea?: string | null;
    type: number;
    title: string;
    relatesTos: SimpleMaster[] | null;
    thirdPartyCompanyName: string | null;
    images: string[];
    fixImages?: string[] | null;
    rewardGranted: boolean;
    status: ObservationStatus;
    isFixedIt?: boolean | null;
    enabled?: boolean | null;
    createdAt?: string | Date;
    createdTimestamp: string | Date;
    updatedBy?: number | null;
    updatedTimestamp?: string | Date;
    fullName?: string | null;
    email?: string | null;
    contactNumber?: string | null;
    updated?: AdminUser | null;
    approve?: AdminUser | null;
    rejected?: AdminUser | null;
    fixedItDescription?: string | null;
    counselingPersonName?: string | null;
    member?: AdminUser[] | null; // Added member array based on incident example
    memberIds?: number[]; // Added memberIds based on incident example
    teamLeader?: AdminUser | null; // Added teamLeader based on incident example
    teamLeaderId?: number | null; // Added teamLeaderId based on incident example
    engineerIncharge?: AdminUser | null; // Added engineerIncharge based on incident example
}


interface EditObservationData {
    id: number;
    enabled: boolean;
    isFixedIt: boolean;
    adminId: number | null;
    admin?: AdminUser | null;
    fullName?: string | null;
    email?: string | null;
    contactNumber?: string | null;
    plantId: number | null;
    zoneId: number | null;
    qrCodeId: number | null;
    type: number | null;
    title: string | null;
    fixedItDescription: string | null;
    counselingPersonName: string | null;
    thirdPartyCompanyName: string | null;
    images: string[];
    fixImages: string[];
    imagesToRemove: string[];
    fixImagesToRemove: string[];
    newImageFiles: File[];
    newFixImageFiles: File[];
}

@Component({
    selector: 'app-manage-observation',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        TabComponent, PaginationComponent,
        OffcanvasComponent, Carousel, ToastMessageComponent, NgbDropdownModule
    ],
    templateUrl: './manage-observation.component.html',
    styleUrls: ['./manage-observation.component.scss']
})
export class ManageObservationComponent implements OnInit, AfterViewInit, OnDestroy {
    public componentRoles = ROLES;
    public Math = Math; // Add Math object to make it accessible in the template

    @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;
    @ViewChild('deleteConfirmationModalElement') deleteConfirmationModalElement!: ElementRef;
    @ViewChild('updateStatusConfirmationModalElement') updateStatusConfirmationModalElement!: ElementRef;
    @ViewChild('rewardConfirmationModalElement') rewardConfirmationModalElement!: ElementRef;

    deleteConfirmationModalInstance: Modal | null = null;
    updateStatusConfirmationModalInstance: Modal | null = null;
    rewardConfirmationModalInstance: Modal | null = null;

    listLoading = false;
    editLoading = false;
    findItFixItList: Observation[] = [];
    openObservationList: Observation[] = [];
    closeObservationList: Observation[] = [];
    adminrole: any = "superadmin";
    isDownloadingExcel = false;
    downloadType: 'current' | 'all' | null = null;

    currentPage = 1;
    itemsPerPage = 10;
    totalItems = 0;

    selectedTabIndex = 0;
    tabs = [
        { title: 'Find it, Fix it', listKey: 'findItFixItList' as const, statusFilter: ['status||eq||1', 'isFixedIt||eq||true'] },
        { title: 'Open', listKey: 'openObservationList' as const, statusFilter: ['status||eq||0'] },
        { title: 'Close', listKey: 'closeObservationList' as const, statusFilter: ['status||eq||1', 'isFixedIt||ne||true'] },
    ];

    isFilterModalOpen = false;
    isEditModalOpen = false;
    isDetailsModalOpen = false;

    filters: ObservationFilter = {
        firstName: null, lastName: null, email: null, contactNumber: null,
        startDate: null, endDate: null, plantId: null,
        enabled: true, sortDirection: 'DESC', sortField: 'id'
    };

    currentUserRole: string = '';
    loggedInAdminId: number | null = null;
    loggedInPlantIds: number[] = [];

    plants: Plant[] = [];
    availableAdminsForAssign: AdminUser[] = [];
    availableRelatesTo: SimpleMaster[] = [];
    availableQrCodes: QrCodeStub[] = [];
    observationTypes = [
        { value: 0, label: 'Safe Act' }, { value: 1, label: 'Unsafe Act' }, { value: 2, label: 'Unsafe Conditions' },
    ];
    availableSortFields = [
        { value: 'id', label: 'ID' }, { value: 'createdTimestamp', label: 'Reported Date' },
        { value: 'admin.firstName', label: 'Reporter Name' }, { value: 'plant.name', label: 'Plant Name' },
        { value: 'type', label: 'Observation Type' }
    ];
    availableZones: Zone[] = [];
    editAvailableZones: Zone[] = [];

    editUserForm!: FormGroup;
    selectedObservationForEdit: EditObservationData | null = null;
    editExistingImages: string[] = [];
    editExistingFixImages: string[] = [];
    editImagesToRemove: string[] = [];
    editFixImagesToRemove: string[] = [];
    editNewImageFiles: File[] = [];
    editNewFixImageFiles: File[] = [];
    editNewImagePreviews: string[] = []; // Added for image previews
    editNewFixImagePreviews: string[] = []; // Added for fixed image previews

    selectedObservationForDetails: Observation | null = null;

    itemToDelete: Observation | null = null;
    itemToUpdateStatus: Observation | null = null;
    itemToReward: Observation | null = null;
    targetStatusForUpdate: ObservationStatus | null = null;
    modalTitle = '';
    modalMessage = '';
    confirmButtonText = '';
    confirmButtonClass = '';
    confirmIconClass = '';

    ObservationStatus = ObservationStatus;


    constructor(
        private fb: FormBuilder,
        private observationService: ManageObservationService,
        private plantService: PlantManagementService,
        private adminService: AdminService,
        private updateService: UpdateService,
        private relatesToService: RelatesToService,
        private qrCodeService: QrCodeService,
        private zoneService: ZoneService,
        private deleteService: DeleteService,
        private cdr: ChangeDetectorRef,
        private uploadService: UploadService,
    ) { }

    ngOnInit(): void {
        this.setCurrentUserRoleAndDetailsById();
        this.initializeEditForm();
        this.getPlants();
        this.loadAssignableAdmins();
        this.loadRelatesToOptions();
        this.loadObservationsForCurrentTab();
    }

    ngAfterViewInit(): void {
        if (this.deleteConfirmationModalElement) {
            this.deleteConfirmationModalInstance = new Modal(this.deleteConfirmationModalElement.nativeElement);
        } else { console.error("Delete confirmation modal element not found!"); }

        if (this.updateStatusConfirmationModalElement) {
            this.updateStatusConfirmationModalInstance = new Modal(this.updateStatusConfirmationModalElement.nativeElement);
        } else { console.error("Update status confirmation modal element not found!"); }

        if (this.rewardConfirmationModalElement) {
            this.rewardConfirmationModalInstance = new Modal(this.rewardConfirmationModalElement.nativeElement);
        } else { console.error("Reward confirmation modal element not found!"); }
    }

    ngOnDestroy(): void {
        this.deleteConfirmationModalInstance?.dispose();
        this.updateStatusConfirmationModalInstance?.dispose();
        this.rewardConfirmationModalInstance?.dispose();
    }

    private setCurrentUserRoleAndDetailsById(): void {
        try {
            const userString = localStorage.getItem('user');
            if (!userString || userString.trim() === "" || ['null', 'undefined', '""', '" "'].includes(userString.trim().toLowerCase())) {
                console.error("User data not found or is invalid in localStorage.");
                this.currentUserRole = '';
                this.loggedInAdminId = null;
                this.loggedInPlantIds = [];
                this.toast?.showErrorToast("User session invalid. Please log in again.");
                return;
            }

            const currentUser = JSON.parse(userString);
            console.log('Current User Parsed for Role Check (Observation Mgmt):', currentUser);

            this.loggedInAdminId = currentUser?.id ?? null;

            this.loggedInPlantIds = (Array.isArray(currentUser?.plantIds) && currentUser.plantIds.length > 0)
                                      ? currentUser.plantIds.map((id: any) => Number(id)).filter((id: number) => !isNaN(id))
                                      : [];

            const roleId = currentUser?.adminsRoleId;

            if (roleId === 1) {
                this.currentUserRole = this.componentRoles.SUPER_ADMIN;
            } else if (roleId === 2) {
                this.currentUserRole = this.componentRoles.PLANT_ADMIN;
                if (this.loggedInPlantIds.length === 0) {
                    console.error(`Plant Admin (Role ID ${roleId}, User ID ${this.loggedInAdminId}) has no plants assigned!`);
                    this.toast?.showErrorToast("User configuration error: Plant Admin has no assigned plants.");
                }
            } else {
                console.error(`Invalid or missing adminsRoleId (${roleId}) for User ID ${this.loggedInAdminId}.`);
                this.currentUserRole = '';
                this.toast?.showErrorToast("User configuration error: Invalid role.");
            }

            console.log(`User Role Determined: ${this.currentUserRole || 'None'}, Plant IDs: [${this.loggedInPlantIds.join(', ')}]`);

        } catch (error) {
            console.error("Error parsing user data from localStorage:", error);
            this.currentUserRole = '';
            this.loggedInAdminId = null;
            this.loggedInPlantIds = [];
            this.toast?.showErrorToast("Error reading user session. Please log in again.");
        }
    }

    initializeEditForm(): void {
        this.editUserForm = this.fb.group({
            enabled: [true],
            isFixedIt: [false],
            plantId: [null, Validators.required],
            zoneId: [null],
            qrCodeId: [null],
            type: [null],
            title: ['', [Validators.required, Validators.maxLength(200)]],
            fixedItDescription: ['', Validators.maxLength(200)],
            counselingPersonName: ['', [Validators.pattern(/^[a-zA-Z\s]*$/), Validators.maxLength(50)]],
            thirdPartyCompanyName: ['', Validators.maxLength(100)],
            fullName: ['', [Validators.pattern(/^[a-zA-Z\s]*$/), Validators.maxLength(50)]],
            email: ['', [Validators.email]],
            contactNumber: ['', [Validators.pattern(/^[0-9]{10}$/), Validators.maxLength(10)]]
        });

        // Add conditional validation for fixedItDescription when isFixedIt is true
        this.editUserForm.get('isFixedIt')?.valueChanges.subscribe(isFixedIt => {
            const fixedItDescControl = this.editUserForm.get('fixedItDescription');
            if (isFixedIt) {
                fixedItDescControl?.setValidators([Validators.required, Validators.maxLength(200)]);
            } else {
                fixedItDescControl?.clearValidators();
                fixedItDescControl?.setValidators([Validators.maxLength(200)]);
            }
            fixedItDescControl?.updateValueAndValidity();
        });
    }

    async getPlants() {
        const data = { sort: 'name,ASC', filter: ['enabled||eq||true'], limit: 1000 };
        const param = createAxiosConfig(data);
        let allEnabledPlants: Plant[] = [];
        try {
            const response = await this.plantService.getPlants(param);
            allEnabledPlants = response?.data ?? response ?? [];
            console.log("Fetched all enabled plants:", allEnabledPlants.length);
        } catch (error) {
            console.error("Error fetching plants:", error);
            this.plants = [];
            this.toast?.showErrorToast('Failed to load plants.');
            return;
        }

        if (this.currentUserRole === this.componentRoles.PLANT_ADMIN && this.loggedInPlantIds.length > 0) {
            this.plants = allEnabledPlants.filter(plant =>
                this.loggedInPlantIds.includes(plant.id)
            );
            console.log(`Plant Admin: Filtered ${this.plants.length} plants for dropdown.`);
             if (this.plants.length !== this.loggedInPlantIds.length) {
                  console.warn(`Some assigned plants might be disabled or not found.`);
             }
        } else if (this.currentUserRole === this.componentRoles.SUPER_ADMIN) {
            this.plants = allEnabledPlants;
             console.log(`Super Admin: Showing all ${this.plants.length} enabled plants in dropdown.`);
        } else {
            this.plants = [];
            console.log("No plants available for current user role/assignment for dropdown.");
        }
    }

    async loadAssignableAdmins() {
        const data = { sort: 'firstName,ASC', filter: ['enabled||eq||true', 'status||eq||1'], limit: 1000 };
        const param = createAxiosConfig(data);
        try {
            const response = await this.adminService.getAdmin(param);
            this.availableAdminsForAssign = response?.data ?? [];
        } catch (error) { console.error("Error fetching assignable admins:", error); this.availableAdminsForAssign = []; }
    }

    async loadRelatesToOptions() {
        const data = { sort: 'title,ASC', filter: ['enabled||eq||true'], limit: 1000 };
        const param = createAxiosConfig(data);
        try {
            const response = await this.relatesToService.getRelatesTo(param);
            this.availableRelatesTo = response?.data ?? [];
        } catch (error) { console.error("Error fetching relatesTo options:", error); this.availableRelatesTo = []; }
    }

    async loadZones(plantId: number, target: 'filter' | 'edit'): Promise<void> {
        const targetListProperty = target === 'filter' ? 'availableZones' : 'editAvailableZones';
        this[targetListProperty] = [];
        const data = { sort: 'zoneName,ASC', filter: [`plantId||eq||${plantId}`, 'enabled||eq||true'], limit: 1000 };
        const params = createAxiosConfig(data);
        try {
            const response = await this.zoneService.getZone(params);
            this[targetListProperty] = response ?? [];
        } catch (error) { console.error(`Error fetching zones for plant ${plantId}:`, error); this[targetListProperty] = []; }
    }

    async loadQrCodesForZone(zoneId: number | null) {
        this.availableQrCodes = [];
        if (!zoneId) return;
        const data = { filter: [`zoneId||eq||${zoneId}`, 'enabled||eq||true'], limit: 1000 };
        const params = createAxiosConfig(data);
        try {
            const response = await this.qrCodeService.getQrCode(params);
            this.availableQrCodes = response ?? [];
        } catch (error) { console.error(`Error fetching QR codes for zone ${zoneId}:`, error); this.availableQrCodes = []; }
    }

    openFilterModal(): void { this.isFilterModalOpen = true; }
    closeFilterModal(): void { this.isFilterModalOpen = false; }

    applyFilters(): void {
        // Trim string values in filters
        if (this.filters.firstName) this.filters.firstName = this.filters.firstName.trim();
        if (this.filters.lastName) this.filters.lastName = this.filters.lastName.trim();
        if (this.filters.email) this.filters.email = this.filters.email.trim();
        if (this.filters.contactNumber) this.filters.contactNumber = this.filters.contactNumber.trim();

        // Check for date range validity
        if (this.filters.startDate && this.filters.endDate) {
            const startDate = new Date(this.filters.startDate);
            const endDate = new Date(this.filters.endDate);
            if (endDate < startDate) {
                this.toast?.showErrorToast("End date cannot be earlier than start date.");
                return;
            }
        }

        // Validate first name contains only alphabets if provided
        if (this.filters.firstName && !this.isValidName(this.filters.firstName)) {
            this.toast?.showErrorToast("First name should contain only alphabets.");
            return;
        }

        // Validate last name contains only alphabets if provided
        if (this.filters.lastName && !this.isValidName(this.filters.lastName)) {
            this.toast?.showErrorToast("Last name should contain only alphabets.");
            return;
        }

        // Validate email format if provided
        if (this.filters.email && !this.isValidEmail(this.filters.email)) {
            this.toast?.showErrorToast("Email must be an adani.com address.");
            return;
        }

        // Validate contact number if provided
        if (this.filters.contactNumber && !this.isValidContactNumber(this.filters.contactNumber)) {
            this.toast?.showErrorToast("Contact number must be 10 digits.");
            return;
        }

        this.currentPage = 1;
        this.loadObservationsForCurrentTab();
        this.closeFilterModal();
    }

    resetFilters(): void {
        this.filters = {
            firstName: null, lastName: null, email: null, contactNumber: null,
            startDate: null, endDate: null,
            plantId: null,
            enabled: true,
            sortDirection: 'DESC', sortField: 'id'
        };
        this.currentPage = 1;
        this.loadObservationsForCurrentTab();
    }

    openDeleteConfirmation(item: Observation): void {
        if (!item || !item.id) { this.toast?.showErrorToast("Invalid observation data for deletion."); return; }
        const observationPlantId = item.plant?.id ?? item.plantId;
        if (this.currentUserRole === this.componentRoles.PLANT_ADMIN &&
            (!observationPlantId || !this.loggedInPlantIds.includes(observationPlantId))) {
            this.toast?.showErrorToast("You do not have permission to delete observations for this plant.");
            return;
        }
        this.itemToDelete = item;
        this.deleteConfirmationModalInstance?.show();
    }

    closeDeleteConfirmation(): void {
        this.deleteConfirmationModalInstance?.hide();
        this.itemToDelete = null;
    }

    confirmDelete(): void {
        if (!this.itemToDelete) { this.toast?.showErrorToast("Action failed: No item selected."); this.closeDeleteConfirmation(); return; }
        const itemToDeleteNow = this.itemToDelete;
        this.closeDeleteConfirmation();
        this.executeDelete(itemToDeleteNow);
    }

    async executeDelete(item: Observation): Promise<void> {
        console.log("Executing delete action on:", item);
        const payload = { tableName: 'observation', id: item.id };
        this.listLoading = true;
        try {
            await this.deleteService.deleteData(payload);
            this.toast?.showSuccessToast(`Observation ID ${item.id} deleted successfully.`);
            if ((this.getCurrentListData()?.length === 1) && this.currentPage > 1) { this.currentPage--; }
            this.loadObservationsForCurrentTab();
        } catch (error: any) {
            console.error(`Error deleting observation ${item.id}:`, error);
            this.toast?.showErrorToast(error?.response?.data?.message || 'Failed to delete observation.');
        } finally { this.listLoading = false; }
    }

    openUpdateStatusConfirmation(item: Observation, targetStatus: ObservationStatus): void {
        if (!item || item.id === undefined || (targetStatus !== ObservationStatus.Open && targetStatus !== ObservationStatus.Closed)) {
             this.toast?.showErrorToast("Invalid data or target status for update."); return;
        }
        const observationPlantId = item.plant?.id ?? item.plantId;
        if (this.currentUserRole === this.componentRoles.PLANT_ADMIN &&
            (!observationPlantId || !this.loggedInPlantIds.includes(observationPlantId))) {
            this.toast?.showErrorToast("You do not have permission to update status for this observation.");
            return;
        }
        this.itemToUpdateStatus = item;
        this.targetStatusForUpdate = targetStatus;

        if (this.targetStatusForUpdate === ObservationStatus.Closed) {
            this.modalTitle = 'Confirm Closing Observation';
            this.modalMessage = `Are you sure you want to close Observation ID ${item.id}?`;
            this.confirmButtonText = 'Yes, Close';
            this.confirmButtonClass = 'bg-secondary';
            this.confirmIconClass = 'bi bi-lock-fill';
        } else {
            this.modalTitle = 'Confirm Reopening Observation';
            this.modalMessage = `Are you sure you want to reopen Observation ID ${item.id}?`;
            this.confirmButtonText = 'Yes, Reopen';
            this.confirmButtonClass = 'bg-primary';
            this.confirmIconClass = 'bi bi-unlock-fill';
        }
        this.updateStatusConfirmationModalInstance?.show();
    }

    closeUpdateStatusConfirmation(): void {
        this.updateStatusConfirmationModalInstance?.hide();
        this.itemToUpdateStatus = null;
        this.targetStatusForUpdate = null;
    }

    confirmUpdateStatus(): void {
        if (!this.itemToUpdateStatus || this.targetStatusForUpdate === null) { this.toast?.showErrorToast("Action failed: Missing item or target status."); this.closeUpdateStatusConfirmation(); return; }
        const itemToUpdateNow = this.itemToUpdateStatus;
        const targetStatus = this.targetStatusForUpdate;
        this.closeUpdateStatusConfirmation();
        this.executeUpdateStatus(itemToUpdateNow, targetStatus);
    }

    async executeUpdateStatus(item: Observation, newStatus: ObservationStatus): Promise<void> {
        console.log(`Executing update status action on: ${item.id} to status ${newStatus}`);
        const payload = {
            tableName: 'observation',
            id: item.id,
            data: { status: newStatus },
            createdBy: this.loggedInAdminId
        };
        this.listLoading = true;
        try {
            await this.updateService.update(payload);
            const actionText = newStatus === ObservationStatus.Closed ? 'closed' : 'reopened';
            this.toast?.showSuccessToast(`Observation ID ${item.id} status ${actionText} successfully.`);
            this.loadObservationsForCurrentTab();
        } catch (error: any) {
            console.error(`Error updating status for observation ${item.id}:`, error);
            this.toast?.showErrorToast(error?.response?.data?.message || 'Failed to update observation status.');
        } finally { this.listLoading = false; }
    }

    openRewardConfirmation(item: Observation): void {
        if (!item || !item.id) { this.toast?.showErrorToast("Invalid observation data for reward."); return; }
        const observationPlantId = item.plant?.id ?? item.plantId;
        if (this.currentUserRole === this.componentRoles.PLANT_ADMIN &&
            (!observationPlantId || !this.loggedInPlantIds.includes(observationPlantId))) {
            this.toast?.showErrorToast("You do not have permission to reward observations for this plant.");
            return;
        }
        this.itemToReward = item;
        this.rewardConfirmationModalInstance?.show();
    }

    closeRewardConfirmation(): void {
        this.rewardConfirmationModalInstance?.hide();
        this.itemToReward = null;
    }

    confirmReward(): void {
        if (!this.itemToReward) { this.toast?.showErrorToast("Action failed: No item selected."); this.closeRewardConfirmation(); return; }
        const itemToRewardNow = this.itemToReward;
        this.closeRewardConfirmation();
        this.executeReward(itemToRewardNow);
    }

    async executeReward(item: Observation): Promise<void> {
        const data = { id: item.id };
        this.listLoading = true;
        try {
            const response = await this.observationService.approveReward(data);
            if (response.responseCode == 200) {
                this.toast?.showSuccessToast(response.message || `Reward granted for Observation ID ${item.id}.`);
                this.loadObservationsForCurrentTab();
            } else {
                this.toast?.showErrorToast(response.message || "Failed to grant reward.");
            }
        } catch (error: any) {
            console.error("Error approving reward:", error);
            this.toast?.showErrorToast(error?.response?.data?.message || "An error occurred while granting the reward.");
        } finally { this.listLoading = false; }
    }

    getCurrentListData(): Observation[] | undefined {
        const currentTab = this.tabs[this.selectedTabIndex];
        if (currentTab?.listKey) { return (this as any)[currentTab.listKey]; }
        console.warn("Could not get list data for download.");
        return undefined;
    }

    async fetchAllFilteredObservations(): Promise<Observation[] | null> {
        const currentTab = this.tabs[this.selectedTabIndex];
        if (!currentTab) { console.error("Invalid tab."); return null; }
        this.listLoading = true;
        const filterParams: string[] = [...(currentTab.statusFilter || [])];

        if (this.currentUserRole === this.componentRoles.PLANT_ADMIN) {
            if (this.loggedInPlantIds.length > 0) {
                if (this.filters.plantId && this.loggedInPlantIds.includes(this.filters.plantId)) {
                    filterParams.push(`plantId||eq||${this.filters.plantId}`);
                } else {
                    filterParams.push(`plantId||$in||${this.loggedInPlantIds.join(',')}`);
                }
            } else {
                filterParams.push(`plantId||eq||-1`);
            }
        } else if (this.currentUserRole === this.componentRoles.SUPER_ADMIN) {
            if (this.filters.plantId) {
                filterParams.push(`plantId||eq||${this.filters.plantId}`);
            }
        }

        if (this.filters.enabled !== null) filterParams.push(`enabled||eq||${this.filters.enabled}`);
        if (this.filters.firstName) filterParams.push(`admin.firstName||$contL||${this.filters.firstName}`);
        if (this.filters.lastName) filterParams.push(`admin.lastName||$contL||${this.filters.lastName}`);
        if (this.filters.email) filterParams.push(`admin.email||$contL||${this.filters.email}`);
        if (this.filters.contactNumber) filterParams.push(`admin.contactNumber||$contL||${this.filters.contactNumber}`);
        if (this.filters.startDate) filterParams.push(`createdTimestamp||gte||${this.filters.startDate}T00:00:00Z`);
        if (this.filters.endDate) filterParams.push(`createdTimestamp||lte||${this.filters.endDate}T23:59:59Z`);

        const data = {
            limit: 10000,
            sort: `${this.filters.sortField || 'id'},${this.filters.sortDirection || 'DESC'}`,
            filter: filterParams,
            join: ['admin', 'plant', 'zone', 'qrCode', 'relatesTos', 'assignToAdmin', 'updated', 'approve', 'rejected']
        };
        console.log("API Request Params (fetchAllObservations):", JSON.stringify(data, null, 2));

        try {
            const param = createAxiosConfig(data);
            const response = await this.observationService.getObservations(param);
            const observations = response?.data ?? (Array.isArray(response) ? response : []);
            return observations;
        } catch (error: any) {
            console.error("Error fetching all obs for export:", error);
            this.toast?.showErrorToast("Failed to fetch data for export.");
            return null;
        } finally {
            this.listLoading = false;
        }
    }

    async downloadExcel(type: 'current' | 'all') {
        const currentTab = this.tabs[this.selectedTabIndex];
        if (!currentTab || this.isDownloadingExcel || this.listLoading) return;

        this.isDownloadingExcel = true; this.downloadType = type;
        this.toast?.showSuccessToast(`Preparing download for ${type} observations...`);
        let dataToExport: Observation[] | null = null;
        try {
            if (type === 'all') { dataToExport = await this.fetchAllFilteredObservations(); }
            else { dataToExport = this.getCurrentListData() ?? null; }

            if (!dataToExport || dataToExport.length === 0) { this.toast?.showErrorToast(`No observations available.`); this.isDownloadingExcel = false; this.downloadType = null; return; }

            console.log(`Fetched ${dataToExport.length} observations for Excel export (${type}).`);

            const dataForExcel = dataToExport.map(obs => ({
                'ID': obs.id,
                'Status': obs.status === ObservationStatus.Open ? 'Open' : 'Closed',
                'Type': obs.type === 0 ? 'Safe Act' : (obs.type === 1 ? 'Unsafe Act' : (obs.type === 2 ? 'Unsafe Condition' : 'N/A')),
                'Title/Description': obs.title,
                'Relates To': obs.relatesTos?.map(r => r.title).join(', ') ?? '',
                'Reporter Name': obs.admin ? `${obs.admin.firstName} ${obs.admin.lastName}`.trim() : obs.fullName ?? 'Guest',
                'Reporter Email': obs.admin ? obs.admin.email : obs.email ?? 'N/A',
                'Reporter Contact': obs.admin ? obs.admin.contactNumber : obs.contactNumber ?? 'N/A',
                'Plant': obs.plant?.name ?? 'N/A',
                'Zone': obs.zone?.zoneName ?? 'N/A',
                'Zone Area (QR/Manual)': obs.qrCode?.zoneArea ?? obs.zoneArea ?? 'N/A',
                'Fixed It?': obs.isFixedIt ? 'Yes' : 'No',
                'Fixed Description': obs.fixedItDescription ?? '',
                'Fixed Images Count': obs.fixImages?.length ?? 0,
                'Assigned To': obs.assignToAdmin ? `${obs.assignToAdmin.firstName} ${obs.assignToAdmin.lastName}`.trim() : 'N/A',
                'Reward Granted?': obs.rewardGranted ? 'Yes' : 'No',
                '3rd Party Company': obs.thirdPartyCompanyName ?? '',
                'Person Counselled': obs.counselingPersonName ?? '',
                'Created At': obs.createdTimestamp ? new Date(obs.createdTimestamp).toLocaleString() : '',
                'Last Updated At': obs.updatedTimestamp ? new Date(obs.updatedTimestamp).toLocaleString() : '',
                'Images Count': obs.images?.length ?? 0,
            }));
            const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
            const wb: XLSX.WorkBook = XLSX.utils.book_new();
            const safeSheetName = currentTab.title.replace(/[^a-zA-Z0-9]/g, '').substring(0, 30);
            XLSX.utils.book_append_sheet(wb, ws, safeSheetName || 'Observations');
            const dateStr = new Date().toISOString().slice(0, 10);
            const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
            const fileName = `Observations_${safeSheetName}_${typeStr}_${dateStr}.xlsx`;
            XLSX.writeFile(wb, fileName);
            this.toast?.showSuccessToast(`Excel download started.`);
        } catch (error) {
            console.error(`Excel generation error (${type}):`, error);
            this.toast?.showErrorToast(`Excel generation failed.`);
        } finally {
            this.isDownloadingExcel = false; this.downloadType = null;
        }
    }

    async openEditModal(item: Observation): Promise<void> {
        if (!item) return;
        const observationPlantId = item.plant?.id ?? item.plantId;
        if (this.currentUserRole === this.componentRoles.PLANT_ADMIN &&
            (!observationPlantId || !this.loggedInPlantIds.includes(observationPlantId))) {
            this.toast?.showErrorToast("You do not have permission to edit observations for this plant.");
            return;
        }

        this.editExistingImages = [...(item.images ?? [])];
        this.editExistingFixImages = [...(item.fixImages ?? [])];
        this.editImagesToRemove = [];
        this.editFixImagesToRemove = [];
        this.editNewImageFiles = [];
        this.editNewFixImageFiles = [];
        this.editNewImagePreviews = []; // Initialize
        this.editNewFixImagePreviews = []; // Initialize

        this.selectedObservationForEdit = {
            id: item.id,
            enabled: item.enabled ?? true,
            isFixedIt: item.isFixedIt ?? false,
            adminId: item.admin?.id ?? null,
            admin: item.admin,
            fullName: !item.admin ? item.fullName : null,
            email: !item.admin ? item.email : null,
            contactNumber: !item.admin ? item.contactNumber : null,
            plantId: item.plant?.id ?? item.plantId ?? null,
            zoneId: item.zone?.id ?? item.zoneId ?? null,
            qrCodeId: item.qrCode?.id ?? item.qrCodeId ?? null,
            type: item.type ?? null,
            title: item.title ?? null,
            fixedItDescription: item.isFixedIt ? (item.fixedItDescription ?? null) : null,
            counselingPersonName: !item.admin?.id ? (item.counselingPersonName ?? null) : null,
            thirdPartyCompanyName: !item.admin?.id && item.isFixedIt ? (item.thirdPartyCompanyName ?? null) : null,
            images: this.editExistingImages,
            fixImages: this.editExistingFixImages,
            imagesToRemove: this.editImagesToRemove,
            fixImagesToRemove: this.editFixImagesToRemove,
            newImageFiles: this.editNewImageFiles,
            newFixImageFiles: this.editNewFixImageFiles
        };

        this.editUserForm.reset();
        this.editUserForm.patchValue({
            enabled: this.selectedObservationForEdit?.enabled,
            isFixedIt: this.selectedObservationForEdit?.isFixedIt,
            plantId: this.selectedObservationForEdit?.plantId,
            zoneId: this.selectedObservationForEdit?.zoneId,
            qrCodeId: this.selectedObservationForEdit?.qrCodeId,
            type: this.selectedObservationForEdit?.type,
            title: this.selectedObservationForEdit?.title,
            fixedItDescription: this.selectedObservationForEdit?.fixedItDescription,
            counselingPersonName: this.selectedObservationForEdit?.counselingPersonName,
            thirdPartyCompanyName: this.selectedObservationForEdit?.thirdPartyCompanyName,
            fullName: this.selectedObservationForEdit?.fullName,
            email: this.selectedObservationForEdit?.email,
            contactNumber: this.selectedObservationForEdit?.contactNumber,
        });

        this.isEditModalOpen = true;
        this.editLoading = false;
        this.availableQrCodes = [];
        this.editAvailableZones = [];

        if (this.selectedObservationForEdit?.plantId) {
            await this.loadZones(this.selectedObservationForEdit.plantId, 'edit');
            this.cdr.detectChanges(); // Trigger change detection after zones load
        }
        if (this.selectedObservationForEdit?.zoneId) {
            await this.loadQrCodesForZone(this.selectedObservationForEdit.zoneId);
            this.cdr.detectChanges(); // Trigger change detection after QR codes load
        }
    }

    closeEditModal(): void {
        this.isEditModalOpen = false;
        this.selectedObservationForEdit = null;
        this.editUserForm.reset();
        this.availableQrCodes = [];
        this.editAvailableZones = [];
        this.editExistingImages = [];
        this.editExistingFixImages = [];
        this.editImagesToRemove = [];
        this.editFixImagesToRemove = [];
        this.editNewImageFiles = [];
        this.editNewFixImageFiles = [];
        this.editNewImagePreviews = []; // Reset
        this.editNewFixImagePreviews = []; // Reset
    }

    async onEditPlantSelectionChange(event: Event): Promise<void> {
        const selectElement = event.target as HTMLSelectElement;
        const plantId = selectElement.value ? Number(selectElement.value) : null;
        this.editUserForm.patchValue({ zoneId: null, qrCodeId: null });
        this.editAvailableZones = [];
        this.availableQrCodes = [];
        if (plantId !== null) {
            await this.loadZones(plantId, 'edit');
        }
    }

    async onEditZoneChange(event: Event): Promise<void> {
        const selectElement = event.target as HTMLSelectElement;
        const zoneId = selectElement.value ? Number(selectElement.value) : null;
        this.editUserForm.patchValue({ qrCodeId: null });
        this.availableQrCodes = [];
        await this.loadQrCodesForZone(zoneId);
    }

    handleNewImageChange(event: Event): void {
        const element = event.target as HTMLInputElement;
        if (element.files) {
            const files = Array.from(element.files);
            this.editNewImageFiles.push(...files);
            files.forEach(file => {
                const reader = new FileReader();
                reader.onload = (e: any) => {
                    this.editNewImagePreviews.push(e.target.result);
                    this.cdr.detectChanges(); // Manually trigger change detection
                };
                reader.readAsDataURL(file);
            });
            element.value = ''; // Clear the input after selecting files
        }
    }
    removeNewImage(index: number): void {
        this.editNewImageFiles.splice(index, 1);
        this.editNewImagePreviews.splice(index, 1); // Also remove the preview URL
    }
    removeExistingImage(imageUrlToRemove: string, index: number): void {
        this.editImagesToRemove.push(imageUrlToRemove);
        this.editExistingImages.splice(index, 1);
    }
    handleNewFixImageChange(event: Event): void {
        const element = event.target as HTMLInputElement;
        if (element.files) {
            const files = Array.from(element.files);
            this.editNewFixImageFiles.push(...files);
            files.forEach(file => {
                const reader = new FileReader();
                reader.onload = (e: any) => {
                    this.editNewFixImagePreviews.push(e.target.result);
                    this.cdr.detectChanges(); // Manually trigger change detection
                };
                reader.readAsDataURL(file);
            });
            element.value = ''; // Clear the input after selecting files
        }
    }
    removeNewFixImage(index: number): void {
        this.editNewFixImageFiles.splice(index, 1);
        this.editNewFixImagePreviews.splice(index, 1); // Also remove the preview URL
    }
    removeExistingFixImage(imageUrlToRemove: string, index: number): void {
        this.editFixImagesToRemove.push(imageUrlToRemove);
        this.editExistingFixImages.splice(index, 1);
    }

    async submitEditForm(): Promise<void> {
        this.editUserForm.markAllAsTouched();
        if (this.editUserForm.invalid || !this.selectedObservationForEdit) {
            this.toast?.showErrorToast('Please fill all required fields correctly.');
            return;
        }
        const observationPlantId = this.editUserForm.get('plantId')?.value;
         if (this.currentUserRole === this.componentRoles.PLANT_ADMIN &&
              (!observationPlantId || !this.loggedInPlantIds.includes(observationPlantId))) {
              this.toast?.showErrorToast("You do not have permission to save changes for this plant.");
              return;
         }

        // Trim string values in the form
        const formValues = this.editUserForm.getRawValue();
        const stringFields = ['title', 'fixedItDescription', 'counselingPersonName', 'thirdPartyCompanyName', 'fullName', 'email', 'contactNumber'];
        stringFields.forEach(field => {
            if (formValues[field] && typeof formValues[field] === 'string') {
                this.editUserForm.get(field)?.setValue(formValues[field].trim());
            }
        });

        this.editLoading = true;

        // --- Image Upload Logic ---
        const uploadedImageUrls: string[] = [];
        const uploadedFixImageUrls: string[] = [];

        try {
            // Upload new regular images
            for (const file of this.editNewImageFiles) {
                const fileFormData = new FormData();
                fileFormData.append('file', file, file.name);
                const uploadResponse = await this.uploadService.upload(fileFormData);
                if (uploadResponse && typeof uploadResponse === 'string' && uploadResponse.trim() !== '') {
                    uploadedImageUrls.push(uploadResponse);
                } else {
                    throw new Error(`Invalid upload response for image ${file.name}: ${uploadResponse}`);
                }
            }

            // Upload new fixed images
            for (const file of this.editNewFixImageFiles) {
                const fileFormData = new FormData();
                fileFormData.append('file', file, file.name);
                const uploadResponse = await this.uploadService.upload(fileFormData);
                if (uploadResponse && typeof uploadResponse === 'string' && uploadResponse.trim() !== '') {
                    uploadedFixImageUrls.push(uploadResponse);
                } else {
                    throw new Error(`Invalid upload response for fixed image ${file.name}: ${uploadResponse}`);
                }
            }
        } catch (uploadError) {
            console.error('Error uploading images:', uploadError);
            this.toast?.showErrorToast('Failed to upload images. Observation update aborted.');
            this.editLoading = false;
            return;
        }
        // --- End Image Upload Logic ---


        const formData = new FormData();

        Object.keys(formValues).forEach(key => {
            const value = formValues[key];
            // Exclude image file arrays and imageToRemove arrays from direct form data append
            if (key !== 'relatesToIds' && key !== 'newImageFiles' && key !== 'newFixImageFiles' && value !== null && value !== undefined && value !== '') {
                 const valToSend = typeof value === 'boolean' ? String(value) : value;
                 formData.append(key, String(valToSend));
            }
        });

        if (!this.selectedObservationForEdit.adminId) {
             formData.append('fullName', formValues.fullName || '');
             formData.append('email', formValues.email || '');
             formData.append('contactNumber', formValues.contactNumber || '');
        }

        // Construct the final image arrays including existing (not removed) and newly uploaded images
        const finalImages = this.editExistingImages.filter(url => !this.editImagesToRemove.includes(url)).concat(uploadedImageUrls);
        const finalFixImages = this.editExistingFixImages.filter(url => !this.editFixImagesToRemove.includes(url)).concat(uploadedFixImageUrls);

        // The backend might still need imagesToRemove and fixImagesToRemove to handle deletions
        if (this.editImagesToRemove.length > 0) formData.append('imagesToRemove', JSON.stringify(this.editImagesToRemove));
        if (this.editFixImagesToRemove.length > 0) formData.append('fixImagesToRemove', JSON.stringify(this.editFixImagesToRemove));


        console.log("Submitting Observation Update FormData:", this.selectedObservationForEdit.id);
        // Log FormData contents for debugging (optional, can be noisy)
        // for (const pair of formData.entries()) { console.log(`${pair[0]}: ${pair[1]}`); }

        try {

            // Convert FormData to a plain object for the data payload
            const dataPayload: any = {};
            formData.forEach((value, key) => {
                // Note: FormData values are typically strings or Blobs (Files).
                // This conversion puts them into a plain object.
                // Convert boolean values back to their proper type
                if (key === 'enabled' || key === 'isFixedIt') {
                    dataPayload[key] = value === 'true';
                } else {
                    dataPayload[key] = value;
                }
            });

            // Add the image arrays directly to the dataPayload
            dataPayload['images'] = finalImages;
            dataPayload['fixImages'] = finalFixImages;

            // Check if the resulting dataPayload is empty (check for keys other than images/fixImages if needed, or just check overall)
            // A simple check for any key presence might be sufficient if images/fixImages can be empty arrays.
             if (Object.keys(dataPayload).length === 0) {
                 console.warn("Data payload is empty, not submitting update.");
                 this.toast?.showErrorToast('No changes detected to save.');
                 this.editLoading = false;
                 return; // Exit the function
             }


            const payload = {
                tableName: 'observation',
                id: this.selectedObservationForEdit.id,
                data: dataPayload, // Use the plain object here
            };
            await this.updateService.update(payload);
            this.toast?.showSuccessToast('Observation updated successfully!');
            this.closeEditModal();
            this.loadObservationsForCurrentTab();
        } catch (error: any) {
            console.error("Error updating observation:", error);
            this.toast?.showErrorToast(error?.response?.data?.message || 'Failed to update observation.');
        } finally {
            this.editLoading = false;
        }
    }

    openDetailsModal(item: Observation): void {
        const observationPlantId = item.plant?.id ?? item.plantId;
         if (this.currentUserRole === this.componentRoles.PLANT_ADMIN &&
             (!observationPlantId || !this.loggedInPlantIds.includes(observationPlantId))) {
             this.toast?.showErrorToast("You do not have permission to view details for this observation.");
             return;
         }
        this.selectedObservationForDetails = { ...item };
        this.isDetailsModalOpen = true;
    }
    closeDetailsModal(): void {
        this.isDetailsModalOpen = false;
        this.selectedObservationForDetails = null;
    }

    async fetchObservations(page: number, baseFilters: string[], currentFilters: ObservationFilter): Promise<{ data: Observation[], total: number } | null> {
        this.listLoading = true;
        const filterParams: string[] = [...baseFilters];

        // --- Apply Role-Based Plant Filtering ---
        // --- Apply Role-Based Plant Filtering ---
        if (this.currentUserRole === this.componentRoles.PLANT_ADMIN) {
            if (currentFilters.plantId !== null && currentFilters.plantId !== undefined) {
                // Plant Admin selected a specific plant from their list
                // Always filter by the selected plant ID if one is explicitly chosen.
                // The backend should enforce that the selected plant is one of their assigned plants.
                filterParams.push(`plantId||eq||${currentFilters.plantId}`);
                console.log("Plant Admin: Filtering by selected plant ID:", currentFilters.plantId);
            } else if (this.loggedInPlantIds.length > 0) {
                // Plant Admin selected "All My Plants" (or default) - filter by all their assigned plants
                // Assuming backend supports $in operator with comma-separated values
                filterParams.push(`plantId||$in||${this.loggedInPlantIds.join(',')}`);
                console.log("Plant Admin: Filtering by assigned plant IDs:", this.loggedInPlantIds);
            } else {
                // Plant Admin has no plants assigned - this case should ideally be prevented
                // or result in no data. Adding a filter that likely returns nothing.
                console.warn("Plant Admin has no assigned plants. Applying impossible filter.");
                filterParams.push(`plantId||eq||-1`); // Or handle as needed
            }
        } else if (this.currentUserRole === this.componentRoles.SUPER_ADMIN) {
            // Super Admin: Only filter if they explicitly selected a plant
            if (currentFilters.plantId !== null && currentFilters.plantId !== undefined) {
                filterParams.push(`plantId||eq||${currentFilters.plantId}`);
                console.log("Super Admin: Filtering by selected plant ID:", currentFilters.plantId);
            } else {
                 console.log("Super Admin: No specific plant selected, showing all.");
                 // No plant filter added
            }
        } else {
            // No role or unknown role - potentially restrict or show all? Showing all for now.
            console.warn("Unknown user role, not applying specific plant filters.");
            // No plant filter added by default
        }
        // --- End Role-Based Plant Filtering ---

        if (currentFilters.enabled !== null) filterParams.push(`enabled||eq||${currentFilters.enabled}`);
        if (currentFilters.firstName) filterParams.push(`admin.firstName||$contL||${currentFilters.firstName}`);
        if (currentFilters.lastName) filterParams.push(`admin.lastName||$contL||${currentFilters.lastName}`);
        if (currentFilters.email) filterParams.push(`admin.email||$contL||${currentFilters.email}`);
        if (currentFilters.contactNumber) filterParams.push(`admin.contactNumber||$contL||${currentFilters.contactNumber}`);
        if (currentFilters.startDate) filterParams.push(`createdTimestamp||gte||${currentFilters.startDate}T00:00:00Z`);
        if (currentFilters.endDate) filterParams.push(`createdTimestamp||lte||${currentFilters.endDate}T23:59:59Z`);

        const data = {
            page: page, limit: this.itemsPerPage,
            sort: `${currentFilters.sortField || 'id'},${currentFilters.sortDirection || 'DESC'}`,
            filter: filterParams,
            join: ['admin', 'plant', 'zone', 'qrCode', 'relatesTos', 'assignToAdmin', 'updated', 'approve', 'rejected']
        };
        console.log("API Request Params (fetchObservations):", JSON.stringify(data, null, 2));

        try {
            const param = createAxiosConfig(data);
            const response = await this.observationService.getObservations(param);
            const observations = response?.data ?? [];
            const total = response?.total ?? 0;
            return { data: observations, total: total };
        } catch (error: any) {
            console.error("Error fetching observations:", error);
            this.toast?.showErrorToast(error?.response?.data?.message || 'Failed to load observations.');
            this.clearCurrentTabData(); this.totalItems = 0; return null;
        }
        finally { this.listLoading = false; }
    }


    async loadObservationsForCurrentTab() {
        const currentTab = this.tabs[this.selectedTabIndex]; if (!currentTab) return;
        const result = await this.fetchObservations(this.currentPage, currentTab.statusFilter || [], this.filters);
        this.clearCurrentTabData(); this.totalItems = 0;
        if (result) {
            this.totalItems = result.total;
            (this as any)[currentTab.listKey] = result.data;
        }
    }

    clearCurrentTabData() {
        const currentTab = this.tabs[this.selectedTabIndex]; if (!currentTab) return;
        (this as any)[currentTab.listKey] = [];
    }

    onTabSelected(index: number) {
        if (this.selectedTabIndex === index || this.listLoading) return;
        this.selectedTabIndex = index;
        this.currentPage = 1;
        this.loadObservationsForCurrentTab();
    }
    onPageChange(page: number) {
        if (this.currentPage === page || this.listLoading) return;
        this.currentPage = page;
        this.loadObservationsForCurrentTab();
    }

    formatDate(dateString: string | Date | null | undefined, formatType: 'MMM' | 'from'): string {
        if (!dateString) return 'N/A';
        try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return 'Invalid Date';
            if (formatType === 'MMM') return date.toLocaleString('en-US', { month: 'short' });
            if (formatType === 'from') {
                const seconds = Math.floor((+new Date() - +date) / 1000);
                if (seconds < 29) return 'Just now';
                const intervals = [ { label: 'year', seconds: 31536000 }, { label: 'month', seconds: 2592000 }, { label: 'day', seconds: 86400 }, { label: 'hour', seconds: 3600 }, { label: 'minute', seconds: 60 }, { label: 'second', seconds: 1 }];
                for (const interval of intervals) { const count = Math.floor(seconds / interval.seconds); if (count >= 1) return `${count} ${interval.label}${count > 1 ? 's' : ''} ago`; }
                return 'Just now';
            }
            return date.toLocaleDateString();
        } catch (e) { console.error("Error formatting date:", dateString, e); return "Error Date"; }
    }

    // --- Validation Helper Methods ---
    private isValidName(name: string): boolean {
        const nameRegex = /^[a-zA-Z\s]*$/;
        return nameRegex.test(name);
    }

    private isValidEmail(email: string): boolean {
        const emailRegex = /^[a-zA-Z0-9._%+-]+@adani\.com$/;
        return emailRegex.test(email);
    }

    private isValidContactNumber(contactNumber: string): boolean {
        const contactRegex = /^[0-9]{10}$/;
        return contactRegex.test(contactNumber);
    }

    // --- Helper to trim input fields ---
    trimInputField(formData: any, fieldName: string): void {
        if (formData && formData[fieldName] && typeof formData[fieldName] === 'string') {
            formData[fieldName] = formData[fieldName].trim();
        }
    }
}