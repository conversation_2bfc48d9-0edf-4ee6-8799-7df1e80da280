import { ApplicationModule, Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { AuthService } from '../../services/auth.service';
import { NgOtpInputComponent, NgOtpInputModule } from 'ng-otp-input';
import { Router, ActivatedRoute } from '@angular/router';
import { ToastMessageComponent } from "../../shared/toast-message/toast-message.component";
import { LoadingOverlayComponent } from "../../shared/loading-overlay/loading-overlay.component";
import { CommonModule } from '@angular/common';
import { adaniDomainValidator } from '../../core/utilities/adani-email-validator';
import { DepartmentService } from '../../services/master-management/department/department.service';
import { DesignationService } from '../../services/master-management/designation/designation.service';
import { PlantManagementService } from '../../services/plant-management/plant-management.service';
import { createAxiosConfig } from '../../core/utilities/axios-param-config';
import DepartmentModel from '../../model/department.model';
import { DesignationModel } from '../../model/designation.model';
import { PlantModel } from '../../model/plant.model';
import { ApiService } from '../../services/api.service';
import { BusinessUnitModel } from '../../model/business-unit.model';
import { AdminService } from '../../services/admin/admin.service';
import { SamlService } from '../../services/saml/saml.service';

@Component({
    selector: 'app-login',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule, // Make sure this is imported
        NgOtpInputModule,
        ToastMessageComponent,
        LoadingOverlayComponent
    ],
    templateUrl: './login.component.html',
    styleUrls: ['./login.component.scss'] // Corrected property name
})
export class LoginComponent implements OnInit {
    @ViewChild(NgOtpInputComponent, { static: false }) ngOtpInput!: NgOtpInputComponent;
    @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;
    loginForm!: FormGroup;
    signUpForm!: FormGroup; // Initialize here or in constructor
    verifyOtpForm: any = {};
    isSignUp = false;
    sentOtp = false;
    loading = false;
    signUpLoading = false; // Use this for signup process
    genderList = [
        { label: 'Male', value: '1' },
        { label: 'Female', value: '0' },
        { label: 'Other', value: '2' }
    ];
    departmentList: any[] = [];
    designationList: any[] = [];
    plantsList: PlantModel[] = [];
    businessUnitsList: BusinessUnitModel[] = [];
    filteredDepartmentList: any[] = [];
    filteredDesignationList: any[] = [];
    filteredPlantsList: PlantModel[] = [];
    selectedBusinessUnitId: number | null = null;
    selectedBusinessUnitName: string = ''; // Added to store the name for the dialog
    cannotLoginDialog = false;
    cannotLoginData: any = {};
    isEmailExist = false; // Add logic to set this in checkMailExist()
    activeTab = 0;
    signUpResponseCode = 0;
    resentLoading = false; // Renamed for clarity
    email: any;
    showConfirmationDialog = false; // Added for confirmation dialog visibility
    registerConfirmationModalInstance: any = null;
    @ViewChild('registerConfirmationModalElement') registerConfirmationModalElement!: ElementRef;

    constructor(
        private fb: FormBuilder,
        private authService: AuthService,
        private router: Router,
        private route: ActivatedRoute,
        private departmentService: DepartmentService,
        private designationService: DesignationService,
        private plantService: PlantManagementService,
        private apiService: ApiService,
        private adminService: AdminService,
        private samlService: SamlService
    ) {
        this.loginForm = this.fb.group({
            email: ['', [Validators.required, Validators.email, adaniDomainValidator()]]
        });

        this.signUpForm = this.fb.group({
            // Step 1
            firstName: ['', [Validators.required, Validators.minLength(2)]],
            lastName: ['', [Validators.required, Validators.minLength(2)]],
            dob: ['', [Validators.required]],
            gender: ['', [Validators.required]],
            contactNumber: [
                '',
                [Validators.required, Validators.pattern('^[0-9]{10}$')]
            ],
            // Step 2
            businessUnitId: [null, Validators.required],
            email: ['', [Validators.required, Validators.email, adaniDomainValidator()]], // Add adaniDomainValidator if needed
            departmentId: ['', Validators.required],
            designationId: ['', Validators.required],
            plantIds: ['', Validators.required] // Changed name to match payload expectation (will be wrapped in array later)
        });
    }

    ngOnInit() {
        this.getBusinessUnits();
        this.checkForSamlCallback();
    }

    // Check for SAML callback with token
    private checkForSamlCallback() {
        this.route.queryParams.subscribe(params => {
            const token = this.samlService.extractTokenFromUrl();
            if (token) {
                console.log('SAML Token received:', token);
                this.handleSamlCallback(token);
            }
        });
    }

    // Handle SAML callback with token
    private async handleSamlCallback(token: string) {
        this.loading = true;
        try {
            const result = await this.samlService.handleSamlCallback(token, '/home');
            
            if (result.success) {
                this.toast.showSuccessToast('SAML Login Successful!');
            } else {
                this.toast.showErrorToast(result.message);
            }
        } catch (error) {
            console.error('SAML callback error:', error);
            this.toast.showErrorToast('SAML login failed');
        } finally {
            this.loading = false;
        }
    }

    ngAfterViewInit() {
        if (this.registerConfirmationModalElement) {
            // Bootstrap 5 Modal
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            const Modal = (window as any).bootstrap?.Modal;
            if (Modal) {
                this.registerConfirmationModalInstance = new Modal(this.registerConfirmationModalElement.nativeElement);
            }
        }
    }

    handleLogin() {
        this.loginForm.markAllAsTouched(); // Mark fields for validation messages
        if (this.loginForm.valid) {
            this.loading = true;
            this.email = this.loginForm.value.email;
            // Optional: Auto-append @adani.com if needed and valid
            // if (!this.email.endsWith('@adani.com') && this.email.indexOf('@') === -1) {
            //   this.email += '@adani.com';
            // } else if (!this.email.endsWith('@adani.com') && this.email.indexOf('@') > -1) {
            //    this.toast.showErrorToast('Please use your Adani domain email.');
            //    this.loading = false;
            //    return;
            // }

            this.authService.sendOtp({ email: this.email }).then((response) => {
                this.loading = false;
                console.log(response);
                if (response.responseCode == 404 || response.responseCode == 407) {
                    this.toast.showErrorToast(response.message);
                } else if (response.responseCode == 200) {
                    this.toast.showSuccessToast('OTP sent successfully');
                    this.sentOtp = true; // Show OTP section
                } else {
                    this.toast.showErrorToast('An unexpected error occurred sending OTP.');
                    console.error('OTP Send Error:', response);
                }
            }).catch(error => {
                 this.loading = false;
                 this.toast.showErrorToast('Failed to send OTP. Please try again.');
                 console.error('OTP Send Exception:', error);
            });
        } else {
            this.toast.showErrorToast('Please enter a valid email address.');
        }
    }


    async handleSSOLogin() {
        this.loginForm.markAllAsTouched();
        if (this.loginForm.valid) {
            const email = this.loginForm.value.email;
            console.log('Initiating SSO login for email:', email);
            
            try {
                this.loading = true;
                this.toast.showSuccessToast('Validating email for SSO access...');
                
                const result = await this.samlService.performSamlLogin(email);
                
                if (!result.success) {
                    this.toast.showErrorToast(result.message);
                } else {
                    this.toast.showSuccessToast('Redirecting to SAML login...');
                    // If successful, user will be redirected to SAML provider after delay
                }
            } catch (error) {
                console.error('Error during SSO login:', error);
                this.toast.showErrorToast('Error during SSO login. Please try again.');
            } finally {
                // Keep loading true for a bit longer to show the redirect message
                setTimeout(() => {
                    this.loading = false;
                }, 1000);
            }
        } else {
            this.toast.showErrorToast('Please enter a valid email address.');
        }
    }

    onOtpChange(otpValue: string) {
        if (otpValue.length === 4) { // Check length from config
            this.loading = true; // Use loading indicator
            this.authService.verifyOtp({ email: this.email, otp: otpValue }).then((response) => {
                this.loading = false;
                if (response?.responseCode == 400) { // Use optional chaining
                    this.ngOtpInput?.setValue(""); // Use optional chaining
                    this.toast.showErrorToast('Invalid OTP');
                } else if (response?.responseCode == 200) {
                    // Store token/user data from response if needed
                    // localStorage.setItem('authToken', response.data.token); // Example
                    this.toast.showSuccessToast('Login Successful!');
                    this.router.navigate(['/home']); // Redirect to homepage
                } else if (response?.responseCode == 407) {
                     this.cannotLoginData = response.data; // Assuming data contains admin details
                     this.cannotLoginDialog = true;
                     this.sentOtp = false; // Hide OTP input
                     this.loginForm.reset(); // Reset login form
                } else {
                    this.ngOtpInput?.setValue("");
                    this.toast.showErrorToast(response?.message || 'OTP verification failed.');
                }
            }).catch(error => {
                this.loading = false;
                this.ngOtpInput?.setValue("");
                this.toast.showErrorToast('Error verifying OTP. Please try again.');
                console.error('OTP Verify Exception:', error);
            });
        }
    }

    handleRegister() {
        this.isSignUp = !this.isSignUp;
        this.activeTab = 0; // Reset to first step when toggling
        this.signUpForm.reset(); // Clear form when toggling
        this.signUpResponseCode = 0; // Reset response code
    }

    // --- Signup Step Logic ---

    nextStep() {
        // Validate only Step 1 controls
        const step1Controls = ['firstName', 'lastName', 'dob', 'gender', 'contactNumber'];
        let isStep1Valid = true;

        step1Controls.forEach(controlName => {
            const control = this.signUpForm.get(controlName);
            control?.markAsTouched(); // Mark as touched to show errors
            if (control?.invalid) {
                isStep1Valid = false;
            }
        });

        if (isStep1Valid) {
            this.activeTab++;
        } else {
            this.toast.showErrorToast('Please fill all personal details correctly.');
        }
    }

    prevStep() {
        this.activeTab--;
    }

    // --- Final Signup Submission ---
    validateRegisterForm() {
        this.signUpForm.markAllAsTouched(); // Mark all fields to show errors if any

        if (this.signUpForm.invalid) {
             this.toast.showErrorToast('Please fill all required fields correctly.');
             // Find which step has the error and potentially switch back
             const step1Controls = ['firstName', 'lastName', 'dob', 'gender', 'contactNumber'];
             const step2Controls = ['email', 'departmentId', 'designationId', 'plantIds'];
             if (step1Controls.some(name => this.signUpForm.get(name)?.invalid)) {
                 this.activeTab = 0; // Go back to step 1 if error is there
             }
             return; // Stop submission
        }

        if (this.isEmailExist) {
            this.toast.showErrorToast('This email is already registered.');
            return; // Stop submission
        }

        // --- Construct Payload ---
        const formValue = this.signUpForm.value;
        const payload = {
            id: 0,
            enabled: true, // Defaulting to true, adjust if needed
            firstName: formValue.firstName,
            lastName: formValue.lastName,
            email: formValue.email,
            contactNumber: formValue.contactNumber,
            gender: formValue.gender, // Assuming form value is '0', '1', or '2'
            profilePicture: "",
            lastLogin: "", // Should be set by backend on login
            adminsRoleId: 3, // Hardcoded as per requirement
            adminsRole: {
                id: 3 // Hardcoded as per requirement
            },
            applicationId: 1, // Assuming 1 is the application ID
            status: 0, // Assuming 0 means pending approval
            // Ensure plantIds is an array
            plantIds: [parseInt(formValue.plantIds, 10)], // Convert to number and wrap in array
            // Construct the plant array of objects if required by backend
            plant: [{ id: parseInt(formValue.plantIds, 10) }],
            departmentId: parseInt(formValue.departmentId, 10),
            designationId: parseInt(formValue.designationId, 10),
            businessUnitId: parseInt(formValue.businessUnitId, 10),
            department: {
                id: parseInt(formValue.departmentId, 10)
            
            }, // Let backend handle relations
            designation: {
                id: parseInt(formValue.designationId, 10)
            }, // Let backend handle relations
            // Format DOB correctly
            dob: new Date(formValue.dob).toISOString(),
            createdBy: 0 // Assuming 0 for self-registration or handle appropriately
            // createdByFullName: "" // Usually set by backend based on logged-in user if applicable
        };

        console.log("Signup Payload:", payload); // For debugging

        // Store the selected business unit name for the dialog
        const selectedBu = this.businessUnitsList.find(bu => bu.id === parseInt(formValue.businessUnitId, 10));
        this.selectedBusinessUnitName = selectedBu ? selectedBu.title : 'N/A';

        // Show Bootstrap modal for confirmation
        if (this.registerConfirmationModalInstance) {
            this.registerConfirmationModalInstance.show();
        }
    }

    confirmRegistration() {
        this.showConfirmationDialog = false; // Close dialog
        this.signUpLoading = true;
        const formValue = this.signUpForm.value;
        const payload = {
            id: 0,
            enabled: true, // Defaulting to true, adjust if needed
            firstName: formValue.firstName,
            lastName: formValue.lastName,
            email: formValue.email,
            contactNumber: formValue.contactNumber,
            gender: formValue.gender, // Assuming form value is '0', '1', or '2'
            profilePicture: "",
            lastLogin: "", // Should be set by backend on login
            adminsRoleId: 3, // Hardcoded as per requirement
            adminsRole: {
                id: 3 // Hardcoded as per requirement
            },
            applicationId: 1, // Assuming 1 is the application ID
            status: 0, // Assuming 0 means pending approval
            // Ensure plantIds is an array
            plantIds: [parseInt(formValue.plantIds, 10)], // Convert to number and wrap in array
            // Construct the plant array of objects if required by backend
            plant: [{ id: parseInt(formValue.plantIds, 10) }],
            departmentId: parseInt(formValue.departmentId, 10),
            designationId: parseInt(formValue.designationId, 10),
            businessUnitId: parseInt(formValue.businessUnitId, 10),
            department: {
                id: parseInt(formValue.departmentId, 10)
            
            }, // Let backend handle relations
            designation: {
                id: parseInt(formValue.designationId, 10)
            }, // Let backend handle relations
            // Format DOB correctly
            dob: new Date(formValue.dob).toISOString(),
            createdBy: 0 // Assuming 0 for self-registration or handle appropriately
            // createdByFullName: "" // Usually set by backend based on logged-in user if applicable
        };

        this.authService.signup(payload) // Ensure this method exists and accepts the payload
            .then((response) => {
                this.signUpLoading = false;
                this.signUpResponseCode = response?.responseCode || 500; // Store response code

                if (this.signUpResponseCode === 200) {
                    this.toast.showSuccessToast('Registration request submitted successfully!');
                    this.closeRegisterConfirmation();
                } else {
                    this.toast.showErrorToast(response?.message || 'Registration failed. Please try again.');
                    this.signUpResponseCode = 0; // Reset on failure to allow retry
                }
            })
            .catch(() => {
                this.signUpLoading = false;
                this.signUpResponseCode = 0; // Reset on error
                this.toast.showErrorToast('An error occurred during registration.');
                console.error("Signup Error:", "error");
            });
    }

    cancelRegistration() {
        // Hide Bootstrap modal for confirmation
        if (this.registerConfirmationModalInstance) {
            this.registerConfirmationModalInstance.hide();
        }
    }

    closeRegisterConfirmation() {
        this.cancelRegistration();
    }


    handleResendOTP() {
        if (!this.email) return; // Should not happen if OTP screen is visible
        this.resentLoading = true; // Use separate loading state for resend
        this.authService.sendOtp({ email: this.email }).then((response) => {
             this.resentLoading = false;
             if (response.responseCode == 200) {
                 this.toast.showSuccessToast('OTP resent successfully');
             } else {
                 this.toast.showErrorToast(response.message || 'Failed to resend OTP.');
             }
         }).catch(error => {
             this.resentLoading = false;
             this.toast.showErrorToast('Error resending OTP.');
             console.error('OTP Resend Exception:', error);
         });
    }

    back() {
        this.sentOtp = false; // Go back from OTP to email input
        this.loginForm.reset(); // Optionally reset email field
    }

    async checkMailExist() {
        const emailControl = this.signUpForm.get('email');
        if (emailControl?.valid) {
            const emailToCheck = emailControl.value.toLowerCase().trim();
            try {
                const exists = await this.checkEmailExist(emailToCheck);
                if (exists) {
                    this.isEmailExist = true;
                    emailControl.setErrors({ ...emailControl.errors, emailTaken: true });
                    this.toast.showErrorToast('This email is already registered.');
                } else {
                    this.isEmailExist = false;
                    // Remove only the emailTaken error if present
                    if (emailControl.hasError('emailTaken')) {
                        const errors = { ...emailControl.errors };
                        delete errors['emailTaken'];
                        emailControl.setErrors(Object.keys(errors).length ? errors : null);
                    }
                }
            } catch (err) {
                this.isEmailExist = false;
                // Optionally show a toast or log error
                console.error("Error checking email:", err);
            }
        } else {
            this.isEmailExist = false; // Reset if email format is invalid
        }
    }

    private async checkEmailExist(email: string): Promise<boolean> {
        const data = { email };
        try {
            const response: any = await this.adminService.checkEmailAndRole(data);
            console.log(response); // Check if the structure is as expected
            if (response?.responseCode === 200) {
                return true;
            } else {
                return false;
            }
        } catch (error) {
            console.error('Error checking email existence:', error);
            return false;
        }
    }

    handleClose() {
        this.cannotLoginDialog = false;
    }

    // --- Master Data Fetching ---
    getBusinessUnits() {
        this.apiService.getData('/business-unit').then((response) => {
            this.businessUnitsList = response || [];
            // Optionally auto-select the first business unit
            // Do not auto-select business unit or load departments/designations on init
            // The user will select a business unit, which will trigger onBusinessUnitChange
        }).catch(err => console.error("Error fetching business units:", err));
    }

    onBusinessUnitChange(businessUnitId: number) {
        this.selectedBusinessUnitId = businessUnitId;

        // Clear previous selections and lists
        this.signUpForm.patchValue({
            departmentId: '',
            designationId: '',
            plantIds: ''
        });
        this.departmentList = [];
        this.filteredDepartmentList = [];
        this.designationList = [];
        this.filteredDesignationList = [];
        this.plantsList = [];
        this.filteredPlantsList = [];

        // Filter departments, designations, and plants based on businessUnitId
        this.getDepartments(businessUnitId);
        this.getDesignations(businessUnitId);
        this.getPlants(businessUnitId);
    }

    getDepartments(businessUnitId?: number) {
        const data = { page: 1, limit: 1000, sort: 'title,DESC', filter: ['enabled||eq||true'] };
        if (businessUnitId) {
            data.filter.push(`businessUnitId||eq||${businessUnitId}`);
        }
        this.departmentService.getDepartments(createAxiosConfig(data)).then((response) => {
            this.departmentList = response.data || [];
            this.filteredDepartmentList = this.departmentList;
        }).catch(err => console.error("Error fetching departments:", err));
    }

    getDesignations(businessUnitId?: number) {
        const data = { page: 1, limit: 1000, sort: 'title,ASC', filter: ['enabled||eq||true'] };
        if (businessUnitId) {
            data.filter.push(`businessUnitId||eq||${businessUnitId}`);
        }
        this.designationService.getDesignation(createAxiosConfig(data)).then((response) => {
            this.designationList = response?.data || [];
            this.filteredDesignationList = this.designationList;
        }).catch(err => console.error("Error fetching designations:", err));
    }

    getPlants(businessUnitId?: number) {
        const data = { page: 1, limit: 1000, sort: 'name,ASC', filter: ['enabled||eq||true'] };
        if (businessUnitId) {
            data.filter.push(`businessUnitId||eq||${businessUnitId}`);
        }
        this.plantService.getPlants(createAxiosConfig(data)).then((response) => {
            this.plantsList = response?.data || [];
            this.filteredPlantsList = this.plantsList;
        }).catch(err => console.error("Error fetching plants:", err));
    }
    
    // Contact number existence check for registration
    async checkContactExist() {
        const contactControl = this.signUpForm.get('contactNumber');
        if (contactControl?.valid) {
            const contactToCheck = contactControl.value.toLowerCase().trim();
            try {
                const result = await this._checkContactNumber(contactToCheck);
                if (result && result.exists) {
                    contactControl.setErrors({
                        ...contactControl.errors,
                        contactTaken: true,
                        contactTakenMsg: result.message ? result.message : 'This contact number is already registered.'
                    });
                    this.toast.showErrorToast(result.message ? result.message : 'This contact number is already registered.');
                } else {
                    // Remove only the contactTaken error if present
                    if (contactControl.hasError('contactTaken')) {
                        const errors = { ...contactControl.errors };
                        delete errors['contactTaken'];
                        contactControl.setErrors(Object.keys(errors).length ? errors : null);
                    }
                }
            } catch (err) {
                // Optionally show a toast or log error
                console.error("Error checking contact number:", err);
            }
        }
    }

    private async _checkContactNumber(contactNumber: string): Promise<{ exists: boolean, message?: string }> {
        const data = { contactNumber };
        try {
            const response: any = await this.adminService.checkContactNumber(data);
            console.log(response); // Check if the structure is as expected
            if (response?.responseCode === 200) {
                return { exists: false };
            } else if (response?.responseCode === 300) {
                return { exists: true, message: response.message || 'Contact number already present' };
            } else {
                return { exists: false };
            }
        } catch (error) {
            console.error('Error checking contact number existence:', error);
            return { exists: false };
        }
    }

    // --- Validation Helper ---
    isInvalid(fieldName: string): boolean {
        const control = this.signUpForm.get(fieldName);
        return !!(control && control.invalid && (control.touched || control.dirty));
    }

    getErrorMessage(fieldName: string): string | null {
        const control = this.signUpForm.get(fieldName);
        if (control && control.errors && (control.touched || control.dirty)) {
            if (control.errors['required']) return 'This field is required.';
            if (control.errors['email']) return 'Please enter a valid email address.';
            if (control.errors['minlength']) return `Minimum length is ${control.errors['minlength'].requiredLength}.`;
            if (control.errors['pattern']) return 'Invalid format. Please enter 10 digits.';
            if (control.errors['emailTaken']) return 'This email is already registered.'; // Custom error
            if (control.errors['contactTaken']) return control.errors['contactTakenMsg'] || 'This contact number is already registered.';
            // Add more specific errors as needed
            return 'Invalid input.'; // Generic fallback
        }
        return null;
    }
}