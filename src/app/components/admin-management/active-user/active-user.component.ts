import { CommonModule } from '@angular/common';
import { Component, OnInit, ViewChild, ElementRef, inject, AfterViewInit, OnDestroy } from '@angular/core'; // Added inject, AfterViewInit, OnDestroy
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators, AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { AdminService } from '../../../services/admin/admin.service';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { PaginationComponent } from "../../../shared/pagination/pagination.component";
import { OffcanvasComponent } from "../../../shared/offcanvas/offcanvas.component";
import { PlantManagementService } from '../../../services/plant-management/plant-management.service';
import { DesignationService } from '../../../services/master-management/designation/designation.service';
import { DepartmentService } from '../../../services/master-management/department/department.service';
import { AdminModel } from "../../../model/admin.model"; // Assuming AdminModel exists
import { ToastMessageComponent } from "../../../shared/toast-message/toast-message.component";
import { UploadService } from '../../../services/upload/upload.service';
import { UpdateService } from '../../../services/update/update.service';
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap';
import * as XLSX from 'xlsx';
import { Modal } from 'bootstrap';
import { Subject } from 'rxjs'; // Keep Subject
import { AuthService } from '../../../services/auth.service';
import { takeUntil } from 'rxjs/operators'; // Keep takeUntil
import { NgSelectModule } from '@ng-select/ng-select';
import { AddDjpService } from '../../../services/add-djp/add-djp.service';

// Define ROLES constant
const ROLES = {
    SUPER_ADMIN: 'super_admin',
    PLANT_ADMIN: 'plant_admin',
};

// Interface for filter structure
interface UserFilter {
    firstName?: string | null;
    lastName?: string | null;
    email?: string | null;
    mobile?: string | null;
    plantId?: number | null; // Plant selected in filter
    designationId?: number | null;
    departmentId?: number | null;
    adminRoleId?: number | null;
    sortField?: string | null;
    sortDirection?: 'ASC' | 'DESC';
}

interface DeviceInfo {
    id: number;
    adminId: number;
    platform?: string | null;
    appVersion?: string | null;
    deviceName?: string | null;
    lastLogin?: string | Date | null;
    osVersion?: string | null;
}

interface ActiveUserItem {
    id: number;
    profilePicture?: string | null;
    firstName: string;
    lastName: string;
    email: string;
    contactNumber: string;
    dob?: string | Date | null;
    gender?: number | string | null;
    createdTimestamp?: string | Date;
    lastLogin?: string | Date | null;
    adminsRole?: { id?: number; name: string }; // Added ID for patching form
    plant?: { id?: number; name: string }[]; // Added ID for patching form
    department?: { id?: number; title: string }; // Added ID for patching form
    designation?: { id?: number; title: string }; // Added ID for patching form
    deviceDetails?: DeviceInfo | null;
    updatedTimestamp?: string | Date | null;
    enabled?: boolean;
}

interface SelectOption {
    id: number;
    name: string; // or title
}

interface AppOption {
    value: number;
    name: string;
}

interface DesignationOption {
    id: number;
    title: string;
}


// Custom validator for adani.com email domain
export function adaniEmailValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
        if (!control.value) {
            return null; // Let required validator handle empty values
        }

        const email = control.value.toLowerCase();
        const isAdaniEmail = email.endsWith('@adani.com');

        return isAdaniEmail ? null : { adaniDomain: true };
    };
}

@Component({
    selector: 'app-active-user',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        PaginationComponent,
        OffcanvasComponent,
        NgbDropdownModule,
        ToastMessageComponent,
        NgSelectModule // <-- Add NgSelectModule here
    ],
    templateUrl: './active-user.component.html',
    styleUrl: './active-user.component.scss'
})
export class ActiveUserComponent implements OnInit, AfterViewInit, OnDestroy { // Added AfterViewInit, OnDestroy
    public componentRoles = ROLES; // Expose roles

    @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;
    @ViewChild('transferUserModalElement') transferUserModalElement!: ElementRef;
    @ViewChild('deleteConfirmationModalElement') deleteConfirmationModalElement!: ElementRef;
    @ViewChild('newUserFileInput') newUserFileInput!: ElementRef;

    deleteConfirmationModalInstance: Modal | null = null;
    transferModalInstance: Modal | null = null;

    userToDelete: any | null = null;
    activeUserList: any[] = []; // Use specific type
    listLoading = false;
    isOffModalOpen = false;
    isDownloadingExcel = false;
    downloadType: 'current' | 'all' | null = null;
    deviceDetailsCache = new Map<number, DeviceInfo | null | 'loading'>();

    // Add User Properties
    isAddUserModalOpen = false;
    addUserForm!: FormGroup;
    addUserSubmitted = false;
    isAddingUser = false;
    newUserProfileImageUrl: string | ArrayBuffer | null = null;
    newUserSelectedFile: File | null = null;
    isNewUserEmailExist = false;
    availableRolesForNewUser: SelectOption[] = [];

    currentPage = 1;
    itemsPerPage = 10;
    totalItems = 0;

    filters: UserFilter = {
        firstName: null, lastName: null, email: null, mobile: null,
        plantId: null, designationId: null, departmentId: null, adminRoleId: null,
        sortField: 'id', sortDirection: 'DESC'
    };

    // Role-Based Access Control Properties
    currentUserRole: string = '';
    loggedInAdminId: number | null = null; // Changed from placeholder
    loggedInPlantIds: number[] = [];

    availablePlants: SelectOption[] = []; // Filtered by role for main list
    allAvailablePlants: SelectOption[] = []; // All plants for transfer functionality
    availableDesignations: DesignationOption[] = [];
    availableDepartments: any = []; // Use SelectOption
    availableAdminRoles: SelectOption[] = [{ id: 3, name: 'User' }, { id: 2, name: 'Plant Admin' }, { id: 1, name: 'Super Admin' }];
    availableSortFields = [{ value: 'id', label: 'ID' }, { value: 'firstName', label: 'First Name' }, { value: 'lastName', label: 'Last Name' }, { value: 'email', label: 'Email' }, { value: 'createdAt', label: 'Created Date' }];

    isEditUserModalOpen = false;
    editUserForm!: FormGroup;
    submitted = false;
    isSubmitting = false;
    profileImageUrl: string | ArrayBuffer | null = null;
    selectedFile: File | null = null;
    selectedUser: ActiveUserItem | null = null; // Use specific type

    transferUserForm!: FormGroup;
    currentUserForTransfer: any = null;
    currentUserPlantNames: string = 'N/A';
    availablePlantsForTransfer: any[] = [];
    isSubmittingTransfer: boolean = false;
    private allDeviceDetailsMap = new Map<number, DeviceInfo>();
    private isFetchingAllDeviceDetails = false;
    isDeviceDetailsLoading = true;

    applications: AppOption[] = [{ value: 1, name: 'App One' }, { value: 3, name: 'App Three' }];
    loggedInUserPlantName: any; // Keep if used elsewhere

    private destroy$ = new Subject<void>(); // Keep for potential subscriptions

    // Inject services
    private fb = inject(FormBuilder);
    private adminService = inject(AdminService);
    private plantService = inject(PlantManagementService);
    private designationService = inject(DesignationService);
    private departmentService = inject(DepartmentService);
    private updateService = inject(UpdateService);
    private uploadService = inject(UploadService);
    private authService = inject(AuthService);
    private djpService = inject(AddDjpService);

    constructor() { }

    async ngOnInit() {
        this.setCurrentUserRoleAndDetailsById(); // Set role first
        this.initializeEditForm();
        this.initializeTransferForm();
        this.initializeAddUserForm();
        this.setupAvailableRolesForNewUser();

        // Reset device details loading state
        this.isDeviceDetailsLoading = true;

        // Start loading users immediately to show loading indicator
        this.loadActiveUsers(this.currentPage);

        // Load dropdown data and device details in parallel
        const dropdownDataPromise = Promise.all([
            this.getPlants(), // Fetches plants based on role
            this.getDesignations(),
            this.getDepartments()
        ]);

        // Fetch device details in parallel without blocking the UI
        this.fetchAllDeviceDetails().then(() => {
            // Update the user list with device details when they become available
            if (this.activeUserList.length > 0) {
                this.activeUserList = this.mergeDeviceDetailsIntoList(this.activeUserList);
            }
        }).catch(error => {
            console.error("Error fetching device details:", error);
            this.isDeviceDetailsLoading = false;
        });

        try {
            await dropdownDataPromise;
            console.log('Dropdown data fetched.');
        } catch (error) {
            console.error("Error during dropdown data fetching:", error);
            this.toast?.showErrorToast("Failed to load dropdown data.");
        }
    }

    ngAfterViewInit(): void {
        if (this.deleteConfirmationModalElement) {
            this.deleteConfirmationModalInstance = new Modal(this.deleteConfirmationModalElement.nativeElement);
        } else { console.error("Delete confirmation modal element not found!"); }

        if (this.transferUserModalElement && !this.transferModalInstance) {
            this.transferModalInstance = new Modal(this.transferUserModalElement.nativeElement, { keyboard: false, backdrop: 'static' });
        }
    }

    ngOnDestroy(): void {
        this.transferModalInstance?.dispose();
        this.deleteConfirmationModalInstance?.dispose();
        this.destroy$.next(); // Complete subject for other subscriptions
        this.destroy$.complete();
    }

    private setCurrentUserRoleAndDetailsById() {
        try {
            const userString = localStorage.getItem('user');
            if (!userString || userString.trim() === "" || ['null', 'undefined', '""', '" "'].includes(userString.trim().toLowerCase())) {
                this.currentUserRole = ''; this.loggedInAdminId = null; this.loggedInPlantIds = [];
                this.toast?.showErrorToast("User session invalid."); return;
            }
            const currentUser = JSON.parse(userString);
            this.loggedInAdminId = currentUser?.id ?? null;
            this.loggedInPlantIds = (Array.isArray(currentUser?.plantIds) && currentUser.plantIds.length > 0)
                ? currentUser.plantIds.map((id: any) => Number(id)).filter((id: number) => !isNaN(id)) : [];
            const roleId = currentUser?.adminsRoleId;
            if (roleId === 1) { this.currentUserRole = ROLES.SUPER_ADMIN; }
            else if (roleId === 2) {
                this.currentUserRole = ROLES.PLANT_ADMIN;
                if (this.loggedInPlantIds.length === 0) { this.toast?.showErrorToast("Plant Admin has no assigned plants."); }
            } else { this.currentUserRole = ''; this.toast?.showErrorToast("Invalid user role."); }
            console.log(`Role: ${this.currentUserRole}, UserID: ${this.loggedInAdminId}, Plants: [${this.loggedInPlantIds.join(', ')}]`);
        } catch (error) {
            console.error("Error parsing user data:", error);
            this.currentUserRole = ''; this.loggedInAdminId = null; this.loggedInPlantIds = [];
            this.toast?.showErrorToast("Error reading user session.");
        }
    }

    openDeleteConfirmation(user: any): void {
        if (!user || !user.id) { this.toast?.showErrorToast("Invalid user data provided."); return; }
        // Role Check: Prevent Plant Admin from deleting users outside their plants?
        // This requires knowing the user's plant(s). Assuming deletion is allowed for now,
        // add checks here if needed based on user.plantUsers and loggedInPlantIds.
        console.log('Opening delete confirmation for user:', user);
        this.userToDelete = user;
        this.deleteConfirmationModalInstance?.show();
    }

    async confirmDelete(): Promise<void> {
        if (!this.userToDelete || !this.userToDelete.id) { this.toast?.showErrorToast("An internal error occurred."); this.closeDeleteConfirmation(); return; }
        // Add role check again before proceeding if necessary
        const userToDeleteNow = this.userToDelete;
        this.closeDeleteConfirmation();
        console.log(`Proceeding with delete operation for user ID: ${userToDeleteNow.id}`);
        this.listLoading = true;
        try {
            const response = await this.adminService.deletAdmin({ id: userToDeleteNow.id });
            if (response.responseCode == 200) {
                this.djpComplete(userToDeleteNow)
                this.toast?.showSuccessToast('User deleted successfully!');
                if (this.activeUserList.length === 1 && this.currentPage > 1) { this.currentPage--; }
                this.loadActiveUsers(this.currentPage);
            } else { this.toast?.showErrorToast(response.message || 'Failed to delete user.'); }
        } catch (error) {
            console.error(`Error deleting user ID: ${userToDeleteNow.id}`, error);
            this.toast?.showErrorToast('An error occurred while deleting the user.');
        } finally {
            this.listLoading = false; this.userToDelete = null;
        }
    }

    async djpComplete(deactiveUser: any) {
        try {
            const payload = { adminId: deactiveUser.id }
            await this.djpService.djpComplete(payload);
        }
        catch (error: any) {
            console.error(error);
        }
    }


    closeDeleteConfirmation(): void {
        this.deleteConfirmationModalInstance?.hide();
        this.userToDelete = null;
    }

    async fetchAllDeviceDetails(): Promise<void> {
        if (this.isFetchingAllDeviceDetails) return;
        this.isFetchingAllDeviceDetails = true;
        this.isDeviceDetailsLoading = true;
        console.log('Fetching all device details...');
        try {
            const data = { filter: ['enabled||eq||true'] }; // Adjust filter if needed
            const param = createAxiosConfig(data);
            const allDetails: DeviceInfo[] = await this.adminService.getDeviceDetails(param);
            this.allDeviceDetailsMap.clear();
            allDetails.forEach(device => { if (device.adminId != null) { this.allDeviceDetailsMap.set(device.adminId, device); } });
            console.log(`Stored ${this.allDeviceDetailsMap.size} device details in map.`);
        } catch (error) {
            console.error("Failed to fetch all device details:", error);
            this.toast?.showErrorToast("Could not load device details.");
            this.allDeviceDetailsMap.clear();
        } finally {
            this.isFetchingAllDeviceDetails = false;
            this.isDeviceDetailsLoading = false;
        }
    }

    private mergeDeviceDetailsIntoList(users: ActiveUserItem[]): ActiveUserItem[] {
        return users.map(user => ({ ...user, deviceDetails: this.allDeviceDetailsMap.get(user.id) ?? null }));
    }

    initializeTransferForm(): void {
        this.transferUserForm = this.fb.group({ targetPlantId: ['', Validators.required] });
    }

    openTransferModal(user: any) {
        if (!user) { this.toast?.showErrorToast("User data not found."); return; }

        // Get user's current plant IDs
        const currentUserPlants = user.plant || [];
        const currentUserPlantIds = currentUserPlants.map((pu: any) => pu.id).filter((id: any): id is number => id != null);

        // Check if user is a regular user (not plant admin or super admin)
        const userRoleId = user.adminsRole?.id;

        // Role-based permission check
        if (this.currentUserRole === ROLES.PLANT_ADMIN) {
            // Plant admin can only transfer regular users (role ID 3)
            if (userRoleId !== 3) {
                this.toast?.showErrorToast("You can only transfer regular users.");
                return;
            }

            // Check if the user belongs to at least one of the plant admin's plants
            const hasAccessToUser = currentUserPlantIds.some((pid: number) => this.loggedInPlantIds.includes(pid));

            if (!hasAccessToUser) {
                this.toast?.showErrorToast("You do not have permission to transfer this user.");
                return;
            }

            // No longer require plant admins to have multiple plants
            // Plant admins can now transfer users to any plant in the system
        }

        this.currentUserForTransfer = user;
        this.isSubmittingTransfer = false;
        this.transferUserForm.reset();

        this.currentUserPlantNames = currentUserPlants.map((pu: any) => pu.name).filter(Boolean).join(', ') || 'N/A';

        // Get all available plants for transfer (excluding current ones)
        // Both super admin and plant admin can transfer to any plant except current ones
        this.availablePlantsForTransfer = this.allAvailablePlants.filter(plant => !currentUserPlantIds.includes(plant.id));

        if (!this.transferModalInstance) {
            if (!this.transferUserModalElement) { console.error("Transfer modal element not found!"); this.toast?.showErrorToast("Could not initialize transfer dialog."); return; }
            this.transferModalInstance = new Modal(this.transferUserModalElement.nativeElement, { keyboard: false, backdrop: 'static' });
        }
        this.transferModalInstance.show();
    }

    closeTransferModal() {
        if (this.transferModalInstance) { this.transferModalInstance.hide(); }
        this.currentUserForTransfer = null; this.currentUserPlantNames = 'N/A';
        this.availablePlantsForTransfer = []; this.transferUserForm.reset();
    }

    async submitTransfer() {
        this.transferUserForm.markAllAsTouched();
        if (this.transferUserForm.invalid) { this.toast?.showErrorToast('Please select a plant.'); return; }
        if (!this.currentUserForTransfer || !this.currentUserForTransfer.id) { this.toast?.showErrorToast('User context lost.'); this.closeTransferModal(); return; }

        // Get user's current plant IDs for permission check
        const currentUserPlants = this.currentUserForTransfer.plant || [];
        const currentUserPlantIds = currentUserPlants.map((pu: any) => pu.id).filter((id: any): id is number => id != null);
        const targetPlantId = Number(this.transferUserForm.value.targetPlantId);

        // Double-check permissions before submitting
        if (this.currentUserRole === ROLES.PLANT_ADMIN) {
            // Verify user belongs to at least one of admin's plants
            const hasAccessToUser = currentUserPlantIds.some((pid: number) => this.loggedInPlantIds.includes(pid));
            if (!hasAccessToUser) {
                this.toast?.showErrorToast("Permission denied: You cannot transfer this user.");
                this.closeTransferModal();
                return;
            }

            // No verification needed for target plant
            // Plant admin can transfer to any plant in the system
        }

        this.isSubmittingTransfer = true;
        const userId = this.currentUserForTransfer.id;

        const data = { adminId: userId, plantId: targetPlantId, transferedByAdminId: this.loggedInAdminId };
        console.log('Submitting transfer request:', data);
        try {
            // Use the specific transfer request service call
            const response = await this.plantService.requestPlantTransfer(data);
            // Assuming response indicates success, show toast
            this.toast?.showSuccessToast(response?.message || "Transfer request has been sent successfully!"); // Use response message if available
            this.djpComplete(this.currentUserForTransfer)
            this.closeTransferModal();
            this.loadActiveUsers(this.currentPage); // Refresh list

        } catch (error: any) {
            console.error("Error requesting plant transfer:", error);
            this.toast?.showErrorToast(error?.response?.data?.message || 'Failed to send transfer request.');
        } finally {
            this.isSubmittingTransfer = false;
        }
    }

    getCurrentListData(): ActiveUserItem[] | undefined {
        return this.activeUserList;
    }

    async fetchAllFilteredActiveUsers(): Promise<ActiveUserItem[] | null> {
        this.listLoading = true;
        const filterParams: string[] = ['id||ne||1', 'status||eq||1'];
        const plantFilterPath = 'plant.id';

        // Apply Role-Based Plant Filter
        if (this.currentUserRole === ROLES.PLANT_ADMIN) {
            if (this.loggedInPlantIds.length > 0) {

                // *** START FIX (Same as above) ***
                const selectedPlantIdNum = this.filters.plantId != null ? Number(this.filters.plantId) : null;

                console.log('[fetchAllFiltered] Checking Plant Filter:');
                console.log('  Raw filters.plantId:', this.filters.plantId, typeof this.filters.plantId);
                console.log('  Converted selectedPlantIdNum:', selectedPlantIdNum, typeof selectedPlantIdNum);
                console.log('  loggedInPlantIds:', this.loggedInPlantIds);
                console.log('  Includes Check Result:', selectedPlantIdNum != null && this.loggedInPlantIds.includes(selectedPlantIdNum));

                if (selectedPlantIdNum != null && this.loggedInPlantIds.includes(selectedPlantIdNum)) {
                    filterParams.push(`${plantFilterPath}||eq||${selectedPlantIdNum}`);
                    console.log(`[fetchAllFiltered] Applying EQ filter for plant: ${selectedPlantIdNum}`);
                } else {
                    filterParams.push(`${plantFilterPath}||$in||${this.loggedInPlantIds.join(',')}`);
                    console.log(`[fetchAllFiltered] Applying IN filter for plants: ${this.loggedInPlantIds.join(',')}`);
                    // No need to reset filter UI value here
                }
                // *** END FIX ***

            } else {
                filterParams.push(`${plantFilterPath}||eq||-1`);
                console.log('[fetchAllFiltered] Plant Admin has no plants, blocking results.');
            }
        } else if (this.currentUserRole === ROLES.SUPER_ADMIN) {
            // Super Admin logic
            if (this.filters.plantId != null) {
                filterParams.push(`${plantFilterPath}||eq||${this.filters.plantId}`);
                console.log(`[fetchAllFiltered] Super Admin applying EQ filter for plant: ${this.filters.plantId}`);
            } else {
                console.log('[fetchAllFiltered] Super Admin showing all plants.');
            }
        }
        // End Role-Based Plant Filter

        // ... (rest of filter additions)
        if (this.filters.firstName) filterParams.push(`firstName||$contL||${this.filters.firstName}`);
        if (this.filters.lastName) filterParams.push(`lastName||$contL||${this.filters.lastName}`);
        if (this.filters.email) filterParams.push(`email||$contL||${this.filters.email}`);
        if (this.filters.mobile) filterParams.push(`contactNumber||$contL||${this.filters.mobile}`);
        if (this.filters.designationId != null) filterParams.push(`designationId||eq||${this.filters.designationId}`);
        if (this.filters.departmentId != null) filterParams.push(`departmentId||eq||${this.filters.departmentId}`);
        if (this.filters.adminRoleId != null) filterParams.push(`adminsRoleId||eq||${this.filters.adminRoleId}`);


        const data = {
            limit: 10000,
            sort: `${this.filters.sortField || 'id'},${this.filters.sortDirection || 'DESC'}`,
            filter: filterParams,
            join: ['adminsRole', 'department', 'designation', 'plantUsers.plant'] // Keep join as is for now
        };

        try {
            const param = createAxiosConfig(data);
            console.log("[fetchAllFiltered] Final API Request Params:", JSON.stringify(data, null, 2));
            const response = await this.adminService.getAdmin(param);
            // ... (rest of try/catch)
            let users: ActiveUserItem[] = response?.data ?? response ?? [];
            if (users.length > 0) {
                users = this.mergeDeviceDetailsIntoList(users);
            }
            return users;
        } catch (error: any) {
            console.error("Error fetching all active users for download:", error);
            this.toast?.showErrorToast(error?.response?.data?.message || "Failed to retrieve full data for download.");
            return null;
        } finally {
            this.listLoading = false;
        }
    }

    async downloadExcel(type: 'current' | 'all') {
        if (this.isDownloadingExcel || this.listLoading) return;

        this.isDownloadingExcel = true;
        this.downloadType = type;
        this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} users...`);

        let dataToExport: ActiveUserItem[] | null = null;

        try {
            if (type === 'all') { dataToExport = await this.fetchAllFilteredActiveUsers(); }
            else { dataToExport = this.getCurrentListData() ?? null; if (dataToExport === undefined) { dataToExport = null; } }

            if (dataToExport === null) { this.toast?.showErrorToast("Failed to retrieve data for download."); return; }
            if (!dataToExport || dataToExport.length === 0) { this.toast?.showErrorToast(`No users available to download.`); return; }

            console.log(`Fetched ${dataToExport.length} users for Excel export (${type}).`);

            const dataForExcel = dataToExport.map(user => ({
                'User ID': user.id, 'First Name': user.firstName, 'Last Name': user.lastName, 'Email': user.email, 'Contact Number': user.contactNumber,
                'Role': user.adminsRole?.name ?? 'N/A', 'DOB': user.dob ? new Date(user.dob).toLocaleDateString() : 'N/A',
                'Gender': user.gender === 1 ? 'Male' : (user.gender === 0 ? 'Female' : (user.gender === 2 ? 'Other' : 'N/A')),
                'Department': user.department?.title ?? 'N/A', 'Designation': user.designation?.title ?? 'N/A',
                'Assigned Plants': user.plant && user.plant.length > 0 ? user.plant.map(pu => pu.name).filter(Boolean).join(', ') : 'N/A',
                'Created At': user.createdTimestamp ? new Date(user.createdTimestamp).toLocaleString() : 'N/A',
                'Last Login': user.lastLogin ? new Date(user.lastLogin).toLocaleString() : 'N/A',
                'Status': user.enabled ? 'Active' : 'Inactive',
            }));

            const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
            const wb: XLSX.WorkBook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'ActiveUsers');

            const dateStr = new Date().toISOString().slice(0, 10);
            const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
            const fileName = `ActiveUsers_${typeStr}_${dateStr}.xlsx`;

            XLSX.writeFile(wb, fileName);
            this.toast?.showSuccessToast(`Excel file download started (${type}).`);

        } catch (error) {
            console.error(`Error generating Excel file (${type}):`, error);
            this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
        } finally {
            this.isDownloadingExcel = false; this.downloadType = null;
        }
    }

    initializeEditForm(): void {
        this.editUserForm = this.fb.group({
            adminsRoleId: ['', Validators.required], firstName: ['', [Validators.required, Validators.pattern(/^[a-zA-Z\s]{1,30}$/)]],
            lastName: ['', [Validators.required, Validators.pattern(/^[a-zA-Z\s]{1,30}$/)]], gender: ['', Validators.required],
            email: [{ value: '', disabled: true }, [Validators.required, Validators.email, adaniEmailValidator()]],
            contactNumber: ['', [Validators.required, Validators.pattern(/^[0-9]{10}$/)]],
            departmentId: ['', Validators.required],
            designationId: ['', Validators.required],
            plantIds: [[], Validators.required] // Added required validation for plant selection
        });
    }

    async getDesignations() {
        const data = { sort: 'title,ASC', filter: ['enabled||eq||true'] };
        try {
            const param = createAxiosConfig(data);
            const response = await this.designationService.getDesignation(param);
            this.availableDesignations = response ?? [];
        } catch (error) { console.error("Error fetching designations:", error); this.availableDesignations = []; this.toast?.showErrorToast('Failed to load designations.'); }
    }

    async getDepartments() {
        const data = { sort: 'title,ASC', filter: ['enabled||eq||true'] };
        try {
            const param = createAxiosConfig(data);
            const response = await this.departmentService.getDepartments(param);
            this.availableDepartments = response?.map((dept: any) => ({ id: dept.id, title: dept.title })) ?? [];
        } catch (error) { console.error("Error fetching departments:", error); this.availableDepartments = []; this.toast?.showErrorToast('Failed to load departments.'); }
    }

    async getPlants() {
        const data = { sort: 'name,ASC', filter: ['enabled||eq||true'], limit: 1000 }; // Fetch all for Super Admin initially
        let allEnabledPlants: SelectOption[] = [];
        try {
            const param = createAxiosConfig(data);
            const response = await this.plantService.getPlants(param);
            allEnabledPlants = response?.data?.map((p: any) => ({ id: p.id, name: p.name })) ?? response?.map((p: any) => ({ id: p.id, name: p.name })) ?? [];
        } catch (error) {
            console.error("Error fetching plants:", error);
            this.availablePlants = []; this.toast?.showErrorToast('Failed to load plants.'); return;
        }

        // For plant admins, we need to handle two scenarios:
        // 1. For filtering in the main list, they should only see their assigned plants
        // 2. For transfers, they need access to all plants

        // Store all plants for all users
        this.allAvailablePlants = [...allEnabledPlants];

        if (this.currentUserRole === ROLES.PLANT_ADMIN && this.loggedInPlantIds.length > 0) {
            // For filtering in the main list, only show plants the admin has access to
            this.availablePlants = allEnabledPlants.filter(plant => this.loggedInPlantIds.includes(plant.id));
        } else if (this.currentUserRole === ROLES.SUPER_ADMIN) {
            this.availablePlants = allEnabledPlants;
        } else {
            this.availablePlants = [];
        }
        console.log(`Loaded ${this.availablePlants.length} plants for filtering and ${this.allAvailablePlants.length} plants for transfers.`);
    }

    openFilterModal() { this.isOffModalOpen = true; }
    closeModal() { this.isOffModalOpen = false; }

    openEditModal(user: ActiveUserItem) { // Use specific type
        if (!user) { console.error('Cannot open edit modal: user data is missing.'); return; }

        // Check if plant admin is trying to edit a super admin
        if (this.currentUserRole === ROLES.PLANT_ADMIN && user.adminsRole?.id === 1) {
            this.toast?.showErrorToast("Plant admins cannot edit super admin users.");
            return;
        }

        // Role Check: Can Plant Admin edit users outside their plants? Assume no for now.
        const userPlantIds = user.plant?.map(pu => pu.id).filter(id => id != null) as number[] || [];
        if (this.currentUserRole === ROLES.PLANT_ADMIN && !userPlantIds.some(pid => this.loggedInPlantIds.includes(pid))) {
            if (userPlantIds.length > 0) { // Only show error if user *has* plants, just not the admin's
                this.toast?.showErrorToast("You do not have permission to edit users for this plant.");
                return;
            }
            // Allow editing if user has NO plants assigned? Or deny? Decide based on requirements.
            // console.warn("Plant Admin attempting to edit user with no assigned plants.");
        }

        const currentPlantIds = user.plant
                                ?.map(pu => pu.id) // Get plant IDs
                                .filter(id => id != null) as number[] || []; //
        console.log('current palnt id',currentPlantIds);
 
        this.selectedUser = user;
        this.submitted = false; this.isSubmitting = false; this.selectedFile = null;
        this.profileImageUrl = user.profilePicture || '../../../assets/svg/Avatar.svg';
        this.editUserForm.reset({}, { emitEvent: false });
        // Map gender to numeric string for select preselection based on HTML option values
        let genderValueForForm = '';
        if (user.gender !== undefined && user.gender !== null) {
            const numericGender = Number(user.gender); // Try to convert to number
            if (!isNaN(numericGender) && (numericGender === 0 || numericGender === 1 || numericGender === 2)) {
                genderValueForForm = String(numericGender); // If it's a valid number (0, 1, 2), use its string representation
            } else if (typeof user.gender === 'string') {
                // If it's a string like "Male", "Female", "Other", map it to 1, 0, 2 respectively
                switch (user.gender.toLowerCase()) {
                    case 'male':
                        genderValueForForm = '1';
                        break;
                    case 'female':
                        genderValueForForm = '0';
                        break;
                    case 'other':
                        genderValueForForm = '2';
                        break;
                    default:
                        genderValueForForm = ''; // Fallback for unknown string values
                }
            }
        }
        console.log(`[openEditModal] User gender from API: ${user.gender} (type: ${typeof user.gender})`);
        console.log(`[openEditModal] Mapped genderValueForForm: ${genderValueForForm} (type: ${typeof genderValueForForm})`);
 
        this.editUserForm.patchValue({
            adminsRoleId: user.adminsRole?.id ?? null, firstName: user.firstName, lastName: user.lastName,
            gender: genderValueForForm,
            email: user.email, contactNumber: user.contactNumber,
            departmentId: user.department?.id ?? null, // Use ID from nested object
            designationId: user.designation?.id ?? null, // Use ID from nested object
            plantIds: currentPlantIds
        });

        // Disable role dropdown for plant admin
        if (this.currentUserRole === ROLES.PLANT_ADMIN) {
            this.editUserForm.get('adminsRoleId')?.disable();
        } else {
            this.editUserForm.get('adminsRoleId')?.enable();
        }

        this.isEditUserModalOpen = true;
    }

    closeEditModal() {
        this.isEditUserModalOpen = false; this.selectedUser = null;
    }

    onFileChange(event: Event) {
        const element = event.currentTarget as HTMLInputElement;
        const fileList: FileList | null = element.files;
        if (fileList && fileList.length > 0) {
            const file = fileList[0];
            if (!file.type.startsWith('image/')) { this.toast?.showErrorToast('Please select a valid image file.'); element.value = ""; this.selectedFile = null; this.profileImageUrl = this.selectedUser?.profilePicture || '../../../assets/svg/Avatar.svg'; return; }
            this.selectedFile = file;
            const reader = new FileReader();
            reader.onload = (e) => { this.profileImageUrl = e.target?.result ?? null; };
            reader.readAsDataURL(this.selectedFile);
        } else { this.selectedFile = null; this.profileImageUrl = this.selectedUser?.profilePicture || '../../../assets/svg/Avatar.svg'; }
    }

    resetEditForm() { if (this.selectedUser) { this.openEditModal(this.selectedUser); } }

    async onEditSubmit() {
        this.submitted = true;
        if (this.editUserForm.invalid) { console.warn('Edit form is invalid.'); this.editUserForm.markAllAsTouched(); return; }
        if (!this.selectedUser || !this.selectedUser.id) { this.toast?.showErrorToast('Cannot update: User information is missing.'); return; }

        // Check if email has adani.com domain
        const email = this.selectedUser.email;
        if (!email.toLowerCase().endsWith('@adani.com')) {
            this.toast?.showErrorToast('Email must use the @adani.com domain.');
            return;
        }

        // Check if plant selection is required and provided
        let formValue = this.editUserForm.getRawValue();
        let selectedPlantIds = formValue.plantIds || [];
        if (this.currentUserRole === ROLES.SUPER_ADMIN && (!selectedPlantIds || selectedPlantIds.length === 0)) {
            this.toast?.showErrorToast('Please select at least one plant.');
            return;
        }

        // Add role check again before submitting
        const userPlantIds = this.selectedUser?.plant?.map(pu => pu.id).filter(id => id != null) as number[] || [];
        if (this.currentUserRole === ROLES.PLANT_ADMIN && !userPlantIds.some(pid => this.loggedInPlantIds.includes(pid))) {
            if (userPlantIds.length > 0) { this.toast?.showErrorToast("Permission denied to save changes for this user."); return; }
        }

        this.isSubmitting = true;
        let avatarUrlToUpdate: string | undefined | null = this.selectedUser?.profilePicture;

        if (this.selectedFile) {
            const fileFormData = new FormData();
            fileFormData.append('file', this.selectedFile, this.selectedFile.name);
            try {
                const uploadResponse = await this.uploadService.upload(fileFormData);
                if (uploadResponse && typeof uploadResponse === 'string') { avatarUrlToUpdate = uploadResponse; }
                else { throw new Error('Invalid upload response'); }
            } catch (uploadError) {
                console.error('Error uploading avatar:', uploadError); this.toast?.showErrorToast('Failed to upload new avatar.'); this.isSubmitting = false; return;
            }
        }

        // Update formValue and selectedPlantIds with latest values
        formValue = this.editUserForm.getRawValue();
        selectedPlantIds = formValue.plantIds || []; // Get selected plant IDs

        const updateData: { [key: string]: any } = {
            plantIds: selectedPlantIds,
            adminsRoleId: formValue.adminsRoleId, firstName: formValue.firstName, lastName: formValue.lastName,
            gender: formValue.gender, contactNumber: formValue.contactNumber, departmentId: formValue.departmentId,
            designationId: formValue.designationId, email: formValue.email, profilePicture: avatarUrlToUpdate ?? null
        };

        try {
            const updatePayload = { tableName: 'admins', id: this.selectedUser.id, data: updateData, createdBy: this.loggedInAdminId }; // Add createdBy
            await this.updateService.update(updatePayload);
            this.toast?.showSuccessToast('User updated successfully!');
            this.closeEditModal();
            this.loadActiveUsers(this.currentPage);
        } catch (updateError: any) {
            console.error('Error updating user record:', updateError);
            this.toast?.showErrorToast(updateError?.response?.data?.message || 'Failed to update user information.');
        } finally {
            this.isSubmitting = false;
        }
    }

    // Add User Methods
    initializeAddUserForm(): void {
        this.addUserForm = this.fb.group({
            adminsRoleId: ['', Validators.required],
            firstName: ['', [Validators.required, Validators.pattern(/^[a-zA-Z\s]{1,30}$/)]],
            lastName: ['', [Validators.required, Validators.pattern(/^[a-zA-Z\s]{1,30}$/)]],
            email: ['', [Validators.required, Validators.email, adaniEmailValidator()]],
            gender: ['', Validators.required],
            dob: ['', Validators.required],
            contactNumber: ['', [Validators.required, Validators.pattern(/^[0-9]{10}$/)]],
            departmentId: ['', Validators.required],
            designationId: ['', Validators.required],
            plantIds: [[], this.currentUserRole === ROLES.SUPER_ADMIN ? Validators.required : null],
            plantId: ['', this.currentUserRole === ROLES.PLANT_ADMIN ? Validators.required : null]
        });
    }

    setupAvailableRolesForNewUser(): void {
        // Super admin can create any role including super admin
        if (this.currentUserRole === ROLES.SUPER_ADMIN) {
            this.availableRolesForNewUser = [
                { id: 1, name: 'Super Admin' },
                { id: 2, name: 'Plant Admin' },
                { id: 3, name: 'User' }
            ];
        }
        // Plant admin can only create users
        else if (this.currentUserRole === ROLES.PLANT_ADMIN) {
            this.availableRolesForNewUser = [
                { id: 3, name: 'User' }
            ];
        }
    }

    openAddUserModal(): void {
        this.isAddUserModalOpen = true;
        this.addUserSubmitted = false;
        this.isAddingUser = false;
        this.newUserSelectedFile = null;
        this.newUserProfileImageUrl = '../../../assets/svg/Avatar.svg';
        this.isNewUserEmailExist = false;
        this.resetAddUserForm();
    }

    closeAddUserModal(): void {
        this.isAddUserModalOpen = false;
    }

    resetAddUserForm(): void {
        this.addUserForm.reset();
        // Set default role based on user type
        if (this.currentUserRole === ROLES.PLANT_ADMIN) {
            this.addUserForm.patchValue({
                adminsRoleId: 3 // User role
            });
        }
        this.newUserSelectedFile = null;
        this.newUserProfileImageUrl = '../../../assets/svg/Avatar.svg';
        this.isNewUserEmailExist = false;
    }

    onNewUserFileChange(event: Event): void {
        const element = event.currentTarget as HTMLInputElement;
        const fileList: FileList | null = element.files;
        if (fileList && fileList.length > 0) {
            const file = fileList[0];
            if (!file.type.startsWith('image/')) {
                this.toast?.showErrorToast('Please select a valid image file.');
                element.value = "";
                this.newUserSelectedFile = null;
                this.newUserProfileImageUrl = '../../../assets/svg/Avatar.svg';
                return;
            }
            this.newUserSelectedFile = file;
            const reader = new FileReader();
            reader.onload = (e) => {
                this.newUserProfileImageUrl = e.target?.result ?? null;
            };
            reader.readAsDataURL(this.newUserSelectedFile);
        } else {
            this.newUserSelectedFile = null;
            this.newUserProfileImageUrl = '../../../assets/svg/Avatar.svg';
        }
    }

    async checkEmailExists(): Promise<void> {
        const email = this.addUserForm.get('email')?.value;
        if (!email) {
            this.isNewUserEmailExist = false;
            return;
        }

        // Check for adani.com domain first
        if (!email.toLowerCase().endsWith('@adani.com')) {
            // Mark the field as touched to trigger validation display
            this.addUserForm.get('email')?.markAsTouched();
            return;
        }

        // Only check for existing email if it's a valid adani.com email
        try {
            // Check if email exists
            const data = { filter: [`email||eq||${email}`] };
            const param = createAxiosConfig(data);
            const response = await this.adminService.getAdmin(param);

            if (response && Array.isArray(response) && response.length > 0) {
                this.isNewUserEmailExist = true;
                this.toast?.showErrorToast('This email is already registered.');
            } else {
                this.isNewUserEmailExist = false;
            }
        } catch (error) {
            console.error('Error checking email existence:', error);
            this.isNewUserEmailExist = false;
        }
    }

    async onAddSubmit(): Promise<void> {
        this.addUserSubmitted = true;

        if (this.addUserForm.invalid) {
            console.warn('Add user form is invalid.');
            this.addUserForm.markAllAsTouched();
            return;
        }

        if (this.isNewUserEmailExist) {
            this.toast?.showErrorToast('This email is already registered.');
            return;
        }

        this.isAddingUser = true;
        let profilePictureUrl: string | null = null;

        // Upload profile picture if selected
        if (this.newUserSelectedFile) {
            const fileFormData = new FormData();
            fileFormData.append('file', this.newUserSelectedFile, this.newUserSelectedFile.name);
            try {
                const uploadResponse = await this.uploadService.upload(fileFormData);
                if (uploadResponse && typeof uploadResponse === 'string') {
                    profilePictureUrl = uploadResponse;
                } else {
                    throw new Error('Invalid upload response');
                }
            } catch (uploadError) {
                console.error('Error uploading avatar:', uploadError);
                this.toast?.showErrorToast('Failed to upload profile picture.');
                this.isAddingUser = false;
                return;
            }
        }

        // Prepare user data
        const formValue = this.addUserForm.getRawValue();

        // Handle plant IDs based on user role
        let plantIds: number[] = [];
        if (this.currentUserRole === ROLES.SUPER_ADMIN) {
            plantIds = formValue.plantIds || [];
        } else if (this.currentUserRole === ROLES.PLANT_ADMIN) {
            if (formValue.plantId) {
                plantIds = [Number(formValue.plantId)];
            } else {
                // If plant admin doesn't select a plant, use their own plant
                plantIds = this.loggedInPlantIds;
            }
        }

        // Create plant array with objects for each plant ID
        const plantArray = plantIds.map((id: number) => ({ id }));
        console.log('Add user - Plant array:', plantArray);

        const userData = {
            id: 0,
            enabled: true,
            firstName: formValue.firstName,
            lastName: formValue.lastName,
            email: formValue.email,
            contactNumber: formValue.contactNumber,
            gender: formValue.gender,
            profilePicture: profilePictureUrl,
            adminsRoleId: formValue.adminsRoleId,
            plantIds: plantIds, // Keep the original plantIds array
            plant: plantArray, // Add the new plant array with objects
            departmentId: formValue.departmentId,
            designationId: formValue.designationId,
            dob: new Date(formValue.dob).toISOString(),
            status: 1, // Active status
            createdBy: this.loggedInAdminId
        };

        try {
            // Log the full payload for debugging
            console.log('Add user - Full payload:', JSON.stringify(userData, null, 2));

            // Use auth service signup method
            const response = await this.authService.signup(userData);

            if (response && response.responseCode === 200) {
                this.toast?.showSuccessToast('User created successfully!');
                this.closeAddUserModal();
                this.loadActiveUsers(this.currentPage);
            } else {
                this.toast?.showErrorToast(response?.message || 'Failed to create user.');
            }
        } catch (error: any) {
            console.error('Error creating user:', error);
            this.toast?.showErrorToast(error?.response?.data?.message || 'Failed to create user.');
        } finally {
            this.isAddingUser = false;
        }
    }

    async loadActiveUsers(page: number) {
        this.listLoading = true;
        this.activeUserList = [];

        const filterParams: string[] = ['id||ne||1', 'status||eq||1'];
        const plantFilterPath = 'plant.id';

        // Apply Role-Based Plant Filter
        if (this.currentUserRole === ROLES.PLANT_ADMIN) {
            if (this.loggedInPlantIds.length > 0) {

                // *** START FIX ***
                // Convert the selected filter plant ID to a number for reliable comparison
                const selectedPlantIdNum = this.filters.plantId != null ? Number(this.filters.plantId) : null;

                // Add logging to verify types and values just before the check
                console.log('[loadActiveUsers] Checking Plant Filter:');
                console.log('  Raw filters.plantId:', this.filters.plantId, typeof this.filters.plantId);
                console.log('  Converted selectedPlantIdNum:', selectedPlantIdNum, typeof selectedPlantIdNum);
                console.log('  loggedInPlantIds:', this.loggedInPlantIds);
                console.log('  Includes Check Result:', selectedPlantIdNum != null && this.loggedInPlantIds.includes(selectedPlantIdNum));

                // Check if a specific plant filter is applied AND it's one the admin manages (using the number)
                if (selectedPlantIdNum != null && this.loggedInPlantIds.includes(selectedPlantIdNum)) {
                    // A specific, valid plant IS selected - filter ONLY by this plant
                    filterParams.push(`${plantFilterPath}||eq||${selectedPlantIdNum}`); // Use the number
                    console.log(`[loadActiveUsers] Applying EQ filter for plant: ${selectedPlantIdNum}`);
                } else {
                    // No specific valid plant selected -> filter by ALL plants the admin has access to.
                    filterParams.push(`${plantFilterPath}||$in||${this.loggedInPlantIds.join(',')}`);
                    console.log(`[loadActiveUsers] Applying IN filter for plants: ${this.loggedInPlantIds.join(',')}`);

                    // Optional: Reset visual filter if an invalid plantId was selected (e.g., string vs number caused includes mismatch initially)
                    // This might not be strictly necessary if the dropdown prevents selecting plants outside loggedInPlantIds
                    if (this.filters.plantId != null && (selectedPlantIdNum == null || !this.loggedInPlantIds.includes(selectedPlantIdNum))) {
                        console.warn(`[loadActiveUsers] Invalid or non-managed plant filter (${this.filters.plantId}) detected, showing all accessible plants.`);
                        // Decide if you want to reset the UI filter value: this.filters.plantId = null;
                    }
                }
                // *** END FIX ***

            } else {
                // Plant Admin has no assigned plants, block results
                filterParams.push(`${plantFilterPath}||eq||-1`);
                console.log('[loadActiveUsers] Plant Admin has no plants, blocking results.');
            }
        } else if (this.currentUserRole === ROLES.SUPER_ADMIN) {
            // Super Admin logic remains the same
            if (this.filters.plantId != null) {
                filterParams.push(`${plantFilterPath}||eq||${this.filters.plantId}`);
                console.log(`[loadActiveUsers] Super Admin applying EQ filter for plant: ${this.filters.plantId}`);
            } else {
                console.log('[loadActiveUsers] Super Admin showing all plants.');
            }
        }
        // End Role-Based Plant Filter

        // ... (rest of your filter additions)
        if (this.filters.firstName) filterParams.push(`firstName||$contL||${this.filters.firstName}`);
        if (this.filters.lastName) filterParams.push(`lastName||$contL||${this.filters.lastName}`);
        if (this.filters.email) filterParams.push(`email||$contL||${this.filters.email}`);
        if (this.filters.mobile) filterParams.push(`contactNumber||$contL||${this.filters.mobile}`);
        if (this.filters.designationId != null) filterParams.push(`designationId||eq||${this.filters.designationId}`);
        if (this.filters.departmentId != null) filterParams.push(`departmentId||eq||${this.filters.departmentId}`);
        if (this.filters.adminRoleId != null) filterParams.push(`adminsRoleId||eq||${this.filters.adminRoleId}`);


        const data = {
            page: page, limit: this.itemsPerPage,
            sort: `${this.filters.sortField || 'id'},${this.filters.sortDirection || 'DESC'}`,
            filter: filterParams,
            join: ['adminsRole', 'department', 'designation', 'plantUsers.plant'] // Keep join as is for now
        };

        try {
            const param = createAxiosConfig(data);
            console.log("[loadActiveUsers] Final API Request Params:", JSON.stringify(data, null, 2));
            const response = await this.adminService.getAdmin(param);
            // ... (rest of try/catch)
            let users: ActiveUserItem[] = response.data ?? [];
            this.totalItems = response.total ?? 0;
            if (users.length > 0) { users = this.mergeDeviceDetailsIntoList(users); }
            this.activeUserList = users;
        } catch (error: any) {
            console.error("Error fetching active users:", error);
            this.activeUserList = []; this.totalItems = 0;
            this.toast?.showErrorToast(error?.response?.data?.message || 'Failed to load users.');
        } finally {
            this.listLoading = false;
        }
    }

    onPageChange(page: number) {
        if (page !== this.currentPage) {
            this.currentPage = page;
            this.isDeviceDetailsLoading = true; // Reset device details loading state
            this.loadActiveUsers(this.currentPage);

            // Fetch device details for the new page
            this.fetchAllDeviceDetails().then(() => {
                if (this.activeUserList.length > 0) {
                    this.activeUserList = this.mergeDeviceDetailsIntoList(this.activeUserList);
                }
            }).catch(error => {
                console.error("Error fetching device details:", error);
                this.isDeviceDetailsLoading = false;
            });
        }
    }

    applyFilters() {
        // Trim all string values to prevent whitespace-only searches
        if (this.filters.firstName) this.filters.firstName = this.filters.firstName.trim();
        if (this.filters.lastName) this.filters.lastName = this.filters.lastName.trim();
        if (this.filters.email) this.filters.email = this.filters.email.trim();
        if (this.filters.mobile) this.filters.mobile = this.filters.mobile.trim();

        // Validate email format if provided
        if (this.filters.email) {
            // First check basic email format
            const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            if (!emailRegex.test(this.filters.email)) {
                this.toast?.showErrorToast("Please enter a valid email address.");
                return;
            }

            // Then check for adani.com domain
            if (!this.filters.email.toLowerCase().endsWith('@adani.com')) {
                this.toast?.showErrorToast("Email must use the @adani.com domain.");
                return;
            }
        }

        this.currentPage = 1;
        this.isDeviceDetailsLoading = true; // Reset device details loading state
        this.loadActiveUsers(this.currentPage);

        // Fetch device details for the filtered results
        this.fetchAllDeviceDetails().then(() => {
            if (this.activeUserList.length > 0) {
                this.activeUserList = this.mergeDeviceDetailsIntoList(this.activeUserList);
            }
        }).catch(error => {
            console.error("Error fetching device details:", error);
            this.isDeviceDetailsLoading = false;
        });

        this.closeModal();
    }

    resetFilters() {
        this.filters = {
            firstName: null, lastName: null, email: null, mobile: null,
            plantId: null, designationId: null, departmentId: null, adminRoleId: null,
            sortField: 'id', sortDirection: 'DESC'
        };
        this.currentPage = 1;
        this.isDeviceDetailsLoading = true; // Reset device details loading state
        this.loadActiveUsers(this.currentPage);

        // Fetch device details for the reset results
        this.fetchAllDeviceDetails().then(() => {
            if (this.activeUserList.length > 0) {
                this.activeUserList = this.mergeDeviceDetailsIntoList(this.activeUserList);
            }
        }).catch(error => {
            console.error("Error fetching device details:", error);
            this.isDeviceDetailsLoading = false;
        });

        this.closeModal();
    }

    onFilterPlantChange(): void {
        // When plant changes, update the designation and department dropdowns
        // based on the selected plant
        if (this.filters.plantId) {
            // Load designations and departments for the selected plant
            this.getDesignations();
            this.getDepartments();
        } else {
            // Load all designations and departments
            this.getDesignations();
            this.getDepartments();
        }
    }

    getSortClass(key: string): string {
        if (this.filters.sortField === key) { return this.filters.sortDirection === 'ASC' ? 'sort-asc' : 'sort-desc'; }
        return 'sort-none';
    }

    sortBy(field: string) {
        if (this.listLoading) return;
        if (this.filters.sortField === field) { this.filters.sortDirection = this.filters.sortDirection === 'ASC' ? 'DESC' : 'ASC'; }
        else { this.filters.sortField = field; this.filters.sortDirection = 'DESC'; }
        this.currentPage = 1;
        this.isDeviceDetailsLoading = true; // Reset device details loading state
        this.loadActiveUsers(this.currentPage);

        // Fetch device details for the sorted results
        this.fetchAllDeviceDetails().then(() => {
            if (this.activeUserList.length > 0) {
                this.activeUserList = this.mergeDeviceDetailsIntoList(this.activeUserList);
            }
        }).catch(error => {
            console.error("Error fetching device details:", error);
            this.isDeviceDetailsLoading = false;
        });
    }

    async handleDelete(user: any) { // Keep using 'any' if ActiveUserItem causes issues here
        if (!user || !user.id) { console.error('Cannot delete: User data or ID is missing.'); return; }

        // Check if plant admin is trying to delete a super admin
        if (this.currentUserRole === ROLES.PLANT_ADMIN && user.adminsRole?.id === 1) {
            this.toast?.showErrorToast("Plant admins cannot delete super admin users.");
            return;
        }

        // Add role check before showing confirmation
        const userPlantIds = user.plantUsers?.map((pu: any) => pu.plant?.id).filter((id: any) => id != null) as number[] || [];
        if (this.currentUserRole === ROLES.PLANT_ADMIN && !userPlantIds.some(pid => this.loggedInPlantIds.includes(pid))) {
            if (userPlantIds.length > 0) { this.toast?.showErrorToast("Permission denied to delete this user."); return; }
        }
        this.openDeleteConfirmation(user); // Show modal only if allowed
    }

    showMorePlants(user: any) {
        console.log('Show more plants requested for user:', user);
        this.toast?.showErrorToast('Functionality to show more plants is not implemented.');
    }

    // Reusable async email existence check for add/edit forms
    async checkEmailExistActiveUser(form: FormGroup, field: string) {
        const control = form.get(field);
        if (control?.valid) {
            const email = control.value.toLowerCase().trim();
            try {
                const response: any = await this.adminService.checkEmailAndRole({ email });
                if (response?.responseCode === 200) {
                    // If backend says "admin found", show custom message
                    const customMsg = response?.message === 'admin found'
                        ? 'Email already used, try a different one.'
                        : (response.message || 'This email is already registered.');
                    control.setErrors({ ...control.errors, emailTaken: true, emailTakenMsg: customMsg });
                    this.toast?.showErrorToast(customMsg);
                } else {
                    // Remove only the emailTaken error if present
                    if (control.hasError('emailTaken')) {
                        const errors = { ...control.errors };
                        delete errors['emailTaken'];
                        delete errors['emailTakenMsg'];
                        control.setErrors(Object.keys(errors).length ? errors : null);
                    }
                }
            } catch (err) {
                // On error or not found, clear the error
                if (control.hasError('emailTaken')) {
                    const errors = { ...control.errors };
                    delete errors['emailTaken'];
                    delete errors['emailTakenMsg'];
                    control.setErrors(Object.keys(errors).length ? errors : null);
                }
                // Optionally log error
                console.error("Error checking email:", err);
            }
        }
    }

    // Reusable async contact existence check for add/edit forms
    async checkContactExistActiveUser(form: FormGroup, field: string) {
        const control = form.get(field);
        if (control?.valid) {
            const contactNumber = control.value.toLowerCase().trim();

            // If in edit mode and the contact number is unchanged for the current user, skip uniqueness check
            if (
                this.isEditUserModalOpen &&
                this.selectedUser &&
                this.selectedUser.contactNumber &&
                this.selectedUser.contactNumber.toLowerCase().trim() === contactNumber
            ) {
                // Remove only the contactTaken error if present
                if (control.hasError('contactTaken')) {
                    const errors = { ...control.errors };
                    delete errors['contactTaken'];
                    delete errors['contactTakenMsg'];
                    control.setErrors(Object.keys(errors).length ? errors : null);
                }
                return;
            }

            try {
                const response: any = await this.adminService.checkContactNumber({ contactNumber });
                if (response?.responseCode === 300) {
                    control.setErrors({ ...control.errors, contactTaken: true, contactTakenMsg: response.message || 'This contact number is already registered.' });
                    this.toast?.showErrorToast(response.message || 'This contact number is already registered.');
                } else {
                    // Remove only the contactTaken error if present
                    if (control.hasError('contactTaken')) {
                        const errors = { ...control.errors };
                        delete errors['contactTaken'];
                        delete errors['contactTakenMsg'];
                        control.setErrors(Object.keys(errors).length ? errors : null);
                    }
                }
            } catch (err) {
                // On error or not found, clear the error
                if (control.hasError('contactTaken')) {
                    const errors = { ...control.errors };
                    delete errors['contactTaken'];
                    delete errors['contactTakenMsg'];
                    control.setErrors(Object.keys(errors).length ? errors : null);
                }
                // Optionally log error
                console.error("Error checking contact number:", err);
            }
        }
    }
}