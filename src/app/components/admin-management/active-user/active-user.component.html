<app-toast-message></app-toast-message>
<div class="card custom-card" id="admin-list">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col d-flex align-items-center">
                <h6 class=>Admin List</h6>
            </div>
            <div class="col text-end d-flex align-items-center justify-content-end">
                <!-- Add User Button -->
                <button type="button" class="btn btn-sm adani-btn me-2" (click)="openAddUserModal()">
                    <i class="bi bi-person-plus me-1"></i> Add User
                </button>

                <!-- Download Excel Dropdown -->
                <div ngbDropdown class="d-inline-block me-2">
                  <button type="button" class="btn btn-sm adani-btn dropdown-toggle" id="downloadUserExcelDropdown" ngbDropdownToggle
                      [disabled]="isDownloadingExcel || listLoading">
                      <span *ngIf="!isDownloadingExcel">
                          <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
                      </span>
                      <span *ngIf="isDownloadingExcel">
                          <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                          Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '') }}...
                      </span>
                  </button>
                  <ul ngbDropdownMenu aria-labelledby="downloadUserExcelDropdown">
                      <li>
                          <button ngbDropdownItem (click)="downloadExcel('current')" [disabled]="isDownloadingExcel || listLoading || (activeUserList?.length ?? 0) === 0">
                              <i class="bi bi-download me-1"></i> Download Current Page ({{ activeUserList?.length ?? 0 }})
                          </button>
                      </li>
                      <li>
                          <button ngbDropdownItem (click)="downloadExcel('all')" [disabled]="isDownloadingExcel || listLoading || totalItems === 0">
                              <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
                          </button>
                      </li>
                  </ul>
              </div>
                <img src="../../../assets/svg/filter.svg" class="filter-button ms-3" (click)="openFilterModal()" alt="" style="width: 35px;" />
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered custom-table">
                <thead class="table-header">
                    <tr>
                        <th scope="col" class="col-avatar">Avatar</th>
                        <th scope="col" class="col-admin-details">Admin Details</th>
                        <th scope="col" class="col-plant-info">Plant Information</th>
                        <th scope="col" class="col-device">Device Details</th>
                    </tr>
                </thead>
                <tbody>
                  <tr *ngIf="listLoading">
                    <td colspan="4" class="text-center p-4"> <!-- Colspan = 4 -->
                        <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        Loading active users...
                    </td>
                </tr>
                <tr *ngIf="!listLoading && activeUserList.length === 0">
                  <td colspan="4" class="text-center p-4 text-muted"> <!-- Colspan = 4 -->
                      No active users found matching the criteria.
                  </td>
              </tr>
                    <tr *ngFor="let item of activeUserList">
                        <td class="text-center">
                            <div class="user-id mb-2 fw-bold">ID: {{item.id}}</div>
                            <img [src]="item.profilePicture || '../../../assets/svg/Avatar.svg'" class="img-thumbnail rounded-circle" alt="Avatar">
                            <div class="d-flex flex-row gap-1 justify-content-center mt-2">
                                <!-- Edit button -->
                                <button type="button" *ngIf="currentUserRole === componentRoles.SUPER_ADMIN ||
                                                           (currentUserRole === componentRoles.PLANT_ADMIN && item.adminsRole?.id !== 1)"
                                       class="btn btn-sm btn-warning" title="Edit" (click)="openEditModal(item)">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <!-- Transfer button -->
                                <button type="button" *ngIf="(currentUserRole === componentRoles.SUPER_ADMIN) ||
                                                            (currentUserRole === componentRoles.PLANT_ADMIN && item.adminsRole?.id === 3)"
                                       class="btn btn-sm btn-info" title="Transfer User" (click)="openTransferModal(item)">
                                  <i class="bi bi-arrow-left-right"></i>
                                </button>
                                <!-- Delete button -->
                                <button type="button" *ngIf="currentUserRole === componentRoles.SUPER_ADMIN ||
                                                           (currentUserRole === componentRoles.PLANT_ADMIN && item.adminsRole?.id !== 1)"
                                       class="btn btn-sm btn-danger" title="Delete" (click)="openDeleteConfirmation(item)">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                        <td>
                            <div class="details-container">
                                <p class="label-value"><strong>Name:</strong> <span class="value-text">{{item.firstName}} {{item.lastName}}</span></p>
                                <p class="label-value">
                                    <strong>Role:</strong>
                                    <span class="value-text">
                                        <span *ngIf="item.adminsRole?.id === 1" class="badge bg-primary">Super Admin</span>
                                        <span *ngIf="item.adminsRole?.id === 2" class="badge bg-warning">Plant Admin</span>
                                        <span *ngIf="item.adminsRole?.id === 3" class="badge bg-secondary">User</span>
                                    </span>
                                </p>
                                <p class="label-value"><strong>Email:</strong> <span class="value-text">{{item.email}}</span></p>
                                <p class="label-value"><strong>Contact:</strong> <span class="value-text">{{item.contactNumber}}</span></p>
                                <p class="label-value"><strong>Date of Birth:</strong> <span class="value-text">{{item.dob | date:'dd-MMM-yyyy'}}</span></p>
                                <p class="label-value">
                                    <strong>Gender:</strong>
                                    <span class="value-text" [ngSwitch]="+(item.gender ?? -1)">
                                        <span *ngSwitchCase="0">Female</span>
                                        <span *ngSwitchCase="1">Male</span>
                                        <span *ngSwitchCase="2">Other</span>
                                        <span *ngSwitchDefault>{{ item.gender }}</span>
                                    </span>
                                </p>
                                <p class="label-value" *ngIf="item.createdTimestamp != null">
                                    <strong>Created Date:</strong> <span class="value-text">{{item.createdTimestamp | date:'dd-MMM-yyyy'}}</span>
                                </p>
                                <p class="label-value" *ngIf="item.lastLogin != null">
                                    <strong>Last Login:</strong> <span class="value-text">{{item.lastLogin | date:'dd-MMM-yyyy HH:mm'}}</span>
                                </p>
                            </div>
                        </td>
                        <td>
                          <div class="details-container">
                            <p class="label-value">
                              <strong>Plants:</strong>
                              <span class="value-text">
                                <div class="plant-badges-container">
                                  <ng-container *ngIf="item.plant?.length; else noPlants">
                                    <span *ngFor="let plant of item.plant" class="plant-badge" [ngClass]="{'bg-primary': plant.id % 6 === 0, 'bg-secondary': plant.id % 6 === 1, 'bg-success': plant.id % 6 === 2, 'bg-info': plant.id % 6 === 3, 'bg-warning': plant.id % 6 === 4, 'bg-danger': plant.id % 6 === 5}">
                                      {{ plant.name }}
                                    </span>
                                  </ng-container>
                                  <ng-template #noPlants><span class="text-muted">No plants</span></ng-template>
                                </div>
                              </span>
                            </p>
                            <p class="label-value"><strong>Department:</strong> <span class="value-text">{{item.department?.title || 'N/A'}}</span></p>
                            <p class="label-value"><strong>Designation:</strong> <span class="value-text">{{item.designation?.title || 'N/A'}}</span></p>
                          </div>
                        </td>
                        <td>
                          <!-- Show loading indicator while device details are being fetched -->
                          <ng-container *ngIf="isDeviceDetailsLoading; else deviceDataLoaded">
                            <div class="text-center">
                              <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                              <span>Loading device details...</span>
                            </div>
                          </ng-container>
                          <ng-template #deviceDataLoaded>
                            <!-- Check if deviceDetails exist on the item -->
                            <ng-container *ngIf="item.deviceDetails; else noDeviceData">
                              <div class="details-container">
                                <p class="label-value"><strong>Platform:</strong> <span class="value-text">{{ item.deviceDetails.platform ?? 'N/A' }}</span></p>
                                <p class="label-value"><strong>Device:</strong> <span class="value-text">{{ item.deviceDetails.deviceName ?? 'N/A' }}</span></p>
                                <p class="label-value"><strong>OS Version:</strong> <span class="value-text">{{ item.deviceDetails.osVersion ?? 'N/A' }}</span></p>
                                <p class="label-value"><strong>App Version:</strong> <span class="value-text">{{ item.deviceDetails.appVersion ?? 'N/A' }}</span></p>
                                <p class="label-value">
                                  <strong>Last Login:</strong>
                                  <span class="value-text" *ngIf="item.deviceDetails.lastLogin; else noDeviceLogin">
                                    {{ item.deviceDetails.lastLogin | date:'dd-MMM-yyyy HH:mm' }}
                                  </span>
                                  <ng-template #noDeviceLogin><span class="value-text">N/A</span></ng-template>
                                </p>
                              </div>
                            </ng-container>
                            <ng-template #noDeviceData>
                                <span class="text-muted">No device data available</span>
                            </ng-template>
                          </ng-template>
                      </td>

                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div class="card-footer text-muted text-center">
        <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
            (pageChange)="onPageChange($event)"></app-pagination>
    </div>
</div>
<app-offcanvas [title]="'Filter Active Users'" *ngIf="isOffModalOpen" (onClickCross)="closeModal()">
    <div class="filter-container p-3"> <!-- Add padding -->
        <form #filterForm="ngForm" (ngSubmit)="applyFilters()"> <!-- Add form -->
            <div class="row g-3"> <!-- Use g-3 for spacing -->

                <div class="col-12">
                    <label class="form-label" for="filterFirstName">First Name</label>
                    <input type="text" id="filterFirstName" class="form-control" placeholder="First Name"
                           [(ngModel)]="filters.firstName" name="firstName" maxlength="30" pattern="^[a-zA-Z\s]*$"
                           #firstName="ngModel" [ngClass]="{'is-invalid': firstName.invalid && (firstName.dirty || firstName.touched)}">
                    <div *ngIf="firstName.invalid && (firstName.dirty || firstName.touched)" class="invalid-feedback">
                        <span *ngIf="firstName.errors?.['pattern']">First name should contain only alphabets.</span>
                    </div>
                </div>

                <div class="col-12">
                    <label class="form-label" for="filterLastName">Last Name</label>
                    <input type="text" id="filterLastName" class="form-control" placeholder="Last Name"
                           [(ngModel)]="filters.lastName" name="lastName" maxlength="30" pattern="^[a-zA-Z\s]*$"
                           #lastName="ngModel" [ngClass]="{'is-invalid': lastName.invalid && (lastName.dirty || lastName.touched)}">
                    <div *ngIf="lastName.invalid && (lastName.dirty || lastName.touched)" class="invalid-feedback">
                        <span *ngIf="lastName.errors?.['pattern']">Last name should contain only alphabets.</span>
                    </div>
                </div>

                <div class="col-12">
                    <label class="form-label" for="filterEmail">Email</label>
                    <input type="email" id="filterEmail" class="form-control" placeholder="Email with &#64;adani.com domain"
                           [(ngModel)]="filters.email" name="email" maxlength="200" pattern="[a-zA-Z0-9._%+-]+&#64;adani\.com$"
                           #email="ngModel" [ngClass]="{'is-invalid': email.invalid && (email.dirty || email.touched)}">
                    <div *ngIf="email.invalid && (email.dirty || email.touched)" class="invalid-feedback">
                        <span *ngIf="email.errors?.['pattern']">Email must use the &#64;adani.com domain.</span>
                    </div>
                </div>

                <div class="col-12">
                    <label class="form-label" for="filterMobile">Mobile Number</label>
                    <input type="text" id="filterMobile" class="form-control" placeholder="Mobile Number"
                           [(ngModel)]="filters.mobile" name="mobile" maxlength="10" pattern="^[0-9]+$"
                           #mobile="ngModel" [ngClass]="{'is-invalid': mobile.invalid && (mobile.dirty || mobile.touched)}">
                    <div *ngIf="mobile.invalid && (mobile.dirty || mobile.touched)" class="invalid-feedback">
                        <span *ngIf="mobile.errors?.['pattern']">Mobile number should contain only digits.</span>
                    </div>
                </div>

                <div class="col-12">
                    <label class="form-label" for="filterPlant">Select Plant</label>
                    <select id="filterPlant" class="form-select"
                            [(ngModel)]="filters.plantId" name="plantId" (change)="onFilterPlantChange()">
                        <option [ngValue]="null">All Plants</option>
                        <option *ngFor="let plant of availablePlants" [value]="plant.id">{{ plant.name }}</option>
                    </select>
                </div>

                <div class="col-12">
                    <label class="form-label" for="filterDesignation">Filter by Designation</label>
                    <select id="filterDesignation" class="form-select"
                            [(ngModel)]="filters.designationId" name="designationId">
                        <option [ngValue]="null">All Designations</option>
                         <option *ngFor="let desg of availableDesignations" [value]="desg.id">{{ desg.title }}</option>
                    </select>
                </div>

                <div class="col-12">
                    <label class="form-label" for="filterDepartment">Filter by Department</label>
                    <select id="filterDepartment" class="form-select"
                            [(ngModel)]="filters.departmentId" name="departmentId">
                         <option [ngValue]="null">All Departments</option>
                         <option *ngFor="let dept of availableDepartments" [value]="dept.id">{{ dept.title }}</option>
                    </select>
                </div>

                <div class="col-12">
                    <label class="form-label" for="filterAdminRole">Search by Admin Role</label>
                    <select id="filterAdminRole" class="form-select"
                            [(ngModel)]="filters.adminRoleId" name="adminRoleId">
                        <option [ngValue]="null">All Roles</option>
                         <option *ngFor="let role of availableAdminRoles" [value]="role.id">{{ role.name }}</option>
                    </select>
                </div>

                <div class="col-12">
                    <label class="form-label" for="filterSortBy">Sort By</label>
                    <select id="filterSortBy" class="form-select"
                            [(ngModel)]="filters.sortField" name="sortField"> <!-- Bind to sortField -->
                        <option [ngValue]="null">Default Sort (ID DESC)</option>
                        <option *ngFor="let field of availableSortFields" [value]="field.value">{{ field.label }}</option>
                    </select>
                    <!-- Optional: Add Sort Direction dropdown -->
                    <label class="form-label mt-2" for="filterSortDir">Sort Direction</label>
                    <select id="filterSortDir" class="form-select"
                            [(ngModel)]="filters.sortDirection" name="sortDirection">
                        <option value="DESC">Descending</option>
                        <option value="ASC">Ascending</option>
                    </select>
                </div>


                <div class="col-12 mt-4 d-grid gap-2">
                    <button type="submit" class="btn adani-btn">
                        <i class="bi bi-search me-1"></i> Search
                    </button>
                     <button type="button" class="btn btn-secondary" (click)="resetFilters()">
                        <i class="bi bi-arrow-clockwise me-1"></i> Reset Filters
                    </button>
                </div>
            </div>
        </form>
    </div>
</app-offcanvas>

<app-offcanvas [title]="'Edit User'" *ngIf="isEditUserModalOpen" (onClickCross)="closeEditModal()">
    <div class="p-3">
      <!-- Edit User Form -->
      <form [formGroup]="editUserForm" (ngSubmit)="onEditSubmit()">

        <!-- Profile Picture -->
        <div class="mb-3 text-center">
          <img
            [src]="profileImageUrl || '../../../assets/svg/Avatar.svg'"
            alt="Profile Picture"
            class="img-thumbnail rounded-circle mb-2"
            width="100"
            height="100"
            style="object-fit: cover;"
          />
          <label for="profile" class="form-label d-block">Change Profile Picture</label>
          <input type="file" class="form-control form-control-sm" id="profile" #fileInput (change)="onFileChange($event)" accept="image/*"/>
        </div>

        <!-- Role -->
        <div class="mb-3">
          <label for="role" class="form-label">Role <span class="text-danger">*</span></label>
          <select class="form-select"
                  [ngClass]="{'is-invalid': (editUserForm.get('adminsRoleId')?.touched || editUserForm.get('adminsRoleId')?.dirty) && editUserForm.get('adminsRoleId')?.invalid}"
                  formControlName="adminsRoleId" id="role"
                  [disabled]="currentUserRole === componentRoles.PLANT_ADMIN">
            <option value="" disabled>Select Role</option>
            <option *ngFor="let role of availableAdminRoles" [value]="role.id">{{ role.name }}</option>
          </select>
          <div *ngIf="(editUserForm.get('adminsRoleId')?.touched || editUserForm.get('adminsRoleId')?.dirty) && editUserForm.get('adminsRoleId')?.errors?.['required']" class="text-danger mt-1 small">
            Please select a role.
          </div>
        </div>

        <!-- First Name -->
        <div class="mb-3">
          <label for="firstName" class="form-label">First Name <span class="text-danger">*</span></label>
          <input type="text" class="form-control"
                 [ngClass]="{'is-invalid': (editUserForm.get('firstName')?.touched || editUserForm.get('firstName')?.dirty) && editUserForm.get('firstName')?.invalid}"
                 formControlName="firstName" placeholder="Enter First Name"
                 oninput="this.value = this.value.trimStart()"/>
          <div *ngIf="(editUserForm.get('firstName')?.touched || editUserForm.get('firstName')?.dirty) && editUserForm.get('firstName')?.errors?.['required']" class="text-danger mt-1 small">
            First Name is required.
          </div>
          <div *ngIf="(editUserForm.get('firstName')?.touched || editUserForm.get('firstName')?.dirty) && editUserForm.get('firstName')?.errors?.['pattern']" class="text-danger mt-1 small">
            First Name must be alphabetic and maximum 30 characters long.
          </div>
        </div>

        <!-- Last Name -->
        <div class="mb-3">
          <label for="lastName" class="form-label">Last Name <span class="text-danger">*</span></label>
          <input type="text" class="form-control"
                 [ngClass]="{'is-invalid': (editUserForm.get('lastName')?.touched || editUserForm.get('lastName')?.dirty) && editUserForm.get('lastName')?.invalid}"
                 formControlName="lastName" placeholder="Enter Last Name"
                 oninput="this.value = this.value.trimStart()"/>
          <div *ngIf="(editUserForm.get('lastName')?.touched || editUserForm.get('lastName')?.dirty) && editUserForm.get('lastName')?.errors?.['required']" class="text-danger mt-1 small">
            Last Name is required.
          </div>
           <div *ngIf="(editUserForm.get('lastName')?.touched || editUserForm.get('lastName')?.dirty) && editUserForm.get('lastName')?.errors?.['pattern']" class="text-danger mt-1 small">
            Last Name must be alphabetic and maximum 30 characters long.
          </div>
        </div>

        <!-- Assign Plants (ng-select Multi-select) -->
        <div class="mb-3" *ngIf="currentUserRole === componentRoles.SUPER_ADMIN"> <!-- Only show for Super Admin -->
          <label for="plantIds" class="form-label">Assign Plants <span class="text-danger">*</span></label>
          <ng-select
              [items]="availablePlants"
              bindLabel="name"
              bindValue="id"
              [multiple]="true"
              placeholder="Select plants to assign"
              formControlName="plantIds"
              id="plantIds"
              [closeOnSelect]="false"
              [clearable]="true"
              [ngClass]="{'is-invalid': (editUserForm.get('plantIds')?.touched || editUserForm.get('plantIds')?.dirty) && editUserForm.get('plantIds')?.invalid}">
          </ng-select>
          <div *ngIf="(editUserForm.get('plantIds')?.touched || editUserForm.get('plantIds')?.dirty) && editUserForm.get('plantIds')?.errors?.['required']" class="text-danger mt-1 small">
            Please select at least one plant.
          </div>
        </div>

        <!-- Gender -->
        <div class="mb-3">
          <label for="gender" class="form-label">Gender <span class="text-danger">*</span></label>
          <select class="form-select"
                  [ngClass]="{'is-invalid': (editUserForm.get('gender')?.touched || editUserForm.get('gender')?.dirty) && editUserForm.get('gender')?.invalid}"
                  formControlName="gender" id="gender">
            <option value="" disabled>Select Gender</option>
            <option value=1>Male</option>
            <option value=0>Female</option>
            <option value=2>Other</option>
          </select>
          <div *ngIf="(editUserForm.get('gender')?.touched || editUserForm.get('gender')?.dirty) && editUserForm.get('gender')?.errors?.['required']" class="text-danger mt-1 small">
            Please select a gender.
          </div>
        </div>

        <!-- Email (Readonly) -->
        <div class="mb-3">
          <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
          <input type="email" class="form-control"
                 [ngClass]="{'is-invalid': editUserForm.get('email')?.invalid}"
                 formControlName="email" placeholder="Email with &#64;adani.com domain" readonly/>
          <!-- Show validation errors even though field is readonly/disabled -->
          <div *ngIf="editUserForm.get('email')?.errors?.['adaniDomain']" class="text-danger mt-1 small">
            Email must use the &#64;adani.com domain.
          </div>
        </div>

        <!-- Contact Number -->
        <div class="mb-3">
          <label for="contact" class="form-label">Contact No. <span class="text-danger">*</span></label>
          <input type="text" class="form-control"
                 [ngClass]="{'is-invalid': (editUserForm.get('contactNumber')?.touched || editUserForm.get('contactNumber')?.dirty) && editUserForm.get('contactNumber')?.invalid}"
                 formControlName="contactNumber" placeholder="Enter 10 Digit Contact No."
                 onkeypress="return event.charCode >= 48 && event.charCode <= 57" maxlength="10"
                 (blur)="checkContactExistActiveUser(editUserForm, 'contactNumber')"/>
          <div *ngIf="(editUserForm.get('contactNumber')?.touched || editUserForm.get('contactNumber')?.dirty) && editUserForm.get('contactNumber')?.errors" class="text-danger mt-1 small">
              <span *ngIf="editUserForm.get('contactNumber')?.errors?.['required']">Contact number is required.</span>
              <span *ngIf="editUserForm.get('contactNumber')?.errors?.['pattern']">Please enter a valid 10-digit mobile number.</span>
              <span *ngIf="editUserForm.get('contactNumber')?.errors?.['contactTaken']">
                {{ editUserForm.get('contactNumber')?.errors?.['contactTakenMsg'] || 'This contact number is already registered.' }}
              </span>
           </div>
        </div>


        <!-- Department -->
        <div class="mb-3">
          <label for="departmentId" class="form-label">Department <span class="text-danger">*</span></label> <!-- FIX: Changed label 'for' -->
          <!-- FIX: Changed formControlName to 'departmentId' -->
          <select class="form-select"
                  [ngClass]="{'is-invalid': (editUserForm.get('departmentId')?.touched || editUserForm.get('departmentId')?.dirty) && editUserForm.get('departmentId')?.invalid}"
                  formControlName="departmentId" id="departmentId">
            <option value="" disabled>Select Department</option>
            <!-- Assuming availableDepartments has {id: number, name: string} -->
            <option *ngFor="let dept of availableDepartments" [value]="dept.id">{{ dept.title }}</option>
          </select>
           <div *ngIf="(editUserForm.get('departmentId')?.touched || editUserForm.get('departmentId')?.dirty) && editUserForm.get('departmentId')?.errors?.['required']" class="text-danger mt-1 small">
            Department is required.
          </div>
        </div>

        <!-- Designation (ensure this was already correct) -->
        <div class="mb-3">
          <label for="designationId" class="form-label">Designation <span class="text-danger">*</span></label>
          <select class="form-select"
                  [ngClass]="{'is-invalid': (editUserForm.get('designationId')?.touched || editUserForm.get('designationId')?.dirty) && editUserForm.get('designationId')?.invalid}"
                  formControlName="designationId" id="designationId">
            <option value="" disabled>Select Designation</option>
            <!-- Assuming availableDesignations has {id: number, title: string} -->
            <option *ngFor="let desg of availableDesignations" [value]="desg.id">{{ desg.title }}</option>
          </select>
          <div *ngIf="(editUserForm.get('designationId')?.touched || editUserForm.get('designationId')?.dirty) && editUserForm.get('designationId')?.errors?.['required']" class="text-danger mt-1 small">
            Designation is required.
          </div>
        </div>

        <!-- Form Action Buttons -->
        <div class="d-flex gap-2 mt-4">
          <button class="btn adani-btn flex-grow-1" type="submit" [disabled]="isSubmitting || editUserForm.invalid">
            <span *ngIf="!isSubmitting">
              <i class="bi bi-check-circle me-1"></i> Update
            </span>
            <span *ngIf="isSubmitting">
              <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
              Updating...
            </span>
          </button>
          <button type="button" (click)="resetEditForm()" class="btn btn-secondary flex-grow-1" [disabled]="isSubmitting">
              <i class="bi bi-arrow-clockwise me-1"></i> Reset
          </button>
           <button type="button" (click)="closeEditModal()" class="btn btn-outline-secondary flex-grow-1" [disabled]="isSubmitting">
             <i class="bi bi-x-lg me-1"></i> Cancel
           </button>
        </div>

      </form>
    </div>
  </app-offcanvas>
  <!-- Transfer User Modal -->
<div class="modal fade" tabindex="-1" id="transferUserModal" #transferUserModalElement aria-labelledby="transferUserModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header text-white"> <!-- Changed header style slightly -->
        <h5 class="modal-title" id="transferUserModalLabel">
           <i class="bi bi-arrow-left-right me-2"></i> Transfer User: {{ currentUserForTransfer?.firstName }} {{ currentUserForTransfer?.lastName }}
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close" (click)="closeTransferModal()"></button>
      </div>
      <div class="modal-body">
        <form [formGroup]="transferUserForm" *ngIf="currentUserForTransfer"> <!-- Ensure form exists only when user selected -->
          <div class="mb-3">
              <label class="form-label fw-bold">Current Plant(s)</label>
              <!-- Display current plant(s) -->
              <input type="text" class="form-control" [value]="currentUserPlantNames" disabled />
              <small class="text-muted" *ngIf="!currentUserPlantNames">User not currently assigned to any plant.</small>
          </div>

          <div class="mb-3">
            <label for="targetPlantId" class="form-label fw-bold">Transfer To <span class="text-danger">*</span></label>
            <select formControlName="targetPlantId" class="form-select" id="targetPlantId"
                    [class.is-invalid]="transferUserForm.get('targetPlantId')?.touched && transferUserForm.get('targetPlantId')?.invalid">
              <option value="" disabled>Select New Plant</option>
              <!-- Populate with plants *other than* the current one(s) -->
              <option *ngIf="availablePlantsForTransfer.length === 0" value="" disabled>No other plants available for transfer</option>
              <option *ngFor="let plant of availablePlantsForTransfer" [value]="plant.id">{{ plant.name }}</option>
            </select>
            <div *ngIf="transferUserForm.get('targetPlantId')?.touched && transferUserForm.get('targetPlantId')?.invalid" class="invalid-feedback">
              <div *ngIf="transferUserForm.get('targetPlantId')?.errors?.['required']">Please select a plant to transfer to.</div>
            </div>
          </div>

          <div class="d-flex mt-4 justify-content-center gap-2">
            <button type="button" class="btn adani-btn" (click)="submitTransfer()" [disabled]="transferUserForm.invalid || isSubmittingTransfer">
               <span *ngIf="!isSubmittingTransfer">
                 <i class="bi bi-check-circle me-1"></i> Confirm Transfer
               </span>
               <span *ngIf="isSubmittingTransfer">
                  <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                  Transferring...
               </span>
            </button>
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" (click)="closeTransferModal()">
              <i class="bi bi-x-lg me-1"></i> Cancel
            </button>
          </div>
        </form>
         <div *ngIf="!currentUserForTransfer" class="text-center text-muted p-3">
            Loading user data...
          </div>
      </div>
    </div>
  </div>
</div>
<!-- End Transfer User Modal -->
  <!-- Delete Confirmation Modal -->
<div class="modal fade" #deleteConfirmationModalElement id="deleteConfirmationModal" tabindex="-1" aria-labelledby="deleteConfirmationModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header bg-danger text-white"> <!-- Danger header -->
        <h5 class="modal-title" id="deleteConfirmationModalLabel">
          <i class="bi bi-exclamation-triangle-fill me-2"></i> Confirm Deletion
        </h5>
        <button type="button" class="btn-close btn-close-white" aria-label="Close" (click)="closeDeleteConfirmation()"></button>
      </div>
      <div class="modal-body">
        <p>Are you sure you want to permanently delete the user:</p>
        <p><strong>{{ userToDelete?.firstName }} {{ userToDelete?.lastName }} (ID: {{ userToDelete?.id }})</strong>?</p>
        <p class="text-danger fw-bold">This action cannot be undone.</p>
      </div>
      <div class="modal-footer justify-content-center"> <!-- Center buttons -->
        <button type="button" class="btn btn-secondary" (click)="closeDeleteConfirmation()">
            <i class="bi bi-x-lg me-1"></i> Cancel
        </button>
        <button type="button" class="btn btn-danger" (click)="confirmDelete()">
            <i class="bi bi-trash-fill me-1"></i> Yes, Delete Permanently
        </button>
      </div>
    </div>
  </div>
</div>
<!-- End Delete Confirmation Modal -->

<!-- Add User Offcanvas -->
<app-offcanvas [title]="'Add User'" *ngIf="isAddUserModalOpen" (onClickCross)="closeAddUserModal()">
  <div class="p-3">
    <!-- Add User Form -->
    <form [formGroup]="addUserForm" (ngSubmit)="onAddSubmit()">

      <!-- Profile Picture -->
      <div class="mb-3 text-center">
        <img
          [src]="newUserProfileImageUrl || '../../../assets/svg/Avatar.svg'"
          alt="Profile Picture"
          class="img-thumbnail rounded-circle mb-2"
          width="100"
          height="100"
          style="object-fit: cover;"
        />
        <label for="newUserProfile" class="form-label d-block">Upload Profile Picture</label>
        <input type="file" class="form-control form-control-sm" id="newUserProfile" #newUserFileInput (change)="onNewUserFileChange($event)" accept="image/*"/>
      </div>

      <!-- Role -->
      <div class="mb-3">
        <label for="newUserRole" class="form-label">Role <span class="text-danger">*</span></label>
        <select class="form-select"
                [ngClass]="{'is-invalid': (addUserForm.get('adminsRoleId')?.touched || addUserForm.get('adminsRoleId')?.dirty) && addUserForm.get('adminsRoleId')?.invalid}"
                formControlName="adminsRoleId" id="newUserRole">
          <option value="" disabled>Select Role</option>
          <option *ngFor="let role of availableRolesForNewUser" [value]="role.id">{{ role.name }}</option>
        </select>
        <div *ngIf="(addUserForm.get('adminsRoleId')?.touched || addUserForm.get('adminsRoleId')?.dirty) && addUserForm.get('adminsRoleId')?.errors?.['required']" class="text-danger mt-1 small">
          Please select a role.
        </div>
      </div>

      <!-- First Name -->
      <div class="mb-3">
        <label for="newUserFirstName" class="form-label">First Name <span class="text-danger">*</span></label>
        <input type="text" class="form-control"
               [ngClass]="{'is-invalid': (addUserForm.get('firstName')?.touched || addUserForm.get('firstName')?.dirty) && addUserForm.get('firstName')?.invalid}"
               formControlName="firstName" id="newUserFirstName" placeholder="Enter First Name"
               oninput="this.value = this.value.trimStart()"/>
        <div *ngIf="(addUserForm.get('firstName')?.touched || addUserForm.get('firstName')?.dirty) && addUserForm.get('firstName')?.errors?.['required']" class="text-danger mt-1 small">
          First Name is required.
        </div>
        <div *ngIf="(addUserForm.get('firstName')?.touched || addUserForm.get('firstName')?.dirty) && addUserForm.get('firstName')?.errors?.['pattern']" class="text-danger mt-1 small">
          First Name must be alphabetic and maximum 30 characters long.
        </div>
      </div>

      <!-- Last Name -->
      <div class="mb-3">
        <label for="newUserLastName" class="form-label">Last Name <span class="text-danger">*</span></label>
        <input type="text" class="form-control"
               [ngClass]="{'is-invalid': (addUserForm.get('lastName')?.touched || addUserForm.get('lastName')?.dirty) && addUserForm.get('lastName')?.invalid}"
               formControlName="lastName" id="newUserLastName" placeholder="Enter Last Name"
               oninput="this.value = this.value.trimStart()"/>
        <div *ngIf="(addUserForm.get('lastName')?.touched || addUserForm.get('lastName')?.dirty) && addUserForm.get('lastName')?.errors?.['required']" class="text-danger mt-1 small">
          Last Name is required.
        </div>
         <div *ngIf="(addUserForm.get('lastName')?.touched || addUserForm.get('lastName')?.dirty) && addUserForm.get('lastName')?.errors?.['pattern']" class="text-danger mt-1 small">
          Last Name must be alphabetic and maximum 30 characters long.
        </div>
      </div>

      <!-- Email -->
      <div class="mb-3">
        <label for="newUserEmail" class="form-label">Email <span class="text-danger">*</span></label>
        <input type="email" class="form-control"
               [ngClass]="{'is-invalid': (addUserForm.get('email')?.touched || addUserForm.get('email')?.dirty) && addUserForm.get('email')?.invalid}"
               formControlName="email" id="newUserEmail" placeholder="Enter Email with &#64;adani.com domain"
               (blur)="checkEmailExistActiveUser(addUserForm, 'email')"/>
        <div *ngIf="(addUserForm.get('email')?.touched || addUserForm.get('email')?.dirty) && addUserForm.get('email')?.errors?.['required']" class="text-danger mt-1 small">
          Email is required.
        </div>
        <div *ngIf="(addUserForm.get('email')?.touched || addUserForm.get('email')?.dirty) && addUserForm.get('email')?.errors?.['email']" class="text-danger mt-1 small">
          Please enter a valid email address.
        </div>
        <div *ngIf="(addUserForm.get('email')?.touched || addUserForm.get('email')?.dirty) && addUserForm.get('email')?.errors?.['adaniDomain']" class="text-danger mt-1 small">
          Email must use the &#64;adani.com domain.
        </div>
        <div *ngIf="addUserForm.get('email')?.errors?.['emailTaken']" class="text-danger mt-1 small">
          {{ addUserForm.get('email')?.errors?.['emailTakenMsg'] || 'This email is already registered.' }}
        </div>
      </div>

      <!-- Gender -->
      <div class="mb-3">
        <label for="newUserGender" class="form-label">Gender <span class="text-danger">*</span></label>
        <select class="form-select"
                [ngClass]="{'is-invalid': (addUserForm.get('gender')?.touched || addUserForm.get('gender')?.dirty) && addUserForm.get('gender')?.invalid}"
                formControlName="gender" id="newUserGender">
          <option value="" disabled>Select Gender</option>
          <option value="1">Male</option>
          <option value="0">Female</option>
          <option value="2">Other</option>
        </select>
        <div *ngIf="(addUserForm.get('gender')?.touched || addUserForm.get('gender')?.dirty) && addUserForm.get('gender')?.errors?.['required']" class="text-danger mt-1 small">
          Please select a gender.
        </div>
      </div>

      <!-- Date of Birth -->
      <div class="mb-3">
        <label for="newUserDob" class="form-label">Date of Birth <span class="text-danger">*</span></label>
        <input type="date" class="form-control"
               [ngClass]="{'is-invalid': (addUserForm.get('dob')?.touched || addUserForm.get('dob')?.dirty) && addUserForm.get('dob')?.invalid}"
               formControlName="dob" id="newUserDob"/>
        <div *ngIf="(addUserForm.get('dob')?.touched || addUserForm.get('dob')?.dirty) && addUserForm.get('dob')?.errors?.['required']" class="text-danger mt-1 small">
          Date of Birth is required.
        </div>
      </div>

      <!-- Contact Number -->
      <div class="mb-3">
        <label for="newUserContact" class="form-label">Contact No. <span class="text-danger">*</span></label>
        <input type="text" class="form-control"
               [ngClass]="{'is-invalid': (addUserForm.get('contactNumber')?.touched || addUserForm.get('contactNumber')?.dirty) && addUserForm.get('contactNumber')?.invalid}"
               formControlName="contactNumber" id="newUserContact" placeholder="Enter 10 Digit Contact No."
               onkeypress="return event.charCode >= 48 && event.charCode <= 57" maxlength="10"
               (blur)="checkContactExistActiveUser(addUserForm, 'contactNumber')"/>
        <div *ngIf="(addUserForm.get('contactNumber')?.touched || addUserForm.get('contactNumber')?.dirty) && addUserForm.get('contactNumber')?.errors" class="text-danger mt-1 small">
            <span *ngIf="addUserForm.get('contactNumber')?.errors?.['required']">Contact number is required.</span>
            <span *ngIf="addUserForm.get('contactNumber')?.errors?.['pattern']">Please enter a valid 10-digit mobile number.</span>
            <span *ngIf="addUserForm.get('contactNumber')?.errors?.['contactTaken']">
              {{ addUserForm.get('contactNumber')?.errors?.['contactTakenMsg'] || 'This contact number is already registered.' }}
            </span>
         </div>
      </div>

      <!-- Assign Plants (ng-select Multi-select) -->
      <div class="mb-3" *ngIf="currentUserRole === componentRoles.SUPER_ADMIN"> <!-- Only show for Super Admin -->
        <label for="newUserPlantIds" class="form-label">Assign Plants <span class="text-danger">*</span></label>
        <ng-select
            [items]="availablePlants"
            bindLabel="name"
            bindValue="id"
            [multiple]="true"
            placeholder="Select plants to assign"
            formControlName="plantIds"
            id="newUserPlantIds"
            [closeOnSelect]="false"
            [clearable]="true"
            [ngClass]="{'is-invalid': (addUserForm.get('plantIds')?.touched || addUserForm.get('plantIds')?.dirty) && addUserForm.get('plantIds')?.invalid}">
        </ng-select>
        <div *ngIf="(addUserForm.get('plantIds')?.touched || addUserForm.get('plantIds')?.dirty) && addUserForm.get('plantIds')?.errors?.['required']" class="text-danger mt-1 small">
          Please select at least one plant.
        </div>
      </div>

      <!-- Plant Selection for Plant Admin (Single Select) -->
      <div class="mb-3" *ngIf="currentUserRole === componentRoles.PLANT_ADMIN">
        <label for="newUserPlantId" class="form-label">Assign Plant <span class="text-danger">*</span></label>
        <select class="form-select"
                [ngClass]="{'is-invalid': (addUserForm.get('plantId')?.touched || addUserForm.get('plantId')?.dirty) && addUserForm.get('plantId')?.invalid}"
                formControlName="plantId" id="newUserPlantId">
          <option value="" disabled>Select Plant</option>
          <option *ngFor="let plant of availablePlants" [value]="plant.id">{{ plant.name }}</option>
        </select>
        <div *ngIf="(addUserForm.get('plantId')?.touched || addUserForm.get('plantId')?.dirty) && addUserForm.get('plantId')?.errors?.['required']" class="text-danger mt-1 small">
          Please select a plant.
        </div>
      </div>

      <!-- Department -->
      <div class="mb-3">
        <label for="newUserDepartmentId" class="form-label">Department <span class="text-danger">*</span></label>
        <select class="form-select"
                [ngClass]="{'is-invalid': (addUserForm.get('departmentId')?.touched || addUserForm.get('departmentId')?.dirty) && addUserForm.get('departmentId')?.invalid}"
                formControlName="departmentId" id="newUserDepartmentId">
          <option value="" disabled>Select Department</option>
          <option *ngFor="let dept of availableDepartments" [value]="dept.id">{{ dept.title }}</option>
        </select>
         <div *ngIf="(addUserForm.get('departmentId')?.touched || addUserForm.get('departmentId')?.dirty) && addUserForm.get('departmentId')?.errors?.['required']" class="text-danger mt-1 small">
          Department is required.
        </div>
      </div>

      <!-- Designation -->
      <div class="mb-3">
        <label for="newUserDesignationId" class="form-label">Designation <span class="text-danger">*</span></label>
        <select class="form-select"
                [ngClass]="{'is-invalid': (addUserForm.get('designationId')?.touched || addUserForm.get('designationId')?.dirty) && addUserForm.get('designationId')?.invalid}"
                formControlName="designationId" id="newUserDesignationId">
          <option value="" disabled>Select Designation</option>
          <option *ngFor="let desg of availableDesignations" [value]="desg.id">{{ desg.title }}</option>
        </select>
        <div *ngIf="(addUserForm.get('designationId')?.touched || addUserForm.get('designationId')?.dirty) && addUserForm.get('designationId')?.errors?.['required']" class="text-danger mt-1 small">
          Designation is required.
        </div>
      </div>

      <!-- Form Action Buttons -->
      <div class="d-flex gap-2 mt-4">
        <button type="submit" class="btn adani-btn flex-grow-1" [disabled]="isAddingUser || isNewUserEmailExist">
          <span *ngIf="!isAddingUser">
            <i class="bi bi-person-plus me-1"></i> Create User
          </span>
          <span *ngIf="isAddingUser">
            <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
            Creating...
          </span>
        </button>
        <button type="button" (click)="resetAddUserForm()" class="btn btn-secondary flex-grow-1">
            <i class="bi bi-arrow-clockwise me-1"></i> Reset
        </button>
         <button type="button" (click)="closeAddUserModal()" class="btn btn-outline-secondary flex-grow-1">
           <i class="bi bi-x-lg me-1"></i> Cancel
         </button>
      </div>

    </form>
  </div>
</app-offcanvas>
