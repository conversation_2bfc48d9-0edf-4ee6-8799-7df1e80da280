import { Component, OnInit, <PERSON>Child ,inject,ChangeDetectorRef} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ApexAxisChartSeries,
  ApexChart,
  ApexXAxis,
  ApexTitleSubtitle,
  ApexDataLabels,
  ApexStroke,
  ApexGrid,
  ApexPlotOptions,
  ApexYAxis,
  ApexLegend,
  ApexTooltip,
  ApexFill,
  ApexResponsive,
  NgApexchartsModule
} from "ng-apexcharts";
import { SafetyTrainingService } from '../../../services/safety-training/safety-training.service';

// Define a type alias for cleaner chart options type
export type ChartOptions = {
  series: ApexAxisChartSeries | any[]; // Use any[] for pie charts
  chart: ApexChart;
  xaxis?: ApexXAxis;
  yaxis?: ApexYAxis | ApexYAxis[];
  title?: ApexTitleSubtitle;
  labels?: string[]; // Still needed for bar chart tooltips sometimes, but categories are primary
  stroke?: ApexStroke;
  dataLabels?: ApexDataLabels;
  fill?: ApexFill;
  legend?: ApexLegend;
  tooltip?: ApexTooltip;
  plotOptions?: ApexPlotOptions;
  grid?: ApexGrid;
  responsive?: ApexResponsive[];
  colors?: string[]; // Added for custom colors
};

// Interface for the raw safety training data (simplified for chart purposes)
interface SafetyTrainingData {
  month: string; // e.g., "2023-04-01"
  companyMaleParticipants: number;
  companyFemaleParticipants: number;
  trainingType: string; // "Internal" or "External"
  totalCompanyManhours: number;
  totalContractorManhours: number;
  totalCompanyEmployeeParticipants: number; // Added
  totalContractorEmployeeParticipants: number; // Added
  totalParticipants: number; // Added
}

@Component({
  selector: 'app-safety-training-dashboard',
  standalone: true,
  imports: [CommonModule, NgApexchartsModule], // Import NgApexchartsModule
  templateUrl: './safety-training-dashboard.component.html',
  styleUrls: ['./safety-training-dashboard.component.scss']
})
export class SafetyTrainingDashboardComponent implements OnInit {

  // Define properties for chart options
  public monthlyTrainingsChartOptions: Partial<ChartOptions> | any;
  public genderDistributionChartOptions: Partial<ChartOptions> | any;
  public trainingTypeChartOptions: Partial<ChartOptions> | any;
  public manhoursChartOptions: Partial<ChartOptions> | any;
  public participantTypeChartOptions: Partial<ChartOptions> | any; // New Chart 5
  public avgParticipantsChartOptions: Partial<ChartOptions> | any; // New Chart 6
  private cdr = inject(ChangeDetectorRef);
  isLoading: boolean = false;
  year: number = new Date().getFullYear(); // Current year
  constructor(private safetyTrainingService: SafetyTrainingService) {
    // Initialize chart options with basic structure
    this.initializeChartOptions();
  }

  ngOnInit(): void {
    // this.loadChartData();
    this.getMaleFemaleTrainingCount()
    this.getTrainingTypeCount()
    this.getParticipantTypeCount()
    this.getAvgParticipantsCount()
    this.getMonthlyTrainingCount()
    this.getManhoursCount()
  }

  initializeChartOptions(): void {
    this.monthlyTrainingsChartOptions = {
      series: [],
      chart: {
        type: 'bar', height: 350,
        toolbar: {
          show: false
        },
      },
      xaxis: { categories: [] },
      title: { text: 'Monthly Training Records' }, // Re-enabled title
      colors: ['#0b74b0'], // Adani Blue
      dataLabels: { enabled: false }
    };

    // Changed to Bar Chart
    this.genderDistributionChartOptions = {
      series: [],
      chart: {
        type: 'bar', height: 350,
        toolbar: {
          show: false
        },
      },
      plotOptions: { bar: { distributed: true } }, // Distribute colors to bars
      colors: ['#3498db', '#e74c3c'], // Blue, Red
      xaxis: { categories: ['Male', 'Female'] },
      title: { text: 'Company Participant Gender Distribution' }, // Re-enabled title
      legend: { show: false },
      dataLabels: { enabled: true }
    };

    // Changed to Bar Chart
    this.trainingTypeChartOptions = {
      series: [],
      chart: {
        type: 'bar', height: 350,
        toolbar: {
          show: false
        },
      },
      plotOptions: { bar: { distributed: true } }, // Distribute colors to bars
      colors: ['#2ecc71', '#f39c12'], // Green, Orange
      xaxis: { categories: ['Internal Training', 'External Training'] },
      title: { text: 'Training Type Distribution' }, // Re-enabled title
      legend: { show: false },
      dataLabels: { enabled: true }
    };

    this.manhoursChartOptions = {
      series: [],
      chart: {
        type: 'bar', height: 350, stacked: true,
        toolbar: {
          show: false
        },
      },
      plotOptions: { bar: { horizontal: false } },
      colors: ['#75479c', '#bd3681'], // Adani Purple, Pink
      xaxis: { categories: [] },
      yaxis: { title: { text: 'Manhours' } },
      title: { text: 'Monthly Manhours (Company vs Contractor)' }, // Re-enabled title
      legend: { position: 'top' },
      fill: { opacity: 1 },
      dataLabels: { enabled: false }
    };

    // Changed to Bar Chart
    this.participantTypeChartOptions = {
      series: [],
      chart: {
        type: 'bar', height: 350,
        toolbar: {
          show: false
        },
      },
      plotOptions: { bar: { distributed: true } }, // Distribute colors to bars
      colors: ['#1abc9c', '#9b59b6'], // Turquoise, Purple
      xaxis: { categories: ['Company', 'Contractor'] },
      title: { text: 'Participant Type Distribution (Overall)' }, // Re-enabled title
      legend: { show: false },
      dataLabels: { enabled: true }
    };

    // New Chart 6: Average Participants per Training (Monthly)
    this.avgParticipantsChartOptions = {
      series: [],
      chart: {
        type: 'line', height: 350,
        toolbar: {
          show: false
        },
      },
      colors: ['#e67e22'], // Dark Orange
      xaxis: { categories: [] },
      yaxis: { title: { text: 'Avg. Participants' } },
      title: { text: 'Average Participants per Training (Monthly)' }, // Re-enabled title
      stroke: { curve: 'smooth' },
      dataLabels: { enabled: false },

    };
  }

  loadChartData(): void {
    this.isLoading = true;
    // Simulate fetching data
    // Replace this with actual API call to fetch safety training records
    const mockData: SafetyTrainingData[] = [
      { month: '2023-01-01', companyMaleParticipants: 15, companyFemaleParticipants: 5, trainingType: 'Internal', totalCompanyManhours: 40, totalContractorManhours: 20, totalCompanyEmployeeParticipants: 20, totalContractorEmployeeParticipants: 10, totalParticipants: 30 },
      { month: '2023-02-01', companyMaleParticipants: 12, companyFemaleParticipants: 8, trainingType: 'External', totalCompanyManhours: 30, totalContractorManhours: 25, totalCompanyEmployeeParticipants: 20, totalContractorEmployeeParticipants: 15, totalParticipants: 35 },
      { month: '2023-03-01', companyMaleParticipants: 20, companyFemaleParticipants: 3, trainingType: 'Internal', totalCompanyManhours: 50, totalContractorManhours: 15, totalCompanyEmployeeParticipants: 23, totalContractorEmployeeParticipants: 5, totalParticipants: 28 },
      { month: '2023-03-01', companyMaleParticipants: 10, companyFemaleParticipants: 10, trainingType: 'Internal', totalCompanyManhours: 40, totalContractorManhours: 10, totalCompanyEmployeeParticipants: 20, totalContractorEmployeeParticipants: 8, totalParticipants: 28 }, // Second training in March
      { month: '2023-04-01', companyMaleParticipants: 10, companyFemaleParticipants: 2, trainingType: 'Internal', totalCompanyManhours: 24, totalContractorManhours: 36, totalCompanyEmployeeParticipants: 12, totalContractorEmployeeParticipants: 18, totalParticipants: 30 },
      { month: '2023-05-01', companyMaleParticipants: 18, companyFemaleParticipants: 7, trainingType: 'External', totalCompanyManhours: 45, totalContractorManhours: 30, totalCompanyEmployeeParticipants: 25, totalContractorEmployeeParticipants: 20, totalParticipants: 45 },
      { month: '2023-06-01', companyMaleParticipants: 22, companyFemaleParticipants: 6, trainingType: 'Internal', totalCompanyManhours: 55, totalContractorManhours: 22, totalCompanyEmployeeParticipants: 28, totalContractorEmployeeParticipants: 12, totalParticipants: 40 },
    ];

    setTimeout(() => {
      this.processChartData(mockData);
      this.isLoading = false;
    }, 1000); // Simulate network delay
  }

  processChartData(data: SafetyTrainingData[]): void {
    // 1. Monthly Training Count
    const monthlyCounts: { [key: string]: number } = {};
    const monthlyTotals: { [key: string]: { totalParticipants: number, count: number } } = {}; // For Avg Participants

    data.forEach(record => {
      const monthYear = new Date(record.month).toLocaleString('default', { month: 'short', year: 'numeric' });
      // Count trainings
      monthlyCounts[monthYear] = (monthlyCounts[monthYear] || 0) + 1;
      // Sum participants and count for average calculation
      if (!monthlyTotals[monthYear]) {
        monthlyTotals[monthYear] = { totalParticipants: 0, count: 0 };
      }
      monthlyTotals[monthYear].totalParticipants += record.totalParticipants;
      monthlyTotals[monthYear].count += 1;
    });
    const sortedMonths = Object.keys(monthlyCounts).sort((a, b) => new Date(a).getTime() - new Date(b).getTime());
    this.monthlyTrainingsChartOptions.series = [{ name: 'Trainings', data: sortedMonths.map(month => monthlyCounts[month]) }];
    this.monthlyTrainingsChartOptions.xaxis = { categories: sortedMonths };


    // 2. Gender Distribution (Bar Chart)
    let totalMale = 0;
    let totalFemale = 0;
    data.forEach(record => {
      totalMale += record.companyMaleParticipants;
      totalFemale += record.companyFemaleParticipants;
    });
    // Format for bar chart series
    this.genderDistributionChartOptions.series = [{ name: 'Participants', data: [totalMale, totalFemale] }];

    // 3. Training Type Distribution (Bar Chart)
    let internalCount = 0;
    let externalCount = 0;
    data.forEach(record => {
      if (record.trainingType.toLowerCase() === 'internal') {
        internalCount++;
      } else if (record.trainingType.toLowerCase() === 'external') {
        externalCount++;
      }
    });
    // Format for bar chart series
    this.trainingTypeChartOptions.series = [{ name: 'Count', data: [internalCount, externalCount] }];

    // 4. Manhours by Type (Monthly)
    const monthlyManhours: { [key: string]: { company: number, contractor: number } } = {};
    data.forEach(record => {
      const monthYear = new Date(record.month).toLocaleString('default', { month: 'short', year: 'numeric' });
      if (!monthlyManhours[monthYear]) {
        monthlyManhours[monthYear] = { company: 0, contractor: 0 };
      }
      monthlyManhours[monthYear].company += record.totalCompanyManhours;
      monthlyManhours[monthYear].contractor += record.totalContractorManhours;
    });
    const sortedManhourMonths = Object.keys(monthlyManhours).sort((a, b) => new Date(a).getTime() - new Date(b).getTime());
    this.manhoursChartOptions.series = [
      { name: 'Company Manhours', data: sortedManhourMonths.map(month => monthlyManhours[month].company) },
      { name: 'Contractor Manhours', data: sortedManhourMonths.map(month => monthlyManhours[month].contractor) }
    ];
    this.manhoursChartOptions.xaxis = { categories: sortedManhourMonths };

    // 5. Participant Type Distribution (Bar Chart)
    let totalCompanyParticipants = 0;
    let totalContractorParticipants = 0;
    data.forEach(record => {
      totalCompanyParticipants += record.totalCompanyEmployeeParticipants;
      totalContractorParticipants += record.totalContractorEmployeeParticipants;
    });
    // Format for bar chart series
    this.participantTypeChartOptions.series = [{ name: 'Participants', data: [totalCompanyParticipants, totalContractorParticipants] }];

    // 6. Average Participants per Training (Monthly)
    const avgParticipantsData = sortedMonths.map(month => {
      const monthlyData = monthlyTotals[month];
      return monthlyData.count > 0 ? parseFloat((monthlyData.totalParticipants / monthlyData.count).toFixed(2)) : 0;
    });
    this.avgParticipantsChartOptions.series = [{ name: 'Avg Participants', data: avgParticipantsData }];
    this.avgParticipantsChartOptions.xaxis = { categories: sortedMonths };

    // Important: Trigger change detection if needed, although direct assignment usually works
    // this.cdr.detectChanges();
  }
  getMaleFemaleTrainingCount() {
    this.safetyTrainingService.getMaleFemaleCount(this.year).then((res: any) => {
      this.genderDistributionChartOptions.series = [{ name: 'Participants', data: [res.data[0].male, res.data[0].female] }]
       this.cdr.detectChanges();
    })
   
  }
  getTrainingTypeCount() {
    this.safetyTrainingService.getTrainingTypeCount(this.year).then((res: any) => {
      this.trainingTypeChartOptions.series = [{ name: 'Count', data: [res.data[0].internal, res.data[0].external] }]
      this.cdr.detectChanges();
    })
    
  }
  getParticipantTypeCount() {
    this.safetyTrainingService.getParticipantTypeCount(this.year).then((res: any) => {
      this.participantTypeChartOptions.series = [{ name: 'Participants', data: [res.data[0].companyParticipant, res.data[0].contractParticipant] }]
      this.cdr.detectChanges();
    })
    
  }
  getManhoursCount() {
    this.safetyTrainingService.getTrainingMonthWiseManHourCount(this.year).then((response: any) => {
      const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
        'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

      const categories = response.data.map((item: any) => monthNames[item.month - 1]);
      console.log(categories);
      const companyManhours = Object.values(response.data).map((item: any) => item.companyEmpManhour);
      const contractorManhours = Object.values(response.data).map((item: any) => item.contractEmpManhour);
      this.manhoursChartOptions.series = [{ name: 'Company Manhours', data: companyManhours },
      { name: 'Contractor Manhours', data: contractorManhours }]
      this.manhoursChartOptions.xaxis = { categories: categories };
       this.cdr.detectChanges();
    })
   
  }
  getAvgParticipantsCount() {
    this.safetyTrainingService.getAverageParticipantPerTraining(this.year).then((response: any) => {
      const counts = Object.values(response.data).map((item: any) => item.averageParticipants);
      const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
        'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

      const categories = response.data.map((item: any) => monthNames[item.month - 1]);

      this.avgParticipantsChartOptions.series = [{ name: 'Trainings', data: counts }]
      this.avgParticipantsChartOptions.xaxis = { categories: categories };
       this.cdr.detectChanges();
    })
   
  }
  getMonthlyTrainingCount() {
    this.safetyTrainingService.getTrainingMonthWiseCount(this.year).then((response: any) => {
      const counts = Object.values(response.data).map((item: any) => item.count);
      const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
        'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

      const categories = response.data.map((item: any) => monthNames[item.month - 1]);

      this.monthlyTrainingsChartOptions.series = [{ name: 'Trainings', data: counts }]
      this.monthlyTrainingsChartOptions.xaxis = { categories: categories };
      console.log(this.monthlyTrainingsChartOptions)
      this.cdr.detectChanges();
    })
    
  }
}
