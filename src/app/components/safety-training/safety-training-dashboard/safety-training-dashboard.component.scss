// Add styles to visually separate the charts within the main dashboard card

// Target the row containing the charts inside the main card body
.card > .card-body > .row.g-3 {
  > .col-md-4 { // Target the direct children columns
    border: 1px solid #e0e0e0; // Add a light border
    border-radius: 0.25rem; // Optional: slightly rounded corners
    padding: 1rem; // Add some internal padding around the chart div
    background-color: #fdfdfd; // Optional: slightly off-white background

    // Ensure the chart div itself doesn't have extra margin/padding interfering
    > div[id$="Chart"] { // Target divs with IDs ending in "Chart"
        margin: 0;
        padding: 0;
    }
  }
}