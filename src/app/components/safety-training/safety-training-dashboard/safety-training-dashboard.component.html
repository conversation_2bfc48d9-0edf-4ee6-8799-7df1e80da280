<div class="container-fluid mt-3">
  <div *ngIf="isLoading" class="text-center p-5">
    <span class="spinner-border text-primary" role="status"></span>
    <p class="mt-2">Loading dashboard data...</p>
  </div>

  <div *ngIf="!isLoading">
    <div class="card custom-card">
      <div class="card-header">
        <h5 class="mb-0">Safety Training Dashboard</h5>
      </div>
      <div class="card-body">
        <div class="row g-3">
          <!-- Monthly Trainings Chart -->
          <div class="col-md-4">
            <div id="monthlyTrainingsChart"  *ngIf="monthlyTrainingsChartOptions?.series?.length">
              <apx-chart
                [series]="monthlyTrainingsChartOptions.series"
                [chart]="monthlyTrainingsChartOptions.chart"
                [xaxis]="monthlyTrainingsChartOptions.xaxis"
                [colors]="monthlyTrainingsChartOptions.colors"
                [dataLabels]="monthlyTrainingsChartOptions.dataLabels"
                [title]="monthlyTrainingsChartOptions.title"
              ></apx-chart>
            </div>
          </div>

          <!-- Gender Distribution Chart -->
          <div class="col-md-4">
            <div id="genderDistributionChart"  *ngIf="genderDistributionChartOptions?.series?.length">
              <apx-chart
                [series]="genderDistributionChartOptions.series"
                [chart]="genderDistributionChartOptions.chart"
                [xaxis]="genderDistributionChartOptions.xaxis"
                [plotOptions]="genderDistributionChartOptions.plotOptions"
                [colors]="genderDistributionChartOptions.colors"
                [legend]="genderDistributionChartOptions.legend"
                [dataLabels]="genderDistributionChartOptions.dataLabels"
                [title]="genderDistributionChartOptions.title"
              ></apx-chart>
            </div>
          </div>

          <!-- Training Type Chart -->
          <div class="col-md-4">
            <div id="trainingTypeChart"  *ngIf="trainingTypeChartOptions?.series?.length">
              <apx-chart
                [series]="trainingTypeChartOptions.series"
                [chart]="trainingTypeChartOptions.chart"
                [xaxis]="trainingTypeChartOptions.xaxis"
                [plotOptions]="trainingTypeChartOptions.plotOptions"
                [colors]="trainingTypeChartOptions.colors"
                [legend]="trainingTypeChartOptions.legend"
                [dataLabels]="trainingTypeChartOptions.dataLabels"
                [title]="trainingTypeChartOptions.title"
              ></apx-chart>
            </div>
          </div>

          <!-- Manhours Chart -->
          <div class="col-md-4">
            <div id="manhoursChart"  *ngIf="manhoursChartOptions?.series?.length">
              <apx-chart
                [series]="manhoursChartOptions.series"
                [chart]="manhoursChartOptions.chart"
                [plotOptions]="manhoursChartOptions.plotOptions"
                [xaxis]="manhoursChartOptions.xaxis"
                [yaxis]="manhoursChartOptions.yaxis"
                [colors]="manhoursChartOptions.colors"
                [legend]="manhoursChartOptions.legend"
                [fill]="manhoursChartOptions.fill"
                [dataLabels]="manhoursChartOptions.dataLabels"
                [title]="manhoursChartOptions.title"
              ></apx-chart>
            </div>
          </div>

          <!-- Participant Type Chart -->
          <div class="col-md-4">
            <div id="participantTypeChart"  *ngIf="participantTypeChartOptions?.series?.length">
              <apx-chart
                [series]="participantTypeChartOptions.series"
                [chart]="participantTypeChartOptions.chart"
                [xaxis]="participantTypeChartOptions.xaxis"
                [plotOptions]="participantTypeChartOptions.plotOptions"
                [colors]="participantTypeChartOptions.colors"
                [legend]="participantTypeChartOptions.legend"
                [dataLabels]="participantTypeChartOptions.dataLabels"
                [title]="participantTypeChartOptions.title"
              ></apx-chart>
            </div>
          </div>

          <!-- Average Participants Chart -->
           <div class="col-md-4">
            <div id="avgParticipantsChart"  *ngIf="avgParticipantsChartOptions?.series?.length">
              <apx-chart
                [series]="avgParticipantsChartOptions.series"
                [chart]="avgParticipantsChartOptions.chart"
                [xaxis]="avgParticipantsChartOptions.xaxis"
                [yaxis]="avgParticipantsChartOptions.yaxis"
                [colors]="avgParticipantsChartOptions.colors"
                [stroke]="avgParticipantsChartOptions.stroke"
                [dataLabels]="avgParticipantsChartOptions.dataLabels"
                [title]="avgParticipantsChartOptions.title"
              ></apx-chart>
            </div>
          </div>

        </div> <!-- End row -->
      </div> <!-- End outer card-body -->
    </div> <!-- End outer card -->
  </div> <!-- End *ngIf="!isLoading" -->
</div> <!-- End container-fluid -->
