.formPaddingSet {
    padding-top: 1px !important;
}

.or-divider {
    display: flex;
    align-items: center;
    text-align: center;
    margin: 20px 0;
    margin-bottom: 5px !important;
}

.or-divider::before,
.or-divider::after {
    content: '';
    flex: 1;
    border-bottom: 1px solid #ccc;
}

.or-divider:not(:empty)::before {
    margin-right: .75em;
}

.or-divider:not(:empty)::after {
    margin-left: .75em;
}

.or-divider span {
    color: #888;
    font-weight: bold;
    letter-spacing: 1px;
}

.uploadFileTitle {
    color: #0B74B0;
}

.upload-box {
    padding: 20px;
    width: 522px;
    height: 150px;
    text-align: center;
    border: 1px dashed #4796C3;
    border-radius: 5px;
    background-color: #f9f9f9;
    cursor: pointer;
    transition: background-color 0.3s ease-in-out;

    &.dragging {
        background-color: #e3f2fd;
    }

    p {
        font-size: 14px;
        color: #555;
    }
}

ul {
    list-style: none;
    padding: 0;

    li {
        div {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 6px;
            border-radius: 5px;
            margin-top: 5px;
        }

        .valid {
            border: 1px solid #6ACD75;
            color: #6ACD75;
        }

        .error {
            border: 1px solid #E95454;
            color: #E95454;
        }

        button {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
        }

        p {
            margin: 4px 0 0 0;
        }
    }
}

.invalidFileStyle {
    margin-bottom: 0px;
    margin-top: 25px;
}

.uploadedFileStyle {
    margin-bottom: -8px;
}

.uploadedFileStyle div {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px;
    border-radius: 5px;
    margin-top: 5px;
}

.uploadedFileStyle .valid {
    border: 1px solid #6ACD75;
    color: #6ACD75;
}

.invalidFileStyle div {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px;
    border-radius: 5px;
    margin-top: 5px;
}

.invalidFileStyle .error {
    border: 1px solid #E95454;
    color: #E95454;
}

.uploadedFileStyle button,
.invalidFileStyle button {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 16px;
}

.uploadedFileStyle p,
.invalidFileStyle p {
    margin: 4px 0 0 0;
}

.button-group {
    display: flex;
    gap: 10px;

    button {
        min-width: 80px;
    }
}

.file-list {
    min-height: 55px;
    min-width: 350px;
    /* Adjust as needed */
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.uploadFile {
    margin-top: 2px;
}

.table-container {
    width: 100%;
    overflow-x: auto;
}

.card-body-validRecord {
    height: 70vh;
    width: 1050px;
    overflow: auto;
}

.table-responsive {
    overflow-x: auto;
    white-space: nowrap;
    max-width: 100%;
}

table {
    min-width: 1000px;
    /* Adjust based on the number of columns */
    border-collapse: collapse;
}

.img-thumbnail {
    width: 100px;
    height: 100px;
}

.table-header th {
    font-size: 12px;
    background-color: #0B74B0 !important;
    color: white !important;
    text-align: center;
}

td {
    font-size: 12px;
}

i.edit {
    font-size: 12px;
}

.actions {
    text-align: center !important;
    vertical-align: middle !important;
}

.table-container {
    overflow-x: auto;
}

/* Add this to your component's style or global styles */
.fixed-cell {
    max-width: 200px;
    /* Adjust as needed */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    position: relative;
}

.fixed-cell:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    left: 0;
    top: 100%;
    background: #333;
    color: #fff;
    padding: 5px 10px;
    border-radius: 4px;
    white-space: normal;
    z-index: 10;
}

.customStyle {
    width: 200px
}


.snackbar-custom {
    background: #aa0202 !important;
    color: white !important;
    border-radius: 8px !important;
    box-shadow: 0 3px 5px -1px rgba(0, 0, 0, 0.2), 0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12) !important;
}

.filedsMarginSet {
    margin-bottom: 5px;
}

::ng-deep .ng-select .ng-select-container {
    min-height: 32px !important;
    /* Adjust as needed */
    height: 32px !important;
    /* Optional: force a specific height */
    font-size: 14px;
    /* Optional: reduce font size for compactness */
    padding-top: 2px;
    /* Optional: tweak padding */
    padding-bottom: 2px;
}