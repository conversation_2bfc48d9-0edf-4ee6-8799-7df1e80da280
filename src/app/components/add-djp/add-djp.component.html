<app-toast-message></app-toast-message>
<div class="row align-items-center">
    <div class="col-12 col-md text-center mb-2 mb-md-0">
        <app-tab [tabs]="tabs" [selectedTabIndex]="selectedTabIndex" (tabSelected)="onTabSelected($event)"></app-tab>
    </div>
</div>

<div class="card custom-card" id="djp-list" *ngIf="selectedTabIndex == 0"> <!-- Changed ID -->
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col-12 col-md">
                <h6 class="mb-2 mb-md-0">Add DJP</h6>
            </div>
            <div class="col-12 col-md-auto">
                <div
                    class="d-flex flex-column flex-md-row align-items-stretch align-items-md-center justify-content-end">
                    <input type="text" class="form-control form-control-sm mb-2 ms-0 ms-md-1"
                        style="max-width: 100%; width: 200px; margin-right: 8px;" placeholder="Search here"
                        [(ngModel)]="searchText" (keyup)="searchTableData()" />

                    <button type="button" class="btn btn-sm adani-btn mb-2 mb-md-0 me-0 me-md-2"
                        (click)="openDJPModal('Add')">
                        <i class="bi bi-plus-circle me-1"></i> Add New DJP
                    </button>

                    <button type="button" class="btn btn-sm adani-btn mb-2 mb-md-0 me-0 me-md-2"
                        (click)="downloadfile()">
                        <i class="bi bi-download me-1"></i> Download Template
                    </button>

                    <div ngbDropdown class="d-inline-block mb-2 mb-md-0 me-0 me-md-2">
                        <button type="button" class="btn btn-sm adani-btn dropdown-toggle w-100"
                            id="downloadNotifExcelDropdown" ngbDropdownToggle
                            [disabled]="isDownloadingExcel || listLoading">
                            <span *ngIf="!isDownloadingExcel">
                                <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
                            </span>
                            <span *ngIf="isDownloadingExcel">
                                <span class="spinner-border spinner-border-sm me-1" role="status"
                                    aria-hidden="true"></span>
                                Downloading
                                {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '') }}...
                            </span>
                        </button>
                        <ul ngbDropdownMenu aria-labelledby="downloadNotifExcelDropdown">
                            <li>
                                <button ngbDropdownItem (click)="downloadExcel('current', 'info')"
                                    [disabled]="isDownloadingExcel || listLoading || (filteredDjpResponse?.length ?? 0) === 0">
                                    <i class="bi bi-download me-1"></i> Download Current Page ({{
                                    filteredDjpResponse?.length ?? 0 }})
                                </button>
                            </li>
                            <li>
                                <button ngbDropdownItem (click)="downloadExcel('all', 'info')"
                                    [disabled]="isDownloadingExcel || listLoading || totalItems === 0">
                                    <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
                                </button>
                            </li>
                        </ul>
                    </div>

                    <img src="../../../assets/svg/filter.svg" class="filter-button ms-0 ms-md-1" alt="Filter"
                        style="width: 35px; cursor: pointer;" title="Filter DJP" (click)="openFilterModal()" />
                </div>
            </div>
        </div>
    </div>


    <div class="card-body">
        <div class="table-container">
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-header">
                        <tr class="text-center">
                            <th scope="col">Title</th>
                            <th scope="col">Zone</th>
                            <th scope="col">Job Description</th>
                            <th scope="col">Criticality</th>
                            <th scope="col">Start Date & Time</th>
                            <th scope="col">End Date & Time</th>
                            <th scope="col">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngIf="listLoading">
                            <td colspan="5" class="text-center p-4">
                                <div class="spinner-border spinner-border-sm text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <span class="ms-2">Loading Djp...</span>
                            </td>
                        </tr>

                        <tr *ngIf="!listLoading && (!filteredDjpResponse  || filteredDjpResponse .length === 0)">
                            <td colspan="8" class="text-center p-4 text-muted">
                                <i class="bi bi-info-circle me-2"></i>No Djp found matching the criteria.
                            </td>
                        </tr>

                        <tr *ngFor="let djp of filteredDjpResponse " class="text-center">

                            <td>{{djp.djpTitle}}</td>
                            <td>{{djp?.zone?.zoneName}}</td>
                            <td class="fixed-cell" [matTooltip]="djp?.jobDescription">
                                {{ djp?.jobDescription }}
                            </td>
                            <td>{{ djp.risk }}</td>

                            <td>{{djp?.fromDate}}</td>
                            <td>{{djp?.toDate}}</td>
                            <td class="actions">

                                <button class="btn btn-sm btn-outline-primary" title="Edit Djp"
                                    (click)="editDjpRecord(djp)">
                                    <i class="bi bi-pencil"></i>
                                </button>
                            </td>

                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="card-footer text-muted text-center" *ngIf="filteredDjpResponse .length > 0">
        <!-- Hide footer if no data -->
        <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
            (pageChange)="onPageChange($event)"></app-pagination>
    </div>

    <app-offcanvas [title]="'Add New DJP'" [width]="'550px'" *ngIf="isAddEditModalOpen"
        (onClickCross)="closeAddEditModal()">

        <app-tab [tabs]="addDJPtabs" [selectedTabIndex]="selectedAddDjpTabIndex"
            (tabSelected)="onAddDjpTabSelected($event)"></app-tab>

        <div class="add-edit-container p-3 formPaddingSet" *ngIf="selectedAddDjpTabIndex === 0">
            <form #djpForm="ngForm" (ngSubmit)="openCreateSingleDjp()">
                <div class="row g-3" *ngIf="selectedDjpData !== null">

                    <div class="col-6 filedsMarginSet">
                        <label for="djpTitle" class="form-label">
                            Title <span class="text-danger">*</span>
                        </label>
                        <input type="text" id="djpTitle" class="form-control form-control-sm" placeholder="Enter Title"
                            [(ngModel)]="selectedDjpData.djpTitle" name="djpTitle" required #titleInput="ngModel"
                            minlength="5" maxlength="30" pattern="^(?=.*[a-zA-Z]{5,})[a-zA-Z0-9\s]+$"
                            [ngClass]="{'is-invalid': titleInput.invalid && (titleInput.dirty || titleInput.touched)}">
                        <div *ngIf="titleInput.invalid && (titleInput.dirty || titleInput.touched)"
                            class="text-danger mt-1 small">
                            <div *ngIf="titleInput.errors?.['pattern']">
                                Title should contain only letters, numbers, and spaces, and must include at least five
                                letter.
                            </div>
                            <div *ngIf="titleInput.errors?.['required']">
                                Title is required.
                            </div>
                            <div *ngIf="titleInput.errors?.['maxlength']">
                                Title cannot exceed 30 characters.
                            </div>
                        </div>
                        <!-- Character count display - only visible when there's text -->
                        <small *ngIf="selectedDjpData.djpTitle" class="text-muted d-block text-end mt-1">
                            {{ selectedDjpData.djpTitle.length }}/30 characters
                        </small>
                    </div>

                    <div class="col-6 filedsMarginSet" *ngIf="isSuperAdminLogin">
                        <label class="form-label" for="plantId">Select Plant <span class="text-danger">*</span></label>
                        <select id="filterPlant" class="form-select" [(ngModel)]="selectedDjpData.plantId"
                            #plantselect="ngModel" required name="plantId"
                            (ngModelChange)="onPlantSelectionChange($event)">
                            <option [ngValue]="null">All Plants</option>
                            <option *ngFor="let plant of availablePlants" [value]="plant.id">{{ plant.name }}</option>
                        </select>
                        <div *ngIf="plantselect.invalid && plantselect.touched" class="text-danger mt-1 small">
                            <div *ngIf="plantselect.errors?.['required']">Plant is required.</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <label for="zoneId" class="form-label">Select Zone <span class="text-danger">*</span></label>
                        <select class="form-select" id="filterZoneId" [(ngModel)]="selectedDjpData.zoneId" name="zoneId"
                            #ngModelRef="ngModel" required> <!-- Disable if no plant selected -->
                            <option [ngValue]="null">
                                {{ availableZones.length > 0 ? 'Select Zone' : 'No zones found' }}
                            </option>
                            <option *ngFor="let zone of availableZones" [value]="zone.id">{{ zone.zoneName }}</option>
                        </select>
                        <div *ngIf="ngModelRef.invalid && ngModelRef.touched" class="text-danger mt-1 small">
                            <div *ngIf="ngModelRef.errors?.['required']">Zone is required.</div>
                        </div>
                    </div>

                    <div class="col-6 filedsMarginSet">
                        <label class="form-label" for="risk">Criticality <span class="text-danger">*</span></label>
                        <select id="risk" class="form-select" [(ngModel)]="selectedDjpData.risk" name="risk"
                            #critical="ngModel" required>
                            <option [ngValue]="null" disabled>Select Criticality</option>
                            <option *ngFor="let criticalityValue of criticalityOptions"
                                [ngValue]="criticalityValue.value">{{
                                criticalityValue.label }}
                            </option>
                        </select>
                        <div *ngIf="critical.invalid && (critical.dirty || critical.touched)"
                            class="text-danger mt-1 small">
                            <div *ngIf="critical.errors?.['required']">Criticality is required.</div>
                        </div>
                    </div>

                    <div class="col-6">
                        <label class="form-label" for="adminId">Plant User <span class="text-danger">*</span></label>
                        <ng-select id="adminId" [(ngModel)]="selectedDjpData.adminId" name="adminId"
                            [items]="availablePlantAdmins" bindLabel="fullName" bindValue="id" placeholder="Select User"
                            required #createPlantAdminCtrl="ngModel" [searchFn]="customSearchFn">

                            <ng-template ng-option-tmp let-item="item">
                                <div>
                                    <div><strong>{{ item.email }}</strong></div>
                                    <div class="small text-muted">{{ item.fullName }}</div>
                                </div>
                            </ng-template>

                            <ng-template ng-label-tmp let-item="item">
                                <span>{{ item.fullName }}</span>
                            </ng-template>
                        </ng-select>

                        <div *ngIf="createPlantAdminCtrl.invalid && (createPlantAdminCtrl.dirty || createPlantAdminCtrl.touched)"
                            class="text-danger small mt-1">
                            Plant user is required.
                        </div>
                    </div>

                    <!-- <div class="col-12 filedsMarginSet">
                        <label for="jobDescription" class="form-label">Job description <span
                                class="text-danger">*</span></label>
                        <textarea id="jobDescription" class="form-control form-control-sm" rows="4"
                            placeholder="Enter Job description" [(ngModel)]="selectedDjpData.jobDescription"
                            name="jobDescription" required #messageInput="ngModel" maxlength="200"></textarea>
                        <div class="d-flex justify-content-between mt-1">
                            <div *ngIf="messageInput.invalid && (messageInput.dirty || messageInput.touched)"
                                class="text-danger small">
                                <div *ngIf="messageInput.errors?.['required']">Job description is required.</div>
                                <div *ngIf="messageInput.errors?.['maxlength']">Job description cannot exceed 200
                                    characters.
                                </div>
                            </div>
                            <small *ngIf="selectedDjpData.jobDescription" class="text-muted ms-auto">
                                {{ selectedDjpData.jobDescription.length }}/200 characters
                            </small>
                        </div>
                    </div> -->

                    <div class="col-12 filedsMarginSet">
                        <label class="form-label" for="remark">Job Description <span class="text-danger">
                                *</span></label>
                        <textarea id="remark" class="form-control form-control-sm"
                            [(ngModel)]="selectedDjpData.jobDescription" placeholder="Enter Job Description"
                            name="remark" minlength="10" maxlength="300" rows="4"
                            pattern="^(?=(?:.*[a-zA-Z]){10,})[a-zA-Z0-9\s.,!?'\()\-]+$" #remark="ngModel"
                            required></textarea>
                        <div *ngIf="remark.errors?.['required'] && (remark.dirty || remark.touched || djpForm.submitted)"
                            class="text-danger small mt-1">
                            Job description is required.
                        </div>
                        <div class="text-danger small mt-1"
                            *ngIf="remark.errors?.['pattern'] && (remark.dirty || remark.touched || djpForm.submitted)">
                            Only letters, numbers, spaces, and basic punctuation allowed. must include at least ten
                            letter.
                        </div>
                    </div>

                    <div class="col-6 filedsMarginSet">
                        <label class="form-label" for="fromDate">Start Date & Time <span
                                class="text-danger">*</span></label>
                        <input type="datetime-local" id="fromDate" class="form-control form-control-sm"
                            [ngModel]="selectedDjpData.fromDate" (ngModelChange)="onDateChange($event, 'fromDate')"
                            name="fromDate" #fromDate="ngModel" [min]="getCurrentDateTimeLocal()" required>
                        <div *ngIf="fromDate.invalid && (fromDate.dirty || fromDate?.touched)"
                            class="text-danger small mt-1">
                            Start date and time are required.
                        </div>
                        <div *ngIf="startDateTimeInPast" class="text-danger small mt-1">
                            Start date and time cannot be in the past.
                        </div>
                    </div>

                    <div class="col-6 filedsMarginSet">
                        <label class="form-label" for="toDate">End Date & Time <span
                                class="text-danger">*</span></label>
                        <input type="datetime-local" id="toDate" class="form-control form-control-sm"
                            [(ngModel)]="selectedDjpData.toDate" (ngModelChange)="onDateChange($event, 'toDate')"
                            name="toDate" #toDate="ngModel"
                            [min]="selectedDjpData.fromDate || getCurrentDateTimeLocal()" required>
                        <div *ngIf="toDate.invalid && (toDate.dirty || toDate?.touched)" class="text-danger small mt-1">
                            End date and time are required.
                        </div>
                        <div *ngIf="endDateTimeInPast" class="text-danger small mt-1">
                            End date and time cannot be in the past.
                        </div>
                        <div *ngIf="endDateBeforeStart" class="text-danger small mt-1">
                            End date and time must be after the start date and time.
                        </div>
                    </div>

                    <div class="col-12 mt-4 d-flex justify-content-end gap-2 border-top pt-3">
                        <button type="button" class="btn btn-sm btn-secondary"
                            (click)="resetDjpForm(djpForm)">Cancel</button>
                        <button type="submit" class="btn btn-sm adani-btn">
                            <span>
                                <i class="bi bi-check-circle me-1"></i> Submit
                            </span>
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <div *ngIf="selectedAddDjpTabIndex === 1">
            <form #uploadForm="ngForm" (ngSubmit)="uploadFiles(uploadForm)">
                <div class="col-12 uploadFile">
                    <div class="row g-3" *ngIf="isSuperAdminLogin">
                        <div class="col-6 filedsMarginSet">
                            <label class="form-label" for="plantId">Select Plant <span
                                    class="text-danger">*</span></label>
                            <select id="filterPlant" class="form-select" [(ngModel)]="superAdminPlant"
                                #plantselectSuperAdmin="ngModel" required name="plantId"
                                (ngModelChange)="onPlantSelectionChange($event)">
                                <option [ngValue]="null">All Plants</option>
                                <option *ngFor="let plant of availablePlants" [value]="plant.id">{{ plant.name }}
                                </option>
                            </select>
                            <div *ngIf="plantselectSuperAdmin.invalid && plantselectSuperAdmin.touched"
                                class="text-danger mt-1 small">
                                <div *ngIf="plantselectSuperAdmin.errors?.['required']">Plant is required.</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <label for="zoneId" class="form-label">Select Zone <span
                                    class="text-danger">*</span></label>
                            <select class="form-select" id="filterZoneId" [(ngModel)]="superAdminZone" name="zoneId"
                                #zoneSelectSuperAdmin="ngModel" required>
                                <option [ngValue]="null">
                                    {{ availableZones.length > 0 ? 'Select Zone' : 'No zones found' }}
                                </option>
                                <option *ngFor="let zone of availableZones" [value]="zone.id">{{ zone.zoneName }}
                                </option>
                            </select>
                            <div *ngIf="zoneSelectSuperAdmin.invalid && zoneSelectSuperAdmin.touched"
                                class="text-danger mt-1 small">
                                <div *ngIf="zoneSelectSuperAdmin.errors?.['required']">Zone is required.</div>
                            </div>
                        </div>
                    </div>
                    <div class="upload-box-container">
                        <label class="fw-bold mb-1">Upload excel for bulk upload</label>
                        <div class="upload-box" [class.dragging]="isDragging" (dragover)="onDragOver($event)"
                            (dragleave)="onDragLeave()" (drop)="onDrop($event)">
                            <div>
                                <img style="width: 5%" alt="" src="../../../assets/svg/upload-plain.svg" />
                            </div>
                            <p>
                                <b>
                                    Drag & Drop files here or
                                    <a (click)="fileInput.click()" class="browseStyle">Browse</a>
                                </b>
                            </p>
                            <p style="font-size: xx-small">Supported formats: Excel</p>
                            <input type="file" (change)="onFileSelected($event)" multiple hidden #fileInput />
                        </div>
                    </div>

                    <!-- Uploading File List -->
                    <div class="file-list">
                        <div *ngIf="invalidFile" class="invalidFileStyle">
                            <div class="error">
                                {{ invalidFile.name }}
                                <button (click)="removeInvalidFile()">
                                    <i class="bi bi-x" style="cursor: pointer; color: red; font-size: 18px"></i>
                                </button>
                            </div>
                            <p style="font-size: x-small">{{ invalidFile.error }}</p>
                        </div>

                        <div *ngIf="selectedFile" class="uploadedFileStyle">
                            <label class="fw-bold mt-3"></label>
                            <div class="valid">
                                {{ selectedFile.name }}
                                <button (click)="removeFile()">
                                    <i class="bi bi-x" style="cursor: pointer; color: #0f8669; font-size: 18px"></i>
                                </button>
                            </div>
                            <p style="font-size: x-small; color: green">
                                The uploaded file format is supported.
                            </p>
                        </div>
                    </div>
                    <div class="button-group mt-3 justify-content-center">

                        <button type="submit" class="btn btn-sm adani-btn me-2 customStyle" [disabled]="!selectedFile">
                            <i class="bi bi-upload me-1"></i>Upload details
                        </button>
                        <button class="btn btn-secondary customStyle" (click)="resetUploadState()">
                            Reset
                        </button>
                    </div>

                </div>
            </form>
        </div>
    </app-offcanvas>

    <app-offcanvas [title]="'Upload Confirmation'" [width]="'1100'" *ngIf="isUploadModelOpen"
        (onClickCross)="closeUploadModel()">
        <div class="filter-container p-3">
            <div class="card-body-validRecord">
                <div class="table-container">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-header">
                                <tr class="text-center">
                                    <th scope="col">
                                        <input type="checkbox" (change)="selectAll($event)" id="selectAll"
                                            [checked]="allSelected">
                                    </th>
                                    <th scope="col">Title</th>
                                    <th scope="col">Zone</th>
                                    <th scope="col">Email</th>
                                    <th scope="col">Job Description</th>
                                    <th scope="col">Criticality</th>
                                    <th scope="col">Start Date & Time</th>
                                    <th scope="col">End Date & Time</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr *ngIf="listLoading">
                                    <td colspan="7" class="text-center p-4">
                                        <div class="spinner-border spinner-border-sm text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <span class="ms-2">Loading Djp...</span>
                                    </td>
                                </tr>

                                <tr
                                    *ngIf="!listLoading && (!validRecordsBulkUpload  || validRecordsBulkUpload.length === 0)">
                                    <td colspan="8" class="text-center p-4 text-muted">
                                        <i class="bi bi-info-circle me-2"></i>No Djp found matching the criteria.
                                    </td>
                                </tr>

                                <tr *ngFor="let validRecords of validRecordsBulkUpload " class="text-center">
                                    <td>
                                        <input type="checkbox" [checked]="validRecords.selected"
                                            (change)="toggleSelection(validRecords)">
                                    </td>
                                    <td>{{validRecords.title}}</td>
                                    <td>{{validRecords?.zone}}</td>
                                    <td>{{validRecords?.email}}</td>
                                    <td class="fixed-cell" [matTooltip]="validRecords?.jobDescription">
                                        {{ validRecords?.jobDescription }}
                                    </td>
                                    <td>{{ getCriticalityLabel(validRecords.Criticality) }}</td>

                                    <td>{{validRecords?.fromDate | date: 'dd/MM/yyyy HH:mm'}}</td>
                                    <td>{{validRecords?.toDate | date: 'dd/MM/yyyy HH:mm'}}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="card-footer text-muted text-center" *ngIf="filteredDjpResponse .length > 0">
                <!-- uncomment for future use -->
                <!-- <app-pagination [currentPage]="currentPageValidRecords" [totalItems]="totalItemsValidRecords"
                    [itemsPerPage]="itemsPerPageValidRecords"
                    (pageChange)="onPageChangeValidRecords($event)"></app-pagination> -->
            </div>
            <div class="mt-3 text-start">
                <button type="submit" class="btn btn-sm adani-btn" style="width: 200px; margin-right: 8px;"
                    (click)="onOpenUploadConfirmation()" [disabled]="!isAnyRecordSelected()">
                    <span>
                        <i class="bi bi-check-circle me-1"></i> Submit
                    </span>
                </button>
                <button class="btn btn-secondary" style="width: 200px"
                    (click)="cancelUploadConfirmation()">Cancel</button>
            </div>
        </div>
    </app-offcanvas>
</div>

<div *ngIf="selectedTabIndex == 1">
    <div class="card custom-card" id="djp-history">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col-12 col-md">
                    <h6 class="mb-2 mb-md-0">DJP History</h6>
                </div>
                <div class="col-12 col-md-auto">
                    <div
                        class="d-flex flex-column flex-md-row align-items-stretch align-items-md-center justify-content-end">
                        <input type="text" class="form-control form-control-sm mb-2 mb-md-0 ms-0 ms-md-1"
                            style="max-width: 100%; width: 200px; margin-right: 8px;" placeholder="Search here"
                            [(ngModel)]="searchTextHistory" (keyup)="searchTableDataHistory()" />

                        <div ngbDropdown class="d-inline-block mb-2 mb-md-0 me-0 me-md-2">
                            <button type="button" class="btn btn-sm adani-btn dropdown-toggle w-100"
                                id="downloadNotifExcelDropdown" ngbDropdownToggle
                                [disabled]="isDownloadingExcel || listLoading">
                                <span *ngIf="!isDownloadingExcel">
                                    <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
                                </span>
                                <span *ngIf="isDownloadingExcel">
                                    <span class="spinner-border spinner-border-sm me-1" role="status"
                                        aria-hidden="true"></span>
                                    Downloading
                                    {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '') }}...
                                </span>
                            </button>
                            <ul ngbDropdownMenu aria-labelledby="downloadNotifExcelDropdown">
                                <li>
                                    <button ngbDropdownItem (click)="downloadExcel('current', 'history')"
                                        [disabled]="isDownloadingExcel || listLoading || (historyDjpResponse?.length ?? 0) === 0">
                                        <i class="bi bi-download me-1"></i> Download Current Page ({{
                                        historyDjpResponse?.length ?? 0 }})
                                    </button>
                                </li>
                                <li>
                                    <button ngbDropdownItem (click)="downloadExcel('all', 'history')"
                                        [disabled]="isDownloadingExcel || listLoading || totalItemsHistory === 0">
                                        <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{
                                        totalItemsHistory }})
                                    </button>
                                </li>
                            </ul>
                        </div>

                        <img src="../../../assets/svg/filter.svg" class="filter-button ms-0 ms-md-1" alt="Filter"
                            style="width: 35px; cursor: pointer;" title="Filter DJP" (click)="openFilterModal()" />
                    </div>
                </div>
            </div>
        </div>

        <div class="card-body">
            <div class="table-container">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-header">
                            <tr class="text-center">
                                <th scope="col">Title</th>
                                <th scope="col">Zone</th>
                                <th scope="col">Job Description</th>
                                <th scope="col">Criticality</th>
                                <th scope="col">Start Date & Time</th>
                                <th scope="col">End Date & Time</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngIf="listLoading">
                                <td colspan="5" class="text-center p-4">
                                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <span class="ms-2">Loading Djp...</span>
                                </td>
                            </tr>

                            <tr *ngIf="!listLoading && (!historyDjpResponse  || historyDjpResponse .length === 0)">
                                <td colspan="8" class="text-center p-4 text-muted">
                                    <i class="bi bi-info-circle me-2"></i>No Djp found matching the criteria.
                                </td>
                            </tr>

                            <tr *ngFor="let djpHistory of historyDjpResponse " class="text-center">

                                <td>{{djpHistory.djpTitle}}</td>
                                <td>{{djpHistory?.zone?.zoneName}}</td>
                                <td class="fixed-cell" [matTooltip]="djpHistory?.jobDescription">
                                    {{ djpHistory?.jobDescription }}
                                </td>
                                <td>{{ djpHistory.risk }}</td>

                                <td>{{djpHistory?.fromDate}}</td>
                                <td>{{djpHistory?.toDate}}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="card-footer text-muted text-center" *ngIf="historyDjpResponse.length > 0">
            <app-pagination [currentPage]="currentPageHistory" [totalItems]="totalItemsHistory"
                [itemsPerPage]="itemsPerPageHistory" (pageChange)="onPageChangeHistory($event)"></app-pagination>
        </div>
    </div>
</div>

<app-offcanvas [title]="'Filter DJP'" *ngIf="isFilterModalOpen" (onClickCross)="closeModal()">
    <div class="filter-container p-3">
        <form #filterForm="ngForm" (ngSubmit)="applyFilters()"> <!-- Use applyFilters -->
            <div class="row g-3">

                <div class="col-12">
                    <label class="form-label" for="filterTitle">Title</label>
                    <input type="text" id="filterTitle" class="form-control" placeholder="Search by Title"
                        [(ngModel)]="filters.djpTitle" name="title" maxlength="30" #filterTitle="ngModel"
                        [ngClass]="{'is-invalid': filterTitle.invalid && (filterTitle.dirty || filterTitle.touched)}">
                    <div *ngIf="filterTitle.invalid && (filterTitle.dirty || filterTitle.touched)"
                        class="invalid-feedback">
                        <div *ngIf="filterTitle.errors?.['maxlength']">Title cannot exceed 30 characters.</div>
                    </div>
                    <!-- Character count display - only visible when there's text -->
                    <small *ngIf="filters.djpTitle" class="text-muted d-block text-end mt-1">
                        {{ filters.djpTitle.length }}/30 characters
                    </small>
                </div>

                <div class="col-12">
                    <label class="form-label" for="filterStartDate">Date Range</label>
                    <div class="input-group">
                        <input type="date" id="filterStartDate" class="form-control" aria-label="Start Date"
                            [(ngModel)]="filters.startDate" name="startDate" #startDate="ngModel"
                            [ngClass]="{'is-invalid': startDate.invalid && (startDate.dirty || startDate.touched)}" />
                        <span class="input-group-text">to</span>
                        <input type="date" id="filterEndDate" class="form-control" aria-label="End Date"
                            [(ngModel)]="filters.endDate" name="endDate" #endDate="ngModel"
                            [ngClass]="{'is-invalid': endDate.invalid && (endDate.dirty || endDate.touched)}" />
                    </div>
                    <div *ngIf="endDate.value && startDate.value && endDate.value < startDate.value"
                        class="text-danger small mt-1">
                        End date cannot be earlier than start date.
                    </div>
                    <small class="text-muted">Filters incidents created within this date range.</small>
                </div>

                <div class="col-12">

                    <label class="form-label mt-2" for="filterSortDirNotif">Sort Direction</label>
                    <select id="filterSortDirNotif" class="form-select" [(ngModel)]="filters.sortDirection"
                        name="sortDirection"> <!-- Bind to filters.sortDirection -->
                        <option value="DESC">Descending</option>
                        <option value="ASC">Ascending</option>
                    </select>
                </div>

                <div class="col-12 mt-4 d-grid gap-2">
                    <button type="submit" class="btn adani-btn" [disabled]="listLoading">
                        <i class="bi bi-search me-1"></i> Apply Filters
                    </button>
                    <button type="button" class="btn btn-secondary" (click)="resetFilters()" [disabled]="listLoading">
                        <i class="bi bi-arrow-clockwise me-1"></i> Reset Filters
                    </button>
                </div>

            </div>
        </form>
    </div>
</app-offcanvas>

<div class="modal fade" #confirmAddNewDjp id="addDjpModel" tabindex="-1" aria-labelledby="addDjpModelLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white"> <!-- Header styled for rejection -->
                <h5 class="modal-title" id="addDjpModelLabel">
                    <i class="bi me-2"></i> Confirmation
                </h5>
                <button type="button" class="btn-close btn-close-white" aria-label="Close"
                    (click)="closeCreateSingleDjp()"></button>
                <!-- Use (click) binding -->
            </div>
            <div class="modal-body">
                <p>Are you sure you want to create a new DJP?
                </p>
                <!-- Display incident ID for clarity -->
            </div>
            <div class="modal-footer">
                <!-- Using standard Bootstrap buttons for confirmation -->
                <button type="button" class="btn" (click)="closeCreateSingleDjp()">
                    <i class="bi me-1"></i> No
                </button>
                <button type="button" class="btn bg-success" (click)="confirmationCreateDjp()">
                    <i class="bi bi-check-lg"></i> Yes
                </button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" #uploadDjp id="uploadDjpConfirmationModal" tabindex="-1"
    aria-labelledby="uploadConfirmationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="uploadConfirmationModalLabel">
                    <i class="bi me-2"></i> Confirmation
                </h5>
                <button type="button" class="btn-close btn-close-white" aria-label="Close"
                    (click)="closeUploadDjp()"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to submit the records?
                </p>
            </div>
            <div class="modal-footer">

                <button type="button" class="btn" (click)="closeUploadDjp()">
                    <i class="bi me-1"></i> No
                </button>
                <button type="button" class="btn adani-btn" (click)="confirmUploadDjp()">
                    <i class="bi bi-check-lg"></i> Yes
                </button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" #uploadCancelDjp id="cancelUploadDjpConfirmationModal" tabindex="-1"
    aria-labelledby="CanceluploadModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="CanceluploadModalLabel">
                    <i class="bi me-2"></i> Confirmation
                </h5>
                <button type="button" class="btn-close btn-close-white" aria-label="Close"
                    (click)="closeCancelConfimation()"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to revoke the selected records?</p>
            </div>
            <div class="modal-footer">

                <button type="button" class="btn" (click)="closeCancelConfimation()">
                    <i class="bi me-1"></i> No
                </button>
                <button type="button" class="btn adani-btn" (click)="CancelUploadConfirmation()">
                    <i class="bi bi-check-lg"></i> Yes
                </button>
            </div>
        </div>
    </div>
</div>