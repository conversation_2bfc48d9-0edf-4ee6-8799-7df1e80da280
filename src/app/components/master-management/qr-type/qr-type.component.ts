import { Component, OnInit, ViewChild } from '@angular/core'; // Added ViewChild
import { FormsModule, NgForm } from '@angular/forms'; // Added NgForm
import { QrTypeService } from '../../../services/master-management/qr-type/qr-type.service';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { CommonModule } from '@angular/common';
import { SwitchComponent } from "../../../shared/switch/switch.component";
import { PaginationComponent } from "../../../shared/pagination/pagination.component";
import { OffcanvasComponent } from "../../../shared/offcanvas/offcanvas.component";
import { UpdateService } from '../../../services/update/update.service';
// Optional: Import ToastrService or similar
// import { ToastrService } from 'ngx-toastr';
import { ToastMessageComponent } from '../../../shared/toast-message/toast-message.component'; // Adjust path if needed
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap'; // For dropdown button
import * as XLSX from 'xlsx'; // For Excel generation


// Interface for filter structure
interface QrTypeFilter {
    name?: string | null;
    enabled?: string | null;
    sortField?: string | null;
    sortDirection?: 'ASC' | 'DESC';
}

// *** NEW: Interface for QR Type Data ***
export interface QrType {
    id: number;
    title: string;
    enabled: boolean;
    createdAt?: string; // Optional
    updatedAt?: string; // Optional
}

// *** NEW: Interface for Create Form Data ***
interface NewQrTypeData {
    title: string;
    enabled: boolean;
}


@Component({
    selector: 'app-qr-type',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        SwitchComponent,
        PaginationComponent,
        OffcanvasComponent,
        NgbDropdownModule,
        ToastMessageComponent
    ],
    templateUrl: './qr-type.component.html',
    styleUrl: './qr-type.component.scss'
})
export class QrTypeComponent implements OnInit {
    // Access the form template variable
    @ViewChild('createForm') createForm?: NgForm;
    @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;


    // --- Modal States ---
    isFilterModalOpen = false; // Renamed from isEditUserModalOpen
    isEditModalOpen = false;   // <-- New state for edit modal
    isCreateModalOpen = false; // <-- New state for create modal

    // --- Data & Loading States ---
    qrTypeList: QrType[] = []; // Use QrType interface
    listLoading = false;
    editLoading = false;     // <-- New state for edit form submission
    createLoading = false;   // <-- New state for create form submission

    // --- Pagination ---
    currentPage = 1;
    itemsPerPage = 10;
    totalItems = 0;
    isDownloadingExcel = false;
    downloadType: 'current' | 'all' | null = null;

    // --- Filtering ---
    filters: QrTypeFilter = {
        name: null,
        enabled: 'true',
        sortField: 'title',
        sortDirection: 'ASC'
    };
    availableSortFields = [
        { value: 'id', label: 'ID' },
        { value: 'title', label: 'QR Type Name' },
        { value: 'enabled', label: 'Enabled Status' },
        { value: 'createdAt', label: 'Created Date' }
    ];

    // --- Editing ---
    selectedQrType: QrType | null = null; // <-- Object to hold data for editing

    // --- Creating ---
    newQrTypeData: NewQrTypeData = { // <-- Object to hold new QR type form data
        title: '',
        enabled: true // Default new QR types to active
    };

    constructor(
        readonly qrTypeService: QrTypeService,
        readonly updateService: UpdateService,
        // Optional: Inject ToastrService
        // private toastr: ToastrService
    ) { }

    ngOnInit(): void {
        this.loadQrTypes(this.currentPage);
    }

    // Helper to get current list data
    getCurrentListData(): QrType[] | undefined {
        return this.qrTypeList;
    }

    // Fetch ALL QR types matching current filters (no pagination)
    async fetchAllFilteredQrTypes(): Promise<QrType[] | null> {
        this.listLoading = true; // Indicate loading
        const filterParams: string[] = [];
        if (this.filters.name) { filterParams.push(`title||$contL||${this.filters.name}`); }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
             filterParams.push(`enabled||$eq||${this.filters.enabled}`);
        }

        const data = {
            limit: 10000, // Large limit for "all"
            sort: `${this.filters.sortField || 'title'},${this.filters.sortDirection || 'ASC'}`, // Match default sort
            filter: filterParams
        };

        try {
            const params = createAxiosConfig(data);
            console.log('API Request Params (QR Types Download - All Data):', JSON.stringify(params, null, 2));
            const response = await this.qrTypeService.getQrType(params); // Use existing service method
            return response ?? [];
        } catch (error: any) {
            console.error("Error fetching all QR types for download:", error);
            this.toast?.showErrorToast(error?.response?.data?.message || "Failed to retrieve full data for download.");
            return null;
        } finally {
             this.listLoading = false; // Reset loading indicator
        }
    }

    // Main download function
    async downloadExcel(type: 'current' | 'all') {
        if (this.isDownloadingExcel || this.listLoading) return; // Prevent concurrent actions

        this.isDownloadingExcel = true;
        this.downloadType = type;
        this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} QR types...`);

        let dataToExport: QrType[] | null = null;

        try {
            // 1. Get Data
            if (type === 'all') {
                dataToExport = await this.fetchAllFilteredQrTypes();
            } else { // 'current'
                dataToExport = this.getCurrentListData() ?? null;
                if (dataToExport === undefined) { dataToExport = null; }
            }

            // 2. Check data
            if (dataToExport === null) { this.toast?.showErrorToast("Failed to retrieve data for download."); return; }
            if (!dataToExport || dataToExport.length === 0) { this.toast?.showErrorToast(`No QR types available to download.`); return; }

            console.log(`Fetched ${dataToExport.length} QR types for Excel export (${type}).`);

            // 3. Transform data
            const dataForExcel = dataToExport.map(qr => ({
                'QR Type ID': qr.id,
                'QR Type Name': qr.title,
                'Status': qr.enabled ? 'Active' : 'Inactive',
                'Created At': qr.createdAt ? new Date(qr.createdAt).toLocaleString() : 'N/A',
                'Updated At': qr.updatedAt ? new Date(qr.updatedAt).toLocaleString() : 'N/A',
            }));

            // 4. Create Worksheet and Workbook
            const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
            const wb: XLSX.WorkBook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'QRTypes'); // Sheet name

            // 5. Generate File Name
            const dateStr = new Date().toISOString().slice(0, 10);
            const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
            const fileName = `QRTypes_${typeStr}_${dateStr}.xlsx`;

            // 6. Trigger Download
            XLSX.writeFile(wb, fileName);
            this.toast?.showSuccessToast(`Excel file download started (${type}).`);

        } catch (error) {
            console.error(`Error generating Excel file (${type}):`, error);
            this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
        } finally {
            this.isDownloadingExcel = false;
            this.downloadType = null;
        }
    }

    // --- Filter Modal Methods ---
    openFilterModal(): void { this.isFilterModalOpen = true; }
    closeFilterModal(): void { this.isFilterModalOpen = false; } // Renamed from closeModal

    @ViewChild('filterForm') filterForm!: NgForm;

    applyFilters(): void {
        // Check if form is valid before applying filters
        if (this.filterForm && this.filterForm.invalid) {
            this.toast?.showErrorToast("Please correct the validation errors before applying filters.");
            return;
        }

        // Trim string values to prevent whitespace-only searches
        if (this.filters.name) {
            this.filters.name = this.filters.name.trim();
        }

        this.currentPage = 1;
        this.loadQrTypes(this.currentPage);
        this.closeFilterModal();
    }

    resetFilters(): void {
        this.filters = {
            name: null,
            enabled: 'true',
            sortField: 'title',
            sortDirection: 'ASC'
        };
        this.currentPage = 1;
        this.loadQrTypes(this.currentPage);
        // Optionally close modal: this.closeFilterModal();
    }

    // --- *** NEW: Edit Modal Methods *** ---
    /** Opens the edit QR type offcanvas modal. */
    openEditModal(qrType: QrType): void {
        this.selectedQrType = { ...qrType }; // Create a copy
        this.isEditModalOpen = true;
        this.editLoading = false;
    }

    /** Closes the edit QR type offcanvas modal. */
    closeEditModal(): void {
        this.isEditModalOpen = false;
        this.selectedQrType = null;
    }

    @ViewChild('editForm') editForm!: NgForm;

    /** Handles the submission of the edit QR type form. */
    async submitEditForm(): Promise<void> {
        if (!this.selectedQrType || this.selectedQrType.id == null) {
            console.error("Cannot save, selected QR type is null or has no ID.");
            this.toast?.showErrorToast('Cannot save, no QR type selected.');
            return;
        }

        // Check form validity
        if (this.editForm && this.editForm.invalid) {
            this.toast?.showErrorToast("Please correct the validation errors before saving.");
            // Mark fields as touched to show validation errors
            Object.values(this.editForm.controls).forEach(control => {
                control.markAsTouched();
            });
            return;
        }

        // Trim the title to remove any leading/trailing whitespace
        if (this.selectedQrType.title) {
            this.selectedQrType.title = this.selectedQrType.title.trim();
        }

        this.editLoading = true;
        const updatePayload = {
            tableName: 'qr-type', // Critical: Ensure this matches your backend/service expectation
            id: this.selectedQrType.id,
            data: {
                title: this.selectedQrType.title,
                enabled: this.selectedQrType.enabled
                // Add other editable fields if necessary
            }
        };

        try {
            await this.update(updatePayload); // Use the generic update method
            this.toast?.showSuccessToast('QR type updated successfully!');

            // Update list locally (optional)
            const index = this.qrTypeList.findIndex(qr => qr.id === this.selectedQrType?.id);
            if (index !== -1 && this.selectedQrType) {
                 this.qrTypeList[index] = { ...this.selectedQrType };
            }

            this.closeEditModal();
            // Consider reloading if necessary: this.loadQrTypes(this.currentPage);

        } catch (error) {
            console.error("Error submitting QR type update:", error);
            this.toast?.showErrorToast('Failed to update QR type.');
        } finally {
            this.editLoading = false;
        }
    }


    // --- *** NEW: Create Modal Methods *** ---

    /** Opens the create QR type offcanvas modal and resets the form data. */
    openCreateModal(): void {
        this.newQrTypeData = {
            title: '',
            enabled: true
        };
        this.createForm?.resetForm({ enabled: true }); // Reset form state with default
        this.isCreateModalOpen = true;
        this.createLoading = false;
    }

    /** Closes the create QR type offcanvas modal. */
    closeCreateModal(): void {
        this.isCreateModalOpen = false;
    }

    /** Handles the submission of the create QR type form. */
    async submitCreateForm(): Promise<void> {
        if (this.createForm?.invalid) {
             Object.values(this.createForm.controls).forEach(control => control.markAsTouched());
             this.toast?.showErrorToast("Please correct the validation errors before creating.");
             console.warn('Create form submitted while invalid.');
             return;
        }

        // Trim the title to remove any leading/trailing whitespace
        if (this.newQrTypeData.title) {
            this.newQrTypeData.title = this.newQrTypeData.title.trim();
        }

        this.createLoading = true;
        console.log('Submitting new QR type:', this.newQrTypeData);

        try {
            // *** ASSUMPTION: qrTypeService has a createQrType method ***
            const createdQrType = await this.qrTypeService.createQrType(this.newQrTypeData);
            console.log('QR type created successfully:', createdQrType);

            this.toast?.showSuccessToast(`QR type "${createdQrType.title}" created successfully!`);

            this.closeCreateModal();
            // Reload list, go to page 1
            this.currentPage = 1;
            this.loadQrTypes(this.currentPage);

        } catch (error) {
            console.error("Error creating QR type:", error);
            this.toast?.showErrorToast('Failed to create QR type. Please try again.');
        } finally {
            this.createLoading = false;
        }
    }


    // --- Data Loading & Update Methods ---

    /** Fetches QR types from the backend. */
    async loadQrTypes(page: number): Promise<void> {
        this.listLoading = true;
        // this.qrTypeList = []; // Clear only after loading starts

        const filterParams: string[] = [];
        if (this.filters.name) {
            filterParams.push(`title||$contL||${this.filters.name}`);
        }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
            filterParams.push(`enabled||$eq||${this.filters.enabled}`);
        }

        const requestData = {
            page: page,
            limit: this.itemsPerPage,
            sort: `${this.filters.sortField || 'title'},${this.filters.sortDirection || 'ASC'}`,
            filter: filterParams
        };

        try {
            const params = createAxiosConfig(requestData);
            // Ensure service method returns { data: QrType[], total: number }
            const response = await this.qrTypeService.getQrType(params);
            this.qrTypeList = response?.data ?? [];
            this.totalItems = response?.total ?? 0;
        } catch (error) {
            console.error("Error fetching QR types:", error);
            this.qrTypeList = [];
            this.totalItems = 0;
            // this.toastr.error('Failed to load QR types.', 'Error');
        } finally {
            this.listLoading = false;
        }
    }

    /** Handles page changes. */
    onPageChange(page: number): void {
        if (page !== this.currentPage) {
            this.currentPage = page;
            this.loadQrTypes(this.currentPage);
        }
    }

    /** Handles the toggle switch change event in the table row. */
    async onSwitchToggle(isEnabled: boolean, qrType: QrType): Promise<void> {
        const originalState = qrType.enabled;
        qrType.enabled = isEnabled; // Optimistic update

        const updatePayload = {
            tableName: 'qr-type', // Use correct table name
            id: qrType.id,
            data: {
                enabled: isEnabled
            }
        };
        console.log(`Attempting to update QR type ${qrType.id} enabled status to: ${isEnabled}`);

        try {
            await this.update(updatePayload);
            console.log(`Successfully updated QR type ${qrType.id} enabled status.`);
            // this.toastr.success('QR type status updated.', 'Success');
        } catch (error) {
            console.error(`Error updating enabled status for QR type ${qrType.id}:`, error);
            // this.toastr.error('Failed to update QR type status.', 'Update Error');
            // Revert UI change on error
            qrType.enabled = originalState;
            // Force update detection if needed
            const index = this.qrTypeList.findIndex(qr => qr.id === qrType.id);
            if (index !== -1) {
                 this.qrTypeList[index] = {...this.qrTypeList[index], enabled: originalState };
            }
        }
    }

    // Generic update via UpdateService (used by toggle and edit form)
    async update(data: { tableName: string, id: number, data: any }): Promise<void> {
        try {
            await this.updateService.update(data);
        } catch (error) {
            console.error("Update service call failed:", error);
            throw error; // Re-throw
        }
    }

    // --- Removed Placeholder/Unused Methods ---
    // Removed handleCreate, handleUpdate, toggleEnabled, handleDelete, sortBy, getSortClass
}