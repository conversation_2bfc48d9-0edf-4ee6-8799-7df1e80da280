import { Component, OnInit, ViewChild } from '@angular/core'; // Added ViewChild
import { FormsModule, NgForm } from '@angular/forms'; // Added NgForm
import { IncidentMasterService } from '../../../services/master-management/incident-master/incident-master.service';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { CommonModule } from '@angular/common';
import { SwitchComponent } from "../../../shared/switch/switch.component";
import { PaginationComponent } from "../../../shared/pagination/pagination.component";
import { OffcanvasComponent } from "../../../shared/offcanvas/offcanvas.component";
import { UpdateService } from '../../../services/update/update.service';
// Optional: Import ToastrService or similar
// import { ToastrService } from 'ngx-toastr';
import { ToastMessageComponent } from '../../../shared/toast-message/toast-message.component'; // Adjust path if needed
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap'; // For dropdown button
import * as XLSX from 'xlsx'; // For Excel generation


// Interface for filter structure
interface IncidentMasterFilter {
    name?: string | null;
    enabled?: string | null;
    sortField?: string | null;
    sortDirection?: 'ASC' | 'DESC';
}

// *** NEW: Interface for Incident Master Data ***
export interface IncidentMaster {
    id: number;
    title: string;
    enabled: boolean;
    createdAt?: string; // Optional
    updatedAt?: string; // Optional
}

// *** NEW: Interface for Create Form Data ***
interface NewIncidentMasterData {
    title: string;
    enabled: boolean;
}


@Component({
    selector: 'app-incident-master',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        SwitchComponent,
        PaginationComponent,
        OffcanvasComponent,
        NgbDropdownModule,
        ToastMessageComponent
    ],
    templateUrl: './incident-master.component.html',
    styleUrl: './incident-master.component.scss'
})
export class IncidentMasterComponent implements OnInit {
    // Access the form template variables
    @ViewChild('createForm') createForm?: NgForm;
    @ViewChild('filterForm') filterForm?: NgForm;
    @ViewChild('editForm') editForm?: NgForm;
    @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;

    // --- Modal States ---
    isFilterModalOpen = false; // Renamed from isEditUserModalOpen
    isEditModalOpen = false;   // <-- New state for edit modal
    isCreateModalOpen = false; // <-- New state for create modal

    // --- Data & Loading States ---
    incidentList: IncidentMaster[] = []; // Use IncidentMaster interface
    listLoading = false;
    editLoading = false;     // <-- New state for edit form submission
    createLoading = false;   // <-- New state for create form submission
    isDownloadingExcel = false;
    downloadType: 'current' | 'all' | null = null;

    // --- Pagination ---
    currentPage = 1;
    itemsPerPage = 10;
    totalItems = 0;

    // --- Filtering ---
    filters: IncidentMasterFilter = {
        name: null,
        enabled: 'true',
        sortField: 'title',
        sortDirection: 'ASC'
    };
    availableSortFields = [
        { value: 'id', label: 'ID' },
        { value: 'title', label: 'Incident Name' },
        { value: 'enabled', label: 'Enabled Status' },
        { value: 'createdAt', label: 'Created Date' }
    ];

    // --- Editing ---
    selectedIncident: IncidentMaster | null = null; // <-- Object to hold data for editing

    // --- Creating ---
    newIncidentData: NewIncidentMasterData = { // <-- Object to hold new incident form data
        title: '',
        enabled: true // Default new incidents to active
    };

    constructor(
        readonly incidentMasterService: IncidentMasterService,
        readonly updateService: UpdateService, // Changed access modifier to readonly
        // Optional: Inject ToastrService
        // private toastr: ToastrService
    ) { }

    ngOnInit() {
        this.loadIncidents(this.currentPage);
    }

    // Helper to get current list data
    getCurrentListData(): IncidentMaster[] | undefined {
        return this.incidentList;
    }

    // Fetch ALL incidents matching current filters (no pagination)
    async fetchAllFilteredIncidents(): Promise<IncidentMaster[] | null> {
        this.listLoading = true; // Indicate loading
        const filterParams: string[] = [];
        if (this.filters.name) { filterParams.push(`title||$contL||${this.filters.name}`); }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
             filterParams.push(`enabled||$eq||${this.filters.enabled}`);
        }

        const data = {
            limit: 10000, // Large limit for "all"
            sort: `${this.filters.sortField || 'title'},${this.filters.sortDirection || 'ASC'}`, // Match default sort
            filter: filterParams
        };

        try {
            const params = createAxiosConfig(data);
            console.log('API Request Params (Incident Types Download - All Data):', JSON.stringify(params, null, 2));
            const response = await this.incidentMasterService.getIncidentMaster(params); // Use existing service method
            return response ?? [];
        } catch (error: any) {
            console.error("Error fetching all incident types for download:", error);
            this.toast?.showErrorToast(error?.response?.data?.message || "Failed to retrieve full data for download.");
            return null;
        } finally {
             this.listLoading = false; // Reset loading indicator
        }
    }

    // Main download function
    async downloadExcel(type: 'current' | 'all') {
        if (this.isDownloadingExcel || this.listLoading) return; // Prevent concurrent actions

        this.isDownloadingExcel = true;
        this.downloadType = type;
        this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} incident types...`);

        let dataToExport: IncidentMaster[] | null = null;

        try {
            // 1. Get Data
            if (type === 'all') {
                dataToExport = await this.fetchAllFilteredIncidents();
            } else { // 'current'
                dataToExport = this.getCurrentListData() ?? null;
                if (dataToExport === undefined) { dataToExport = null; }
            }

            // 2. Check data
            if (dataToExport === null) { this.toast?.showErrorToast("Failed to retrieve data for download."); return; }
            if (!dataToExport || dataToExport.length === 0) { this.toast?.showErrorToast(`No incident types available to download.`); return; }

            console.log(`Fetched ${dataToExport.length} incident types for Excel export (${type}).`);

            // 3. Transform data
            const dataForExcel = dataToExport.map(inc => ({
                'Incident Type ID': inc.id,
                'Incident Name': inc.title,
                'Status': inc.enabled ? 'Active' : 'Inactive',
                'Created At': inc.createdAt ? new Date(inc.createdAt).toLocaleString() : 'N/A',
                'Updated At': inc.updatedAt ? new Date(inc.updatedAt).toLocaleString() : 'N/A',
            }));

            // 4. Create Worksheet and Workbook
            const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
            const wb: XLSX.WorkBook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'IncidentTypes'); // Sheet name

            // 5. Generate File Name
            const dateStr = new Date().toISOString().slice(0, 10);
            const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
            const fileName = `IncidentTypes_${typeStr}_${dateStr}.xlsx`;

            // 6. Trigger Download
            XLSX.writeFile(wb, fileName);
            this.toast?.showSuccessToast(`Excel file download started (${type}).`);

        } catch (error) {
            console.error(`Error generating Excel file (${type}):`, error);
            this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
        } finally {
            this.isDownloadingExcel = false;
            this.downloadType = null;
        }
    }

    // --- Filter Modal Methods ---
    openFilterModal(): void { this.isFilterModalOpen = true; }
    closeFilterModal(): void { this.isFilterModalOpen = false; }
    applyFilters(): void {
        // Check if filter form is valid
        if (this.filterForm?.invalid) {
            Object.values(this.filterForm.controls).forEach(control => control.markAsTouched());
            console.warn('Filter form submitted with invalid input.');
            this.toast?.showErrorToast('Please correct the validation errors before applying filters.');
            return;
        }

        this.currentPage = 1;
        this.loadIncidents(this.currentPage);
        this.closeFilterModal();
    }
    resetFilters(): void {
        this.filters = {
            name: null,
            enabled: 'true',
            sortField: 'title',
            sortDirection: 'ASC'
        };
        this.currentPage = 1;
        this.loadIncidents(this.currentPage);
        // Optionally close modal: this.closeFilterModal();
    }

    // --- *** NEW: Edit Modal Methods *** ---
    /** Opens the edit incident offcanvas modal. */
    openEditModal(incident: IncidentMaster): void {
        this.selectedIncident = { ...incident }; // Create a copy
        this.isEditModalOpen = true;
        this.editLoading = false;
    }

    /** Closes the edit incident offcanvas modal. */
    closeEditModal(): void {
        this.isEditModalOpen = false;
        this.selectedIncident = null;
    }

    /** Handles the submission of the edit incident form. */
    async submitEditForm(): Promise<void> {
        if (!this.selectedIncident || this.selectedIncident.id == null) {
            console.error("Cannot save, selected incident is null or has no ID.");
            this.toast?.showErrorToast('Cannot save, no incident selected.');
            return;
        }

        // Validate form
        if (this.editForm?.invalid) {
            Object.values(this.editForm.controls).forEach(control => control.markAsTouched());
            console.warn('Edit form submitted while invalid.');
            this.toast?.showErrorToast('Please correct the validation errors before submitting.');
            return;
        }

        // Additional validation
        if (this.selectedIncident.title.trim().length === 0) {
            this.toast?.showErrorToast('Incident name cannot be empty.');
            return;
        }

        this.editLoading = true;
        const updatePayload = {
            tableName: 'incident-master', // Critical: Ensure this matches your backend/service expectation
            id: this.selectedIncident.id,
            data: {
                title: this.selectedIncident.title,
                enabled: this.selectedIncident.enabled
                // Add other editable fields if necessary
            }
        };

        try {
            await this.update(updatePayload); // Use the generic update method
            // this.toastr.success('Incident type updated successfully!', 'Saved');

            // Update list locally (optional)
            const index = this.incidentList.findIndex(i => i.id === this.selectedIncident?.id);
            if (index !== -1 && this.selectedIncident) {
                 this.incidentList[index] = { ...this.selectedIncident };
            }

            this.closeEditModal();
            // Consider reloading if necessary: this.loadIncidents(this.currentPage);

        } catch (error) {
            console.error("Error submitting incident update:", error);
            // this.toastr.error('Failed to update incident type.', 'Error');
        } finally {
            this.editLoading = false;
        }
    }


    // --- *** NEW: Create Modal Methods *** ---

    /** Opens the create incident offcanvas modal and resets the form data. */
    openCreateModal(): void {
        this.newIncidentData = {
            title: '',
            enabled: true
        };
        this.createForm?.resetForm({ enabled: true }); // Reset form state with default
        this.isCreateModalOpen = true;
        this.createLoading = false;
    }

    /** Closes the create incident offcanvas modal. */
    closeCreateModal(): void {
        this.isCreateModalOpen = false;
    }

    /** Handles the submission of the create incident form. */
    async submitCreateForm(): Promise<void> {
        if (this.createForm?.invalid) {
             Object.values(this.createForm.controls).forEach(control => control.markAsTouched());
             console.warn('Create form submitted while invalid.');
             this.toast?.showErrorToast('Please correct the validation errors before submitting.');
             return;
        }

        // Additional validation
        if (this.newIncidentData.title.trim().length === 0) {
            this.toast?.showErrorToast('Incident name cannot be empty.');
            return;
        }

        this.createLoading = true;
        console.log('Submitting new incident type:', this.newIncidentData);

        try {
            // *** ASSUMPTION: incidentMasterService has a createIncidentMaster method ***
            const createdIncident = await this.incidentMasterService.createIncidentMaster(this.newIncidentData);
            console.log('Incident type created successfully:', createdIncident);

            // this.toastr.success(`Incident type "${createdIncident.title}" created successfully!`, 'Created');

            this.closeCreateModal();
            // Reload list, go to page 1
            this.currentPage = 1;
            this.loadIncidents(this.currentPage);

        } catch (error) {
            console.error("Error creating incident type:", error);
            // this.toastr.error('Failed to create incident type. Please try again.', 'Creation Error');
        } finally {
            this.createLoading = false;
        }
    }


    // --- Data Loading & Update Methods ---

    /** Fetches incident master data from the backend. */
    async loadIncidents(page: number): Promise<void> {
        this.listLoading = true;
        // this.incidentList = []; // Clear only after loading starts

        const filterParams: string[] = [];
        if (this.filters.name) {
            filterParams.push(`title||$contL||${this.filters.name}`);
        }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
            filterParams.push(`enabled||$eq||${this.filters.enabled}`);
        }

        const requestData = {
            page: page,
            limit: this.itemsPerPage,
            sort: `${this.filters.sortField || 'title'},${this.filters.sortDirection || 'ASC'}`,
            filter: filterParams
        };

        try {
            const params = createAxiosConfig(requestData);
            // Ensure service method returns { data: IncidentMaster[], total: number }
            const response = await this.incidentMasterService.getIncidentMaster(params);
            this.incidentList = response?.data ?? [];
            this.totalItems = response?.total ?? 0;
        } catch (error) {
            console.error("Error fetching incident master data:", error);
            this.incidentList = [];
            this.totalItems = 0;
            // this.toastr.error('Failed to load incident types.', 'Error');
        } finally {
            this.listLoading = false;
        }
    }

    /** Handles page changes. */
    onPageChange(page: number): void {
        if (page !== this.currentPage) {
            this.currentPage = page;
            this.loadIncidents(this.currentPage);
        }
    }

    /** Handles the toggle switch change event in the table row. */
    async onSwitchToggle(isEnabled: boolean, incident: IncidentMaster): Promise<void> {
        const originalState = incident.enabled;
        incident.enabled = isEnabled; // Optimistic update

        const updatePayload = {
            tableName: 'incident-master', // Use correct table name
            id: incident.id,
            data: {
                enabled: isEnabled
            }
        };
        console.log(`Attempting to update incident ${incident.id} enabled status to: ${isEnabled}`);

        try {
            await this.update(updatePayload);
            console.log(`Successfully updated incident ${incident.id} enabled status.`);
            // this.toastr.success('Incident type status updated.', 'Success');
        } catch (error) {
            console.error(`Error updating enabled status for incident ${incident.id}:`, error);
            // this.toastr.error('Failed to update incident type status.', 'Update Error');
            // Revert UI change on error
            incident.enabled = originalState;
            // Force update detection if needed
            const index = this.incidentList.findIndex(i => i.id === incident.id);
            if (index !== -1) {
                 this.incidentList[index] = {...this.incidentList[index], enabled: originalState };
            }
        }
    }

    // Generic update via UpdateService (used by toggle and edit form)
    async update(data: { tableName: string, id: number, data: any }): Promise<void> {
        try {
            await this.updateService.update(data);
        } catch (error) {
            console.error("Update service call failed:", error);
            throw error; // Re-throw
        }
    }

    // --- Removed Placeholder/Unused Methods ---
    // Removed handleCreate, handleUpdate, toggleEnabled, handleDelete, sortBy, getSortClass
}