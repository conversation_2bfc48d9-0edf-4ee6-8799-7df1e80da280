import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';
import { AreaComponent } from './area.component';
import { AreaService } from '../../../services/master-management/area/area.service';
import { ZoneService } from '../../../services/zone/zone.service';
import { PlantManagementService } from '../../../services/plant-management/plant-management.service';
import { UpdateService } from '../../../services/update/update.service';

describe('AreaComponent', () => {
  let component: AreaComponent;
  let fixture: ComponentFixture<AreaComponent>;
  let zoneService: jasmine.SpyObj<ZoneService>;
  let plantService: jasmine.SpyObj<PlantManagementService>;
  let updateService: jasmine.SpyObj<UpdateService>;

  const mockZones = [
    { id: 1, title: 'Zone 1', zoneName: 'Zone 1', enabled: true, plantId: 1 },
    { id: 2, title: 'Zone 2', zoneName: 'Zone 2', enabled: true, plantId: 1 }
  ];

  const mockPlants = [
    { id: 1, name: 'Plant 1', enabled: true },
    { id: 2, name: 'Plant 2', enabled: true }
  ];

  beforeEach(async () => {
    const areaServiceSpy = jasmine.createSpyObj('AreaService', ['getArea']);
    const zoneServiceSpy = jasmine.createSpyObj('ZoneService', ['getZone', 'createZone']);
    const plantServiceSpy = jasmine.createSpyObj('PlantManagementService', ['getPlants']);
    const updateServiceSpy = jasmine.createSpyObj('UpdateService', ['update']);

    await TestBed.configureTestingModule({
      imports: [AreaComponent, FormsModule],
      providers: [
        { provide: AreaService, useValue: areaServiceSpy },
        { provide: ZoneService, useValue: zoneServiceSpy },
        { provide: PlantManagementService, useValue: plantServiceSpy },
        { provide: UpdateService, useValue: updateServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(AreaComponent);
    component = fixture.componentInstance;

    zoneService = TestBed.inject(ZoneService) as jasmine.SpyObj<ZoneService>;
    plantService = TestBed.inject(PlantManagementService) as jasmine.SpyObj<PlantManagementService>;
    updateService = TestBed.inject(UpdateService) as jasmine.SpyObj<UpdateService>;

    // Setup default mocks
    zoneService.getZone.and.returnValue(Promise.resolve({ data: mockZones, total: 2 }));
    plantService.getPlants.and.returnValue(Promise.resolve({ data: mockPlants, total: 2 }));
    updateService.update.and.returnValue(Promise.resolve());
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Component Initialization', () => {
    it('should initialize with default values', () => {
      expect(component.zoneList).toEqual([]);
      expect(component.listLoading).toBeFalse();
      expect(component.isCreateModalOpen).toBeFalse();
      expect(component.isEditModalOpen).toBeFalse();
      expect(component.isFilterModalOpen).toBeFalse();
      expect(component.currentPage).toBe(1);
      expect(component.itemsPerPage).toBe(10);
    });

    it('should load zones on init', () => {
      // Act
      component.ngOnInit();

      // Assert
      expect(zoneService.getZone).toHaveBeenCalled();
      expect(plantService.getPlants).toHaveBeenCalled();
    });

    it('should handle loading state during initialization', async () => {
      // Arrange
      let resolvePromise: any;
      const promise = new Promise(resolve => resolvePromise = resolve);
      zoneService.getZone.and.returnValue(promise);

      // Act
      const initPromise = component.loadZones(1);
      expect(component.listLoading).toBeTrue();

      resolvePromise({ data: mockZones, total: 2 });
      await initPromise;

      // Assert
      expect(component.listLoading).toBeFalse();
    });
  });

  describe('Modal Management', () => {
    it('should open and close filter modal', () => {
      // Test opening filter modal
      component.openFilterModal();
      expect(component.isFilterModalOpen).toBeTrue();

      // Test closing filter modal
      component.closeFilterModal();
      expect(component.isFilterModalOpen).toBeFalse();
    });

    it('should open and close create modal', () => {
      // Test opening create modal
      component.openCreateModal();
      expect(component.isCreateModalOpen).toBeTrue();
      expect(component.newZoneData.title).toBe('');
      expect(component.newZoneData.enabled).toBeTrue();

      // Test closing create modal
      component.closeCreateModal();
      expect(component.isCreateModalOpen).toBeFalse();
    });

    it('should open and close edit modal', () => {
      // Arrange
      const testZone = mockZones[0];

      // Test opening edit modal
      component.openEditModal(testZone);
      expect(component.isEditModalOpen).toBeTrue();
      expect(component.selectedZone).toEqual(testZone);

      // Test closing edit modal
      component.closeEditModal();
      expect(component.isEditModalOpen).toBeFalse();
      expect(component.selectedZone).toBeNull();
    });
  });

  describe('Zone Management', () => {
    it('should create new zone successfully', async () => {
      // Arrange
      component.newZoneData = { title: 'New Zone', enabled: true, plantId: 1, zoneId: null };
      zoneService.createZone.and.returnValue(Promise.resolve({ zoneName: 'New Zone', id: 3 }));

      // Act
      await component.submitCreateForm();

      // Assert
      expect(zoneService.createZone).toHaveBeenCalledWith({
        zoneName: 'New Zone',
        enabled: true,
        plantId: 1
      });
      expect(component.isCreateModalOpen).toBeFalse();
    });

    it('should handle create zone errors', async () => {
      // Arrange
      component.newZoneData = { title: 'New Zone', enabled: true, plantId: 1, zoneId: null };
      zoneService.createZone.and.returnValue(Promise.reject(new Error('Creation failed')));

      // Act
      await component.submitCreateForm();

      // Assert
      expect(component.createLoading).toBeFalse();
    });

    it('should update existing zone successfully', async () => {
      // Arrange
      const testZone = { id: 1, title: 'Updated Zone', enabled: true };
      component.selectedZone = testZone;
      updateService.update.and.returnValue(Promise.resolve());

      // Act
      await component.submitEditForm();

      // Assert
      expect(updateService.update).toHaveBeenCalledWith({
        tableName: 'zone',
        id: 1,
        data: {
          zoneName: 'Updated Zone',
          enabled: true
        }
      });
      expect(component.isEditModalOpen).toBeFalse();
    });

    it('should handle switch toggle', async () => {
      // Arrange
      const testZone = { id: 1, title: 'Test Zone', enabled: false };
      updateService.update.and.returnValue(Promise.resolve());

      // Act
      await component.onSwitchToggle(true, testZone);

      // Assert
      expect(updateService.update).toHaveBeenCalledWith({
        tableName: 'zone',
        id: 1,
        data: {
          enabled: true
        }
      });
      expect(testZone.enabled).toBeTrue();
    });

    it('should revert switch toggle on error', async () => {
      // Arrange
      const testZone = { id: 1, title: 'Test Zone', enabled: false };
      updateService.update.and.returnValue(Promise.reject(new Error('Update failed')));

      // Act
      await component.onSwitchToggle(true, testZone);

      // Assert
      expect(testZone.enabled).toBeFalse(); // Should be reverted
    });
  });

  describe('Filtering and Pagination', () => {
    it('should apply filters and reload zones', async () => {
      // Arrange
      component.filters.name = 'Test Zone';
      component.filters.enabled = 'true';

      // Act
      component.applyFilters();

      // Assert
      expect(component.currentPage).toBe(1);
      expect(zoneService.getZone).toHaveBeenCalled();
      expect(component.isFilterModalOpen).toBeFalse();
    });

    it('should reset filters to defaults', async () => {
      // Arrange
      component.filters.name = 'Test';
      component.filters.enabled = 'false';
      component.currentPage = 3;

      // Act
      component.resetFilters();

      // Assert
      expect(component.filters.name).toBeNull();
      expect(component.filters.enabled).toBe('true');
      expect(component.currentPage).toBe(1);
    });

    it('should handle page changes', () => {
      // Arrange
      spyOn(component, 'loadZones');

      // Act
      component.onPageChange(2);

      // Assert
      expect(component.currentPage).toBe(2);
      expect(component.loadZones).toHaveBeenCalledWith(2);
    });

    it('should not reload on same page', () => {
      // Arrange
      component.currentPage = 2;
      spyOn(component, 'loadZones');

      // Act
      component.onPageChange(2);

      // Assert
      expect(component.loadZones).not.toHaveBeenCalled();
    });
  });
});
