import { Component, OnInit, ViewChild } from '@angular/core'; // Added ViewChild
import { FormsModule, NgForm } from '@angular/forms'; // Added NgForm
import { CommonModule } from '@angular/common';
import { DepartmentService } from '../../../services/master-management/department/department.service'; // Ensure path is correct
import { UpdateService } from '../../../services/update/update.service';
import { AuthService } from '../../../services/auth.service';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { SwitchComponent } from "../../../shared/switch/switch.component";
import { PaginationComponent } from "../../../shared/pagination/pagination.component";
import { OffcanvasComponent } from "../../../shared/offcanvas/offcanvas.component";
import { ToastMessageComponent } from "../../../shared/toast-message/toast-message.component"; // Adjust path if needed
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap'; // For dropdown button
import * as XLSX from 'xlsx'; // For Excel generation
// Optional: Import ToastrService or similar
// import { ToastrService } from 'ngx-toastr';

// Interface for filter structure
interface DepartmentFilter {
    name?: string | null;
    enabled?: string | null;
    sortField?: string | null;
    sortDirection?: 'ASC' | 'DESC';
}

// Interface for Department Data
export interface Department { // Export if used by service
    id: number;
    title: string;
    enabled: boolean;
    createdAt?: string;
    updatedAt?: string;
}

// Interface for the data structure used in the create form
interface NewDepartmentData {
    title: string;
    enabled: boolean;
    // Add other creatable fields if needed
}

@Component({
    selector: 'app-department',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        SwitchComponent,
        PaginationComponent,
        OffcanvasComponent,
        NgbDropdownModule,
        ToastMessageComponent
    ],
    templateUrl: './department.component.html',
    styleUrls: ['./department.component.scss']
})
export class DepartmentComponent implements OnInit {
    // Access the form template variable
    @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;
    @ViewChild('createForm') createForm?: NgForm; // Optional: If you need to reset the form state

    // --- Modal States ---
    isFilterModalOpen = false;
    isEditModalOpen = false;
    isCreateModalOpen = false; // <-- New state for create modal

    // --- Data & Loading States ---
    departmentList: Department[] = [];
    listLoading = false;
    editLoading = false;
    createLoading = false;    // <-- New state for create form submission

    // --- Pagination ---
    currentPage = 1;
    itemsPerPage = 10;
    totalItems = 0;
    isDownloadingExcel = false;
    downloadType: 'current' | 'all' | null = null;

    // --- Filtering ---
    filters: DepartmentFilter = { /* ... defaults ... */
        name: null,
        enabled: 'true',
        sortField: 'title',
        sortDirection: 'ASC'
    };
    availableSortFields = [ /* ... fields ... */
        { value: 'id', label: 'ID' },
        { value: 'title', label: 'Department Name' },
        { value: 'enabled', label: 'Enabled Status' },
        { value: 'createdAt', label: 'Created Date' }
    ];

    // --- Editing ---
    selectedDepartment: Department | null = null;

    // --- Creating ---
    newDepartmentData: NewDepartmentData = { // <-- Object to hold new department form data
        title: '',
        enabled: true // Default new departments to active
    };


    constructor(
        readonly departmentService: DepartmentService, // Service to fetch/create/etc.
        readonly updateService: UpdateService,         // Service for generic updates (like toggle)
        private authService: AuthService,              // Service to get current user info
        // Optional: Inject ToastrService
        // private toastr: ToastrService
    ) { }

    ngOnInit(): void {
        this.loadDepartments(this.currentPage);
    }

    // Helper to get current list data
    getCurrentListData(): Department[] | undefined {
        return this.departmentList;
    }

    // Fetch ALL departments matching current filters (no pagination)
    async fetchAllFilteredDepartments(): Promise<Department[] | null> {
        this.listLoading = true; // Indicate loading
        const filterParams: string[] = [];
        if (this.filters.name) { filterParams.push(`title||$contL||${this.filters.name}`); }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
             filterParams.push(`enabled||$eq||${this.filters.enabled}`);
        }

        const data = {
            limit: 10000, // Large limit for "all"
            sort: `${this.filters.sortField || 'title'},${this.filters.sortDirection || 'ASC'}`, // Match default sort
            filter: filterParams
        };

        try {
            const param = createAxiosConfig(data);
            console.log('API Request Params (Departments Download - All Data):', JSON.stringify(param, null, 2));
            const response = await this.departmentService.getDepartments(param); // Use existing service method
            return response ?? [];
        } catch (error: any) {
            console.error("Error fetching all departments for download:", error);
            this.toast?.showErrorToast(error?.response?.data?.message || "Failed to retrieve full data for download.");
            return null;
        } finally {
             this.listLoading = false; // Reset loading indicator
        }
    }

    // Main download function
    async downloadExcel(type: 'current' | 'all') {
        if (this.isDownloadingExcel || this.listLoading) return; // Prevent concurrent actions

        this.isDownloadingExcel = true;
        this.downloadType = type;
        this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} departments...`);

        let dataToExport: Department[] | null = null;

        try {
            // 1. Get Data
            if (type === 'all') {
                dataToExport = await this.fetchAllFilteredDepartments();
            } else { // 'current'
                dataToExport = this.getCurrentListData() ?? null;
                if (dataToExport === undefined) { dataToExport = null; }
            }

            // 2. Check data
            if (dataToExport === null) { this.toast?.showErrorToast("Failed to retrieve data for download."); return; }
            if (!dataToExport || dataToExport.length === 0) { this.toast?.showErrorToast(`No departments available to download.`); return; }

            console.log(`Fetched ${dataToExport.length} departments for Excel export (${type}).`);

            // 3. Transform data
            const dataForExcel = dataToExport.map(dept => ({
                'Department ID': dept.id,
                'Department Name': dept.title,
                'Status': dept.enabled ? 'Active' : 'Inactive',
                'Created At': dept.createdAt ? new Date(dept.createdAt).toLocaleString() : 'N/A',
                'Updated At': dept.updatedAt ? new Date(dept.updatedAt).toLocaleString() : 'N/A',
            }));

            // 4. Create Worksheet and Workbook
            const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
            const wb: XLSX.WorkBook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'Departments'); // Sheet name

            // 5. Generate File Name
            const dateStr = new Date().toISOString().slice(0, 10);
            const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
            const fileName = `Departments_${typeStr}_${dateStr}.xlsx`;

            // 6. Trigger Download
            XLSX.writeFile(wb, fileName);
            this.toast?.showSuccessToast(`Excel file download started (${type}).`);

        } catch (error) {
            console.error(`Error generating Excel file (${type}):`, error);
            this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
        } finally {
            this.isDownloadingExcel = false;
            this.downloadType = null;
        }
    }

    // --- Filter Modal Methods ---
    openFilterModal(): void { this.isFilterModalOpen = true; }
    closeFilterModal(): void { this.isFilterModalOpen = false; }

    @ViewChild('filterForm') filterForm!: NgForm;

    applyFilters(): void {
        // Check if form is valid before applying filters
        if (this.filterForm && this.filterForm.invalid) {
            this.toast?.showErrorToast("Please correct the validation errors before applying filters.");
            return;
        }

        // Trim string values to prevent whitespace-only searches
        if (this.filters.name) {
            this.filters.name = this.filters.name.trim();
        }

        this.currentPage = 1;
        this.loadDepartments(1);
        this.closeFilterModal();
    }

    resetFilters(): void {
        this.filters = {
            name: null,
            enabled: 'true',
            sortField: 'title',
            sortDirection: 'ASC'
        };
        this.currentPage = 1;
        this.loadDepartments(1);
    }

    // --- Edit Modal Methods (no changes) ---
    openEditModal(department: Department): void { this.selectedDepartment = { ...department }; this.isEditModalOpen = true; this.editLoading = false; }
    closeEditModal(): void { this.isEditModalOpen = false; this.selectedDepartment = null; }
    @ViewChild('editForm') editForm!: NgForm;

    async submitEditForm(): Promise<void> {
        if (!this.selectedDepartment || this.selectedDepartment.id == null) { return; }

        // Check form validity
        if (this.editForm && this.editForm.invalid) {
            this.toast?.showErrorToast("Please correct the validation errors before saving.");
            // Mark fields as touched to show validation errors
            Object.values(this.editForm.controls).forEach(control => {
                control.markAsTouched();
            });
            return;
        }

        // Trim the title to remove any leading/trailing whitespace
        if (this.selectedDepartment.title) {
            this.selectedDepartment.title = this.selectedDepartment.title.trim();
        }

        this.editLoading = true;
        const updatePayload = {
            tableName: 'department',
            id: this.selectedDepartment.id,
            data: {
                title: this.selectedDepartment.title,
                enabled: this.selectedDepartment.enabled,
                businessUnitId: this.authService.getBusinessUnitId()
            }
        };
        try {
            await this.update(updatePayload);
            this.toast?.showSuccessToast('Department updated successfully!');
            this.closeEditModal();
            this.loadDepartments(this.currentPage); // Reload current page
        } catch (error) {
            console.error("Error submitting department update:", error);
            this.toast?.showErrorToast('Failed to update department.');
        } finally {
            this.editLoading = false;
        }
    }


    // --- *** NEW: Create Modal Methods *** ---

    /** Opens the create department offcanvas modal and resets the form data. */
    openCreateModal(): void {
        // Reset form data to defaults whenever the modal is opened
        this.newDepartmentData = {
            title: '',
            enabled: true
        };
        // Optionally reset form validation state if ViewChild is used
        this.createForm?.resetForm({ enabled: true }); // Reset form state, keep default value

        this.isCreateModalOpen = true;
        this.createLoading = false; // Ensure loading state is reset
    }

    /** Closes the create department offcanvas modal. */
    closeCreateModal(): void {
        this.isCreateModalOpen = false;
        // Optionally clear data, though openCreateModal already resets it
        // this.newDepartmentData = { title: '', enabled: true };
    }

    /** Handles the submission of the create department form. */
    async submitCreateForm(): Promise<void> {
        // Double-check form validity (though button is disabled)
        if (this.createForm?.invalid) {
            this.toast?.showErrorToast("Please correct the validation errors before creating.");
            // Mark fields as touched to show validation errors
            Object.values(this.createForm.controls).forEach(control => {
                 control.markAsTouched();
            });
            return;
        }

        // Trim the title to remove any leading/trailing whitespace
        if (this.newDepartmentData.title) {
            this.newDepartmentData.title = this.newDepartmentData.title.trim();
        }

        this.createLoading = true; // Show loading indicator
        console.log('Submitting new department:', this.newDepartmentData);

        try {
            // Call the create method from DepartmentService
            const createdDept = await this.departmentService.createDepartment(this.newDepartmentData);
            console.log('Department created successfully:', createdDept);

            // Show success notification
            this.toast?.showSuccessToast(`Department "${createdDept.title}" created successfully!`);

            this.closeCreateModal(); // Close the modal on success

            // Reload the list to show the new department.
            // Going to page 1 is usually safer if sorting matters.
            this.currentPage = 1;
            this.loadDepartments(this.currentPage);

        } catch (error) {
            console.error("Error creating department:", error);
            // Show error notification
            this.toast?.showErrorToast('Failed to create department. Please try again.');
            // Keep the modal open on error for correction.
        } finally {
            this.createLoading = false; // Hide loading indicator
        }
    }


    // --- Data Loading & Update Methods (Mostly unchanged) ---

    async loadDepartments(page: number): Promise<void> {
         this.listLoading = true;
         this.departmentList = [];

        const filterParams: string[] = [];
        if (this.filters.name) { filterParams.push(`title||$contL||${this.filters.name}`); }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
            filterParams.push(`enabled||$eq||${this.filters.enabled}`);
        }

        const requestData = {
            page: page,
            limit: this.itemsPerPage,
            sort: `${this.filters.sortField || 'title'},${this.filters.sortDirection || 'ASC'}`,
            filter: filterParams
        };

        try {
            const params = createAxiosConfig(requestData); // Assuming this prepares HttpParams or similar
             // Make sure getDepartments returns { data: Department[], total: number }
            const response = await this.departmentService.getDepartments(params);
            this.departmentList = response?.data ?? [];
            this.totalItems = response?.total ?? 0;
        } catch (error) {
            console.error("Error fetching departments:", error);
            this.departmentList = [];
            this.totalItems = 0;
            // this.toastr.error('Failed to load departments.', 'Error');
        } finally {
            this.listLoading = false;
        }
     }

    onPageChange(page: number): void { if (page !== this.currentPage) { this.currentPage = page; this.loadDepartments(this.currentPage); } }

    async onSwitchToggle(isEnabled: boolean, id: number): Promise<void> { /* ... existing toggle logic using update() ... */
        const departmentIndex = this.departmentList.findIndex(d => d.id === id);
        const updatePayload = { tableName: 'department', id: id, data: { enabled: isEnabled } };
        console.log(`Attempting to update department ${id} enabled status to: ${isEnabled}`);
        try {
            await this.update(updatePayload);
            console.log(`Successfully updated department ${id} enabled status.`);
             if (departmentIndex !== -1) { this.departmentList[departmentIndex].enabled = isEnabled; }
            // this.toastr.success('Department status updated.', 'Success');
        } catch (error) {
            console.error(`Error updating enabled status for department ${id}:`, error);
            // this.toastr.error('Failed to update department status.', 'Update Error');
             if (departmentIndex !== -1) { this.departmentList[departmentIndex].enabled = !isEnabled; }
        }
     }

    // Generic update via UpdateService (used by toggle and potentially edit form if preferred)
    async update(data: { tableName: string, id: number, data: any }): Promise<void> {
        try {
            await this.updateService.update(data);
        } catch (error) {
            console.error("Update service call failed:", error);
            throw error; // Re-throw
        }
    }

} // End of component