<app-toast-message></app-toast-message>
<div class="card" id="activated-qrcode">
    <div class="card-header d-flex align-items-center justify-content-between">
      <div>
        <h6 class="mb-0">Departments</h6>
      </div>
      <div class="d-flex align-items-center">
        <!-- *** REPLACE the existing simple Download button with this Dropdown *** -->
        <div ngbDropdown class="d-inline-block me-2">
            <button type="button" class="btn btn-sm adani-btn dropdown-toggle" id="downloadDeptExcelDropdown" ngbDropdownToggle
                [disabled]="isDownloadingExcel || listLoading">
                <span *ngIf="!isDownloadingExcel">
                    <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
                </span>
                <span *ngIf="isDownloadingExcel">
                    <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                    Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '') }}...
                </span>
            </button>
            <ul ngbDropdownMenu aria-labelledby="downloadDeptExcelDropdown">
                <li>
                    <button ngbDropdownItem (click)="downloadExcel('current')" [disabled]="isDownloadingExcel || listLoading || (departmentList?.length ?? 0) === 0">
                        <i class="bi bi-download me-1"></i> Download Current Page ({{ departmentList?.length ?? 0 }})
                    </button>
                </li>
                <li>
                    <button ngbDropdownItem (click)="downloadExcel('all')" [disabled]="isDownloadingExcel || listLoading || totalItems === 0">
                        <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
                    </button>
                </li>
            </ul>
        </div>
          <button class="btn-sm adani-btn ms-2" (click)="openCreateModal()" title="Create New Department">
            <i class="bi bi-plus-circle"></i> Create New
        </button>
          <img src="../../../assets/svg/filter.svg" class="filter-button ms-3" (click)="openFilterModal()" alt=""
            style="width: 35px;" />
            <!-- *** NEW: Create Button *** -->
        </div>
          </div>
      <div class="card-body">
        <div class="table-container">
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-header">
                      <tr class="text-center">
                        <th scope="col">Id</th>
                        <th scope="col">Enabled/Disabled</th>
                        <th scope="col">Department Name</th>
                        <th scope="col">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let department of departmentList">
                        <td>{{ department.id }}</td>
                        <td>
                            <app-switch [(checked)]="department.enabled" [requireConfirmation]="true" (checkedChange)="onSwitchToggle($event, department.id)" onLabel="Active" offLabel="Inactive"></app-switch>
                        </td>
                        <td>{{ department.title }}</td>
                        <td class="actions">
                          <button class="adani-btn" (click)="openEditModal(department)">
                            <i class="bi bi-pencil edit"></i>
                        </button>
                        </td>
                      </tr>
                    </tbody>
                  </table>
            </div>
        </div>


      </div>
      <div class="card-footer text-muted text-center">
        <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
        (pageChange)="onPageChange($event)"></app-pagination>
      </div>
  </div>
  <app-offcanvas [title]="'Filter Departments'" *ngIf="isFilterModalOpen" (onClickCross)="closeFilterModal()">
    <div class="filter-container p-3">
        <form #filterForm="ngForm" (ngSubmit)="applyFilters()">
            <div class="row g-3">

                <div class="col-12">
                    <label class="form-label" for="filterDeptName">Department Name</label>
                    <input type="text" id="filterDeptName" class="form-control" placeholder="Search by Department Name"
                           [(ngModel)]="filters.name" name="name" #filterName="ngModel"
                           pattern="^[a-zA-Z\s]*$" maxlength="30"
                           [ngClass]="{'is-invalid': filterName.invalid && (filterName.dirty || filterName.touched)}">
                    <div *ngIf="filterName.invalid && (filterName.dirty || filterName.touched)" class="invalid-feedback">
                        <div *ngIf="filterName.errors?.['pattern']">Department name should contain only alphabets.</div>
                    </div>
                </div>

                 <div class="col-12">
                    <label class="form-label" for="filterEnabledDept">Enabled Status</label>
                    <select id="filterEnabledDept" class="form-select"
                            [(ngModel)]="filters.enabled" name="enabled"> <!-- Bind to filters.enabled -->
                        <option [ngValue]="null">Any</option>
                        <option value="true">Yes</option>
                        <option value="false">No</option>
                    </select>
                </div>

                <div class="col-12">
                    <label class="form-label" for="filterSortByDept">Sort By</label>
                    <select id="filterSortByDept" class="form-select"
                            [(ngModel)]="filters.sortField" name="sortField"> <!-- Bind to filters.sortField -->
                        <option [ngValue]="null">Default Sort (Name ASC)</option>
                        <option *ngFor="let field of availableSortFields" [value]="field.value">{{ field.label }}</option>
                    </select>
                     <label class="form-label mt-2" for="filterSortDirDept">Sort Direction</label>
                     <select id="filterSortDirDept" class="form-select"
                            [(ngModel)]="filters.sortDirection" name="sortDirection"> <!-- Bind to filters.sortDirection -->
                        <option value="ASC">Ascending</option>
                        <option value="DESC">Descending</option>
                    </select>
                </div>

                <div class="col-12 mt-4 d-grid gap-2">
                    <button type="submit" class="btn adani-btn" [disabled]="filterForm.invalid">
                        <i class="bi bi-search me-1"></i> Search
                    </button>
                     <button type="button" class="btn btn-secondary" (click)="resetFilters()">
                        <i class="bi bi-arrow-clockwise me-1"></i> Reset Filters
                    </button>
                </div>
            </div>
        </form>
    </div>
</app-offcanvas>
<!-- ********** NEW: Edit Offcanvas ********** -->
<app-offcanvas [title]="'Edit Department'" *ngIf="isEditModalOpen" (onClickCross)="closeEditModal()">
  <div class="edit-container p-3">
      <!-- Ensure selectedDepartment is not null before rendering the form -->
      <form *ngIf="selectedDepartment" #editForm="ngForm" (ngSubmit)="submitEditForm()">
          <div class="row g-3">

              <!-- Department ID (Readonly) -->
              <div class="col-12">
                  <label class="form-label" for="editDeptId">Department ID</label>
                  <input type="text" id="editDeptId" class="form-control"
                         [value]="selectedDepartment.id" name="id" readonly disabled>
              </div>

              <!-- Department Name (Editable) -->
              <div class="col-12">
                  <label class="form-label" for="editDeptName">Department Name</label>
                  <input type="text" id="editDeptName" class="form-control" placeholder="Enter Department Name"
                         [(ngModel)]="selectedDepartment.title" name="title" required #titleInput="ngModel"
                         pattern="^[a-zA-Z\s]*$" maxlength="30"
                         [ngClass]="{'is-invalid': titleInput.invalid && (titleInput.dirty || titleInput.touched)}">
                   <!-- Validation Messages -->
                   <div *ngIf="titleInput.invalid && (titleInput.dirty || titleInput.touched)" class="invalid-feedback">
                      <div *ngIf="titleInput.errors?.['required']">Department name is required.</div>
                      <div *ngIf="titleInput.errors?.['pattern']">Department name should contain only alphabets.</div>
                      <div *ngIf="titleInput.errors?.['maxlength']">Department name cannot exceed 30 characters.</div>
                  </div>
                  <!-- Character count display -->
                  <small *ngIf="selectedDepartment?.title" class="text-muted">
                      {{ selectedDepartment.title.length }}/30 characters
                  </small>
              </div>

               <!-- Enabled Status (Could use app-switch or a select) -->
               <div class="col-12">
                   <label class="form-label d-block mb-2">Status</label> <!-- d-block for spacing -->
                    <app-switch
                          [(checked)]="selectedDepartment.enabled"
                          name="enabled"
                          onLabel="Active"
                          offLabel="Inactive">
                    </app-switch>
               </div>


              <!-- Action Buttons -->
              <div class="col-12 mt-4 d-flex justify-content-end gap-2"> <!-- Use flex for button alignment -->
                  <button type="button" class="btn btn-secondary" (click)="closeEditModal()">
                      Cancel
                  </button>
                  <button type="submit" class="btn adani-btn" [disabled]="editForm.invalid || editLoading">
                       <span *ngIf="!editLoading"><i class="bi bi-save me-1"></i> Save Changes</span>
                       <span *ngIf="editLoading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                       <span *ngIf="editLoading"> Saving...</span>
                  </button>
              </div>
          </div>
      </form>
       <!-- Show loading indicator if selectedDepartment is being fetched or form is null -->
      <div *ngIf="!selectedDepartment && isEditModalOpen" class="text-center p-5">
           <div class="spinner-border spinner-border-sm" role="status">
               <span class="visually-hidden">Loading form...</span>
           </div>
      </div>
  </div>
</app-offcanvas>
<!-- ********** END: Edit Offcanvas ********** -->
 <!-- ********** NEW: Create Offcanvas ********** -->
<app-offcanvas [title]="'Create New Department'" *ngIf="isCreateModalOpen" (onClickCross)="closeCreateModal()">
  <div class="create-container p-3">
      <!-- Form for creating a new department -->
      <form #createForm="ngForm" (ngSubmit)="submitCreateForm()">
          <div class="row g-3">

              <!-- Department Name (Required) -->
              <div class="col-12">
                  <label class="form-label" for="createDeptName">Department Name</label>
                  <input type="text" id="createDeptName" class="form-control" placeholder="Enter New Department Name"
                         [(ngModel)]="newDepartmentData.title" name="title" required #createTitleInput="ngModel"
                         pattern="^[a-zA-Z\s]*$" maxlength="30"
                         [ngClass]="{'is-invalid': createTitleInput.invalid && (createTitleInput.dirty || createTitleInput.touched)}">
                  <!-- Validation Messages -->
                  <div *ngIf="createTitleInput.invalid && (createTitleInput.dirty || createTitleInput.touched)" class="invalid-feedback">
                      <div *ngIf="createTitleInput.errors?.['required']">Department name is required.</div>
                      <div *ngIf="createTitleInput.errors?.['pattern']">Department name should contain only alphabets.</div>
                      <div *ngIf="createTitleInput.errors?.['maxlength']">Department name cannot exceed 30 characters.</div>
                  </div>
                  <!-- Character count display -->
                  <small *ngIf="newDepartmentData.title" class="text-muted">
                      {{ newDepartmentData.title.length }}/30 characters
                  </small>
              </div>

              <!-- Enabled Status (Default to Active/true) -->
              <div class="col-12">
                  <label class="form-label d-block mb-2">Status</label>
                  <app-switch
                        [(checked)]="newDepartmentData.enabled"
                        name="enabled"
                        onLabel="Active"
                        offLabel="Inactive">
                  </app-switch>
              </div>

              <!-- Action Buttons -->
              <div class="col-12 mt-4 d-flex justify-content-end gap-2">
                  <button type="button" class="btn btn-secondary" (click)="closeCreateModal()">
                      Cancel
                  </button>
                  <button type="submit" class="btn adani-btn" [disabled]="createForm.invalid || createLoading">
                      <!-- Show loading indicator when submitting -->
                      <span *ngIf="!createLoading"><i class="bi bi-plus-circle-fill me-1"></i> Create Department</span>
                      <span *ngIf="createLoading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                      <span *ngIf="createLoading"> Creating...</span>
                  </button>
              </div>
          </div>
      </form>
  </div>
</app-offcanvas>
<!-- ********** END: Create Offcanvas ********** -->
