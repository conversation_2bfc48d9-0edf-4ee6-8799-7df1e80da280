import { Component, OnInit, ViewChild } from '@angular/core'; // Added ViewChild
import { FormsModule, NgForm } from '@angular/forms'; // Added NgForm
import { BodyPartService } from '../../../services/master-management/body-part/body-part.service';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { CommonModule } from '@angular/common';
import { SwitchComponent } from "../../../shared/switch/switch.component";
import { PaginationComponent } from "../../../shared/pagination/pagination.component";
import { OffcanvasComponent } from "../../../shared/offcanvas/offcanvas.component";
import { UpdateService } from '../../../services/update/update.service';
// Optional: Import ToastrService or similar
// import { ToastrService } from 'ngx-toastr';
import { ToastMessageComponent } from '../../../shared/toast-message/toast-message.component'; // Adjust path if needed
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap'; // For dropdown button
import * as XLSX from 'xlsx'; // For Excel generation


// Interface for filter structure
interface BodyPartFilter {
    name?: string | null;
    enabled?: string | null;
    sortField?: string | null;
    sortDirection?: 'ASC' | 'DESC';
}

// *** NEW: Interface for Body Part Data ***
export interface BodyPart {
    id: number;
    title: string;
    enabled: boolean;
    createdAt?: string; // Optional
    updatedAt?: string; // Optional
}

// *** NEW: Interface for Create Form Data ***
interface NewBodyPartData {
    title: string;
    enabled: boolean;
}


@Component({
    selector: 'app-body-part',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        SwitchComponent,
        PaginationComponent,
        OffcanvasComponent,
        NgbDropdownModule,
        ToastMessageComponent
    ],
    templateUrl: './body-part.component.html',
    styleUrl: './body-part.component.scss'
})
export class BodyPartComponent implements OnInit {
    // Access the form template variables
    @ViewChild('createForm') createForm?: NgForm;
    @ViewChild('editForm') editForm?: NgForm;
    @ViewChild('filterForm') filterForm?: NgForm;
    @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;

    // --- Modal States ---
    isFilterModalOpen = false; // Renamed from isEditUserModalOpen
    isEditModalOpen = false;   // <-- New state for edit modal
    isCreateModalOpen = false; // <-- New state for create modal

    // --- Data & Loading States ---
    bodyPartList: BodyPart[] = []; // Use BodyPart interface
    listLoading = false;
    editLoading = false;     // <-- New state for edit form submission
    createLoading = false;   // <-- New state for create form submission
    isDownloadingExcel = false;
    downloadType: 'current' | 'all' | null = null;

    // --- Pagination ---
    currentPage = 1;
    itemsPerPage = 10;
    totalItems = 0;

    // --- Filtering ---
    filters: BodyPartFilter = {
        name: null,
        enabled: 'true',
        sortField: 'title',
        sortDirection: 'ASC'
    };
    availableSortFields = [
        { value: 'id', label: 'ID' },
        { value: 'title', label: 'Body Part Name' },
        { value: 'enabled', label: 'Enabled Status' },
        { value: 'createdAt', label: 'Created Date' }
    ];

    // --- Editing ---
    selectedBodyPart: BodyPart | null = null; // <-- Object to hold data for editing

    // --- Creating ---
    newBodyPartData: NewBodyPartData = { // <-- Object to hold new body part form data
        title: '',
        enabled: true // Default new body parts to active
    };

    constructor(
        readonly bodyPartService: BodyPartService,
        readonly updateService: UpdateService,
        // Optional: Inject ToastrService
        // private toastr: ToastrService
    ) { }

    ngOnInit() {
        this.loadBodyParts(this.currentPage);
    }

    // Helper to get current list data
    getCurrentListData(): BodyPart[] | undefined {
        return this.bodyPartList;
    }

    // Fetch ALL body parts matching current filters (no pagination)
    async fetchAllFilteredBodyParts(): Promise<BodyPart[] | null> {
        this.listLoading = true; // Indicate loading
        const filterParams: string[] = [];
        if (this.filters.name) { filterParams.push(`title||$contL||${this.filters.name}`); }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
             filterParams.push(`enabled||$eq||${this.filters.enabled}`);
        }

        const data = {
            limit: 10000, // Large limit for "all"
            sort: `${this.filters.sortField || 'title'},${this.filters.sortDirection || 'ASC'}`, // Match default sort
            filter: filterParams
        };

        try {
            const params = createAxiosConfig(data);
            console.log('API Request Params (Body Parts Download - All Data):', JSON.stringify(params, null, 2));
            const response = await this.bodyPartService.getBodyPart(params); // Use existing service method
            return response ?? [];
        } catch (error: any) {
            console.error("Error fetching all body parts for download:", error);
            this.toast?.showErrorToast(error?.response?.data?.message || "Failed to retrieve full data for download.");
            return null;
        } finally {
             this.listLoading = false; // Reset loading indicator
        }
    }

    // Main download function
    async downloadExcel(type: 'current' | 'all') {
        if (this.isDownloadingExcel || this.listLoading) return; // Prevent concurrent actions

        this.isDownloadingExcel = true;
        this.downloadType = type;
        this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} body parts...`);

        let dataToExport: BodyPart[] | null = null;

        try {
            // 1. Get Data
            if (type === 'all') {
                dataToExport = await this.fetchAllFilteredBodyParts();
            } else { // 'current'
                dataToExport = this.getCurrentListData() ?? null;
                if (dataToExport === undefined) { dataToExport = null; }
            }

            // 2. Check data
            if (dataToExport === null) { this.toast?.showErrorToast("Failed to retrieve data for download."); return; }
            if (!dataToExport || dataToExport.length === 0) { this.toast?.showErrorToast(`No body parts available to download.`); return; }

            console.log(`Fetched ${dataToExport.length} body parts for Excel export (${type}).`);

            // 3. Transform data
            const dataForExcel = dataToExport.map(bp => ({
                'Body Part ID': bp.id,
                'Body Part Name': bp.title,
                'Status': bp.enabled ? 'Active' : 'Inactive',
                'Created At': bp.createdAt ? new Date(bp.createdAt).toLocaleString() : 'N/A',
                'Updated At': bp.updatedAt ? new Date(bp.updatedAt).toLocaleString() : 'N/A',
            }));

            // 4. Create Worksheet and Workbook
            const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
            const wb: XLSX.WorkBook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'BodyParts'); // Sheet name

            // 5. Generate File Name
            const dateStr = new Date().toISOString().slice(0, 10);
            const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
            const fileName = `BodyParts_${typeStr}_${dateStr}.xlsx`;

            // 6. Trigger Download
            XLSX.writeFile(wb, fileName);
            this.toast?.showSuccessToast(`Excel file download started (${type}).`);

        } catch (error) {
            console.error(`Error generating Excel file (${type}):`, error);
            this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
        } finally {
            this.isDownloadingExcel = false;
            this.downloadType = null;
        }
    }

    // --- Filter Modal Methods ---
    openFilterModal(): void { this.isFilterModalOpen = true; }
    closeFilterModal(): void { this.isFilterModalOpen = false; } // Renamed from closeModal
    applyFilters(): void {
        if (this.filterForm?.invalid) {
            Object.values(this.filterForm.controls).forEach(control => control.markAsTouched());
            console.warn('Filter form submitted while invalid.');
            return;
        }

        this.currentPage = 1;
        this.loadBodyParts(this.currentPage);
        this.closeFilterModal();
    }
    resetFilters(): void {
        this.filters = {
            name: null,
            enabled: 'true',
            sortField: 'title',
            sortDirection: 'ASC'
        };
        this.currentPage = 1;
        this.loadBodyParts(this.currentPage);
        // Optionally close modal: this.closeFilterModal();
    }

    // --- *** NEW: Edit Modal Methods *** ---
    /** Opens the edit body part offcanvas modal. */
    openEditModal(bodyPart: BodyPart): void {
        this.selectedBodyPart = { ...bodyPart }; // Create a copy
        this.isEditModalOpen = true;
        this.editLoading = false;
    }

    /** Closes the edit body part offcanvas modal. */
    closeEditModal(): void {
        this.isEditModalOpen = false;
        this.selectedBodyPart = null;
    }

    /** Handles the submission of the edit body part form. */
    async submitEditForm(): Promise<void> {
        if (!this.selectedBodyPart || this.selectedBodyPart.id == null) {
            console.error("Cannot save, selected body part is null or has no ID.");
            // this.toastr.error('Cannot save, no body part selected.', 'Error');
            return;
        }

        if (this.editForm?.invalid) {
            Object.values(this.editForm.controls).forEach(control => control.markAsTouched());
            console.warn('Edit form submitted while invalid.');
            return;
        }

        this.editLoading = true;
        const updatePayload = {
            tableName: 'body-part-master', // Critical: Ensure this matches your backend/service expectation
            id: this.selectedBodyPart.id,
            data: {
                title: this.selectedBodyPart.title,
                enabled: this.selectedBodyPart.enabled
                // Add other editable fields if necessary
            }
        };

        try {
            await this.update(updatePayload); // Use the generic update method
            // this.toastr.success('Body part updated successfully!', 'Saved');

            // Update list locally (optional)
            const index = this.bodyPartList.findIndex(bp => bp.id === this.selectedBodyPart?.id);
            if (index !== -1 && this.selectedBodyPart) {
                 this.bodyPartList[index] = { ...this.selectedBodyPart };
            }

            this.closeEditModal();
            // Consider reloading if necessary: this.loadBodyParts(this.currentPage);

        } catch (error) {
            console.error("Error submitting body part update:", error);
            // this.toastr.error('Failed to update body part.', 'Error');
        } finally {
            this.editLoading = false;
        }
    }


    // --- *** NEW: Create Modal Methods *** ---

    /** Opens the create body part offcanvas modal and resets the form data. */
    openCreateModal(): void {
        this.newBodyPartData = {
            title: '',
            enabled: true
        };
        this.createForm?.resetForm({ enabled: true }); // Reset form state with default
        this.isCreateModalOpen = true;
        this.createLoading = false;
    }

    /** Closes the create body part offcanvas modal. */
    closeCreateModal(): void {
        this.isCreateModalOpen = false;
    }

    /** Handles the submission of the create body part form. */
    async submitCreateForm(): Promise<void> {
        if (this.createForm?.invalid) {
             Object.values(this.createForm.controls).forEach(control => control.markAsTouched());
             console.warn('Create form submitted while invalid.');
             return;
        }

        this.createLoading = true;
        console.log('Submitting new body part:', this.newBodyPartData);

        try {
            // *** ASSUMPTION: bodyPartService has a createBodyPart method ***
            const createdBodyPart = await this.bodyPartService.createBodyPart(this.newBodyPartData);
            console.log('Body part created successfully:', createdBodyPart);

            // this.toastr.success(`Body part "${createdBodyPart.title}" created successfully!`, 'Created');

            this.closeCreateModal();
            // Reload list, go to page 1
            this.currentPage = 1;
            this.loadBodyParts(this.currentPage);

        } catch (error) {
            console.error("Error creating body part:", error);
            // this.toastr.error('Failed to create body part. Please try again.', 'Creation Error');
        } finally {
            this.createLoading = false;
        }
    }


    // --- Data Loading & Update Methods ---

    /** Fetches body parts from the backend. (Refactored from getBodyPartList) */
    async loadBodyParts(page: number): Promise<void> {
        this.listLoading = true;
        // this.bodyPartList = []; // Clear only after loading starts

        // Build filter params dynamically
        const filterParams: string[] = [];
        if (this.filters.name) {
            filterParams.push(`title||$contL||${this.filters.name}`);
        }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
            filterParams.push(`enabled||$eq||${this.filters.enabled}`);
        }

        const requestData = {
            page: page,
            limit: this.itemsPerPage,
            sort: `${this.filters.sortField || 'title'},${this.filters.sortDirection || 'ASC'}`,
            filter: filterParams // Use dynamic filters
        };

        try {
            const params = createAxiosConfig(requestData); // Use utility function
            // Ensure service method returns { data: BodyPart[], total: number }
            const response = await this.bodyPartService.getBodyPart(params); // Pass params object
            this.bodyPartList = response?.data ?? []; // Use response.data
            this.totalItems = response?.total ?? 0; // Use response.total
        } catch (error) {
            console.error("Error fetching body parts:", error);
            this.bodyPartList = []; // Clear list on error
            this.totalItems = 0;
            // this.toastr.error('Failed to load body parts.', 'Error');
        } finally {
            this.listLoading = false; // Set loading false
        }
    }

    /** Handles page changes. */
    onPageChange(page: number): void {
        if (page !== this.currentPage) {
            this.currentPage = page;
            this.loadBodyParts(this.currentPage); // Call refactored method
        }
    }

    /** Handles the toggle switch change event in the table row. */
    async onSwitchToggle(isEnabled: boolean, bodyPart: BodyPart): Promise<void> {
        const originalState = bodyPart.enabled;
        bodyPart.enabled = isEnabled; // Optimistic update

        const updatePayload = {
            tableName: 'body-part-master', // Use correct table name
            id: bodyPart.id,
            data: {
                enabled: isEnabled
            }
        };
        console.log(`Attempting to update body part ${bodyPart.id} enabled status to: ${isEnabled}`);

        try {
            await this.update(updatePayload);
            console.log(`Successfully updated body part ${bodyPart.id} enabled status.`);
            // this.toastr.success('Body part status updated.', 'Success');
        } catch (error) {
            console.error(`Error updating enabled status for body part ${bodyPart.id}:`, error);
            // this.toastr.error('Failed to update body part status.', 'Update Error');
            // Revert UI change on error
            bodyPart.enabled = originalState;
            // Force update detection if needed
            const index = this.bodyPartList.findIndex(bp => bp.id === bodyPart.id);
            if (index !== -1) {
                 this.bodyPartList[index] = {...this.bodyPartList[index], enabled: originalState };
            }
        }
    }

    // Generic update via UpdateService (used by toggle and edit form)
    async update(data: { tableName: string, id: number, data: any }): Promise<void> {
        try {
            await this.updateService.update(data);
        } catch (error) {
            console.error("Update service call failed:", error);
            throw error; // Re-throw
        }
    }

    // --- Removed Placeholder/Unused Methods ---
    // Removed handleCreate, handleUpdate, toggleEnabled, handleDelete, sortBy, getSortClass
}