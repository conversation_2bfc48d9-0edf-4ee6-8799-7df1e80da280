import { Component, OnInit, ViewChild } from '@angular/core'; // Added ViewChild
import { FormsModule, NgForm } from '@angular/forms'; // Added FormsModule, NgForm
import { InspectionToolService } from '../../../services/master-management/inspection-tool/inspection-tool.service';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config'; // Added import
import { CommonModule } from '@angular/common';
import { PaginationComponent } from "../../../shared/pagination/pagination.component";
import { OffcanvasComponent } from "../../../shared/offcanvas/offcanvas.component";
import { SwitchComponent } from "../../../shared/switch/switch.component";
import { UpdateService } from '../../../services/update/update.service';
// Optional: Import ToastrService or similar
// import { ToastrService } from 'ngx-toastr';
import { ToastMessageComponent } from '../../../shared/toast-message/toast-message.component'; // Adjust path if needed
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap'; // For dropdown button
import * as XLSX from 'xlsx'; // For Excel generation


// *** NEW: Interface for Filter structure ***
interface InspectionToolFilter {
    name?: string | null;
    enabled?: string | null;
    sortField?: string | null;
    sortDirection?: 'ASC' | 'DESC';
}

// *** NEW: Interface for Inspection Tool Data ***
export interface InspectionTool {
    id: number;
    title: string;
    enabled: boolean;
    createdAt?: string; // Optional
    updatedAt?: string; // Optional
}

// *** NEW: Interface for Create Form Data ***
interface NewInspectionToolData {
    title: string;
    enabled: boolean;
}


@Component({
    selector: 'app-inspection-tool',
    standalone: true,
    // Added FormsModule
    imports: [
        CommonModule,
        FormsModule,
        PaginationComponent,
        OffcanvasComponent,
        SwitchComponent,
        NgbDropdownModule,
        ToastMessageComponent
    ],
    templateUrl: './inspection-tool.component.html',
    styleUrl: './inspection-tool.component.scss'
})
export class InspectionToolComponent implements OnInit {
    // Access the form template variables
    @ViewChild('createForm') createForm?: NgForm;
    @ViewChild('editForm') editForm?: NgForm;
    @ViewChild('filterForm') filterForm?: NgForm;
    @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;

    // --- Modal States ---
    isFilterModalOpen = false; // Renamed from isEditUserModalOpen
    isEditModalOpen = false;   // <-- New state for edit modal
    isCreateModalOpen = false; // <-- New state for create modal
    isDownloadingExcel = false;
    downloadType: 'current' | 'all' | null = null;

    // --- Data & Loading States ---
    inspectionToolList: InspectionTool[] = []; // Use InspectionTool interface
    listLoading = false; // Added loading state
    editLoading = false;     // <-- New state for edit form submission
    createLoading = false;   // <-- New state for create form submission

    // --- Pagination ---
    currentPage = 1;
    itemsPerPage = 10;
    totalItems = 0;

    // --- Filtering ---
    filters: InspectionToolFilter = { // Added filters object
        name: null,
        enabled: 'true', // Default based on original getInspectionToolList call
        sortField: 'title', // Default based on original getInspectionToolList call
        sortDirection: 'ASC' // Default based on original getInspectionToolList call
    };
    availableSortFields = [ // Added sort fields for dropdown
        { value: 'id', label: 'ID' },
        { value: 'title', label: 'Tool Name' },
        { value: 'enabled', label: 'Enabled Status' },
        { value: 'createdAt', label: 'Created Date' }
    ];

    // --- Editing ---
    selectedInspectionTool: InspectionTool | null = null; // <-- Object to hold data for editing

    // --- Creating ---
    newInspectionToolData: NewInspectionToolData = { // <-- Object to hold new tool form data
        title: '',
        enabled: true // Default new tools to active
    };


    constructor(
        readonly inspectionToolService: InspectionToolService,
        readonly updateService: UpdateService,
        // Optional: Inject ToastrService
        // private toastr: ToastrService
    ) { }

    ngOnInit() {
        // Changed method name and passed current page
        this.loadInspectionTools(this.currentPage);
    }

    // Helper to get current list data
    getCurrentListData(): InspectionTool[] | undefined {
        return this.inspectionToolList;
    }

    // Fetch ALL inspection tools matching current filters (no pagination)
    async fetchAllFilteredInspectionTools(): Promise<InspectionTool[] | null> {
        this.listLoading = true; // Indicate loading
        const filterParams: string[] = [];
        if (this.filters.name) { filterParams.push(`title||$contL||${this.filters.name}`); }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
             filterParams.push(`enabled||$eq||${this.filters.enabled}`);
        }

        const data = {
            limit: 10000, // Large limit for "all"
            sort: `${this.filters.sortField || 'title'},${this.filters.sortDirection || 'ASC'}`, // Match default sort
            filter: filterParams
        };

        try {
            const params = createAxiosConfig(data);
            console.log('API Request Params (Tools Download - All Data):', JSON.stringify(params, null, 2));
            const response = await this.inspectionToolService.getInspectionTool(params); // Use existing service method
            return response ?? [];
        } catch (error: any) {
            console.error("Error fetching all tools for download:", error);
            this.toast?.showErrorToast(error?.response?.data?.message || "Failed to retrieve full data for download.");
            return null;
        } finally {
             this.listLoading = false; // Reset loading indicator
        }
    }

    // Main download function
    async downloadExcel(type: 'current' | 'all') {
        if (this.isDownloadingExcel || this.listLoading) return; // Prevent concurrent actions

        this.isDownloadingExcel = true;
        this.downloadType = type;
        this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} inspection tools...`);

        let dataToExport: InspectionTool[] | null = null;

        try {
            // 1. Get Data
            if (type === 'all') {
                dataToExport = await this.fetchAllFilteredInspectionTools();
            } else { // 'current'
                dataToExport = this.getCurrentListData() ?? null;
                if (dataToExport === undefined) { dataToExport = null; }
            }

            // 2. Check data
            if (dataToExport === null) { this.toast?.showErrorToast("Failed to retrieve data for download."); return; }
            if (!dataToExport || dataToExport.length === 0) { this.toast?.showErrorToast(`No inspection tools available to download.`); return; }

            console.log(`Fetched ${dataToExport.length} tools for Excel export (${type}).`);

            // 3. Transform data
            const dataForExcel = dataToExport.map(tool => ({
                'Tool ID': tool.id,
                'Tool Name': tool.title,
                'Status': tool.enabled ? 'Active' : 'Inactive',
                'Created At': tool.createdAt ? new Date(tool.createdAt).toLocaleString() : 'N/A',
                'Updated At': tool.updatedAt ? new Date(tool.updatedAt).toLocaleString() : 'N/A',
            }));

            // 4. Create Worksheet and Workbook
            const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
            const wb: XLSX.WorkBook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'InspectionTools'); // Sheet name

            // 5. Generate File Name
            const dateStr = new Date().toISOString().slice(0, 10);
            const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
            const fileName = `InspectionTools_${typeStr}_${dateStr}.xlsx`;

            // 6. Trigger Download
            XLSX.writeFile(wb, fileName);
            this.toast?.showSuccessToast(`Excel file download started (${type}).`);

        } catch (error) {
            console.error(`Error generating Excel file (${type}):`, error);
            this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
        } finally {
            this.isDownloadingExcel = false;
            this.downloadType = null;
        }
    }

    // --- Filter Modal Methods ---
    openFilterModal(): void { this.isFilterModalOpen = true; }
    closeFilterModal(): void { this.isFilterModalOpen = false; } // Renamed from closeModal
    applyFilters(): void {
        // Check for validation errors
        if (this.filterForm?.invalid) {
            Object.values(this.filterForm.controls).forEach(control => control.markAsTouched());
            console.warn('Filter form submitted while invalid.');
            this.toast?.showErrorToast('Please correct the validation errors before applying filters.');
            return;
        }

        this.currentPage = 1; // Reset page on new filter apply
        this.loadInspectionTools(this.currentPage);
        this.closeFilterModal(); // Close modal after applying
    }
    resetFilters(): void {
        // Reset filter object to defaults
        this.filters = {
            name: null,
            enabled: 'true',
            sortField: 'title',
            sortDirection: 'ASC'
        };
        this.currentPage = 1; // Reset page
        this.loadInspectionTools(this.currentPage);
        // Optionally close modal: this.closeFilterModal();
    }


    // --- *** NEW: Edit Modal Methods *** ---
    /** Opens the edit inspection tool offcanvas modal. */
    openEditModal(tool: InspectionTool): void {
        this.selectedInspectionTool = { ...tool }; // Create a copy
        this.isEditModalOpen = true;
        this.editLoading = false;
    }

    /** Closes the edit inspection tool offcanvas modal. */
    closeEditModal(): void {
        this.isEditModalOpen = false;
        this.selectedInspectionTool = null;
    }

    /** Handles the submission of the edit inspection tool form. */
    async submitEditForm(): Promise<void> {
        if (!this.selectedInspectionTool || this.selectedInspectionTool.id == null) {
            console.error("Cannot save, selected tool is null or has no ID.");
            this.toast?.showErrorToast('Cannot save, no tool selected.');
            return;
        }

        // Check for validation errors
        if (this.editForm?.invalid) {
            Object.values(this.editForm.controls).forEach(control => control.markAsTouched());
            console.warn('Edit form submitted while invalid.');
            this.toast?.showErrorToast('Please correct the validation errors before submitting.');
            return;
        }

        this.editLoading = true;
        const updatePayload = {
            tableName: 'inspection-tool-master', // Critical: Ensure this matches your backend/service expectation
            id: this.selectedInspectionTool.id,
            data: {
                title: this.selectedInspectionTool.title,
                enabled: this.selectedInspectionTool.enabled
                // Add other editable fields if necessary
            }
        };

        try {
            await this.update(updatePayload); // Use the generic update method
            // this.toastr.success('Inspection tool updated successfully!', 'Saved');

            // Update list locally (optional)
            const index = this.inspectionToolList.findIndex(t => t.id === this.selectedInspectionTool?.id);
            if (index !== -1 && this.selectedInspectionTool) {
                this.inspectionToolList[index] = { ...this.selectedInspectionTool };
            }

            this.closeEditModal();
            // Consider reloading if necessary: this.loadInspectionTools(this.currentPage);

        } catch (error) {
            console.error("Error submitting inspection tool update:", error);
            // this.toastr.error('Failed to update inspection tool.', 'Error');
        } finally {
            this.editLoading = false;
        }
    }


    // --- *** NEW: Create Modal Methods *** ---

    /** Opens the create inspection tool offcanvas modal and resets the form data. */
    openCreateModal(): void {
        this.newInspectionToolData = {
            title: '',
            enabled: true
        };
        this.createForm?.resetForm({ enabled: true }); // Reset form state with default
        this.isCreateModalOpen = true;
        this.createLoading = false;
    }

    /** Closes the create inspection tool offcanvas modal. */
    closeCreateModal(): void {
        this.isCreateModalOpen = false;
    }

    /** Handles the submission of the create inspection tool form. */
    async submitCreateForm(): Promise<void> {
        if (this.createForm?.invalid) {
            Object.values(this.createForm.controls).forEach(control => control.markAsTouched());
            console.warn('Create form submitted while invalid.');
            this.toast?.showErrorToast('Please correct the validation errors before submitting.');
            return;
        }

        this.createLoading = true;
        console.log('Submitting new inspection tool:', this.newInspectionToolData);

        try {
            // *** ASSUMPTION: inspectionToolService has a createInspectionTool method ***
            const createdTool = await this.inspectionToolService.createInspectionTool(this.newInspectionToolData);
            console.log('Inspection tool created successfully:', createdTool);

            // this.toastr.success(`Inspection tool "${createdTool.title}" created successfully!`, 'Created');

            this.closeCreateModal();
            // Reload list, go to page 1
            this.currentPage = 1;
            this.loadInspectionTools(this.currentPage);

        } catch (error) {
            console.error("Error creating inspection tool:", error);
            // this.toastr.error('Failed to create inspection tool. Please try again.', 'Creation Error');
        } finally {
            this.createLoading = false;
        }
    }


    // --- Data Loading & Update Methods ---

    /** Fetches inspection tools from the backend. (Refactored from getInspectionToolList) */
    async loadInspectionTools(page: number): Promise<void> {
        this.listLoading = true; // Set loading true
        // this.inspectionToolList = []; // Clear only after loading starts

        // Build filter params dynamically
        const filterParams: string[] = [];
        if (this.filters.name) {
            filterParams.push(`title||$contL||${this.filters.name}`);
        }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
            filterParams.push(`enabled||$eq||${this.filters.enabled}`);
        }

        const requestData = {
            page: page,
            limit: this.itemsPerPage,
            sort: `${this.filters.sortField || 'title'},${this.filters.sortDirection || 'ASC'}`,
            filter: filterParams // Use dynamic filters
        };

        try {
            const params = createAxiosConfig(requestData); // Use utility function
            // Ensure service method returns { data: InspectionTool[], total: number }
            const response = await this.inspectionToolService.getInspectionTool(params); // Pass params object
            this.inspectionToolList = response?.data ?? []; // Use response.data
            this.totalItems = response?.total ?? 0; // Use response.total
        } catch (error) {
            console.error("Error fetching inspection tools:", error);
            this.inspectionToolList = []; // Clear list on error
            this.totalItems = 0;
            // this.toastr.error('Failed to load inspection tools.', 'Error');
        } finally {
            this.listLoading = false; // Set loading false
        }
    }

    /** Handles page changes. */
    onPageChange(page: number): void {
        if (page !== this.currentPage) {
            this.currentPage = page;
            this.loadInspectionTools(this.currentPage); // Call refactored method
        }
    }

    /** Handles the toggle switch change event in the table row. */
    async onSwitchToggle(isEnabled: boolean, tool: InspectionTool): Promise<void> {
        const originalState = tool.enabled;
        tool.enabled = isEnabled; // Optimistic update

        const updatePayload = {
            tableName: 'inspection-tool-master', // Use correct table name
            id: tool.id,
            data: {
                enabled: isEnabled
            }
        };
        console.log(`Attempting to update tool ${tool.id} enabled status to: ${isEnabled}`);

        try {
            await this.update(updatePayload);
            console.log(`Successfully updated tool ${tool.id} enabled status.`);
            // this.toastr.success('Tool status updated.', 'Success');
        } catch (error) {
            console.error(`Error updating enabled status for tool ${tool.id}:`, error);
            // this.toastr.error('Failed to update tool status.', 'Update Error');
            // Revert UI change on error
            tool.enabled = originalState;
            // Force update detection if needed
            const index = this.inspectionToolList.findIndex(t => t.id === tool.id);
            if (index !== -1) {
                this.inspectionToolList[index] = { ...this.inspectionToolList[index], enabled: originalState };
            }
        }
    }

    // Generic update via UpdateService (used by toggle and edit form)
    async update(data: { tableName: string, id: number, data: any }): Promise<void> {
        try {
            await this.updateService.update(data);
        } catch (error) {
            console.error("Update service call failed:", error);
            throw error; // Re-throw
        }
    }

}