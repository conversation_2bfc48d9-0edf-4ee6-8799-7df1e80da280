<app-toast-message></app-toast-message>
<div class="card" id="activated-qrcode">
    <div class="card-header d-flex align-items-center justify-content-between">
        <div>
            <h6 class="mb-0">Inspection Tools</h6>
        </div>
        <div class="d-flex align-items-center">
                <!-- *** REPLACE the existing simple Download button with this Dropdown *** -->
                <div ngbDropdown class="d-inline-block me-2">
                    <button type="button" class="btn btn-sm adani-btn dropdown-toggle" id="downloadToolExcelDropdown"
                        ngbDropdownToggle [disabled]="isDownloadingExcel || listLoading">
                        <span *ngIf="!isDownloadingExcel">
                            <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
                        </span>
                        <span *ngIf="isDownloadingExcel">
                            <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                            Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '')
                            }}...
                        </span>
                    </button>
                    <ul ngbDropdownMenu aria-labelledby="downloadToolExcelDropdown">
                        <li>
                            <button ngbDropdownItem (click)="downloadExcel('current')"
                                [disabled]="isDownloadingExcel || listLoading || (inspectionToolList.length === 0)">
                                <i class="bi bi-download me-1"></i> Download Current Page ({{ inspectionToolList.length
                                }})
                            </button>
                        </li>
                        <li>
                            <button ngbDropdownItem (click)="downloadExcel('all')"
                                [disabled]="isDownloadingExcel || listLoading || totalItems === 0">
                                <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
                            </button>
                        </li>
                    </ul>
                </div>
                <!-- *** END REPLACEMENT *** -->
                <!-- *** NEW: Create Button *** -->
                <button class="btn-sm adani-btn ms-2" (click)="openCreateModal()" title="Create New Inspection Tool">
                    <i class="bi bi-plus-circle"></i> Create New
                </button>
                <img src="../../../assets/svg/filter.svg" class="filter-button ms-3" (click)="openFilterModal()"
                    alt="Filter" style="width: 35px;" />
            </div>
        </div>
    <div class="card-body">
        <div class="table-container">
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-header">
                        <tr class="text-center">
                            <th scope="col">Id</th>
                            <th scope="col">Enabled/Disabled</th>
                            <th scope="col">Inspection Tool</th>
                            <th scope="col">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Loading Indicator -->
                        <tr *ngIf="listLoading">
                            <td colspan="4" class="text-center">
                                <div class="spinner-border spinner-border-sm" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                Loading Inspection Tools...
                            </td>
                        </tr>
                        <!-- No Data Message -->
                        <tr *ngIf="!listLoading && inspectionToolList.length === 0">
                            <td colspan="4" class="text-center">No inspection tools found.</td>
                        </tr>
                        <!-- Data Rows -->
                        <tr *ngFor="let item of inspectionToolList">
                            <td class="text-center">{{item.id}}</td>
                            <td>
                                <app-switch [(checked)]="item.enabled" [requireConfirmation]="true"
                                    (checkedChange)="onSwitchToggle($event, item)" onLabel="Active" offLabel="Inactive">
                                </app-switch>
                            </td>
                            <td>{{item.title}}</td>
                            <td class="actions text-center"> <!-- Added text-center -->
                                <!-- *** UPDATED: Edit Button Click Handler *** -->
                                <button class="adani-btn" (click)="openEditModal(item)" title="Edit Inspection Tool">
                                    <i class="bi bi-pencil edit"></i>
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div class="card-footer text-muted text-center">
        <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
            (pageChange)="onPageChange($event)"></app-pagination>
    </div>
</div>

<!-- Filter Offcanvas (Updated) -->
<app-offcanvas [title]="'Filter Inspection Tools'" *ngIf="isFilterModalOpen" (onClickCross)="closeFilterModal()">
    <div class="filter-container p-3">
        <form #filterForm="ngForm" (ngSubmit)="applyFilters()">
            <div class="row g-3">

                <div class="col-12">
                    <label class="form-label" for="filterToolName">Inspection Tool Name</label>
                    <input type="text" id="filterToolName" class="form-control" placeholder="Search by Tool Name"
                        [(ngModel)]="filters.name" name="name" maxlength="30" pattern="^[a-zA-Z\s]*$" #filterNameInput="ngModel"
                        [ngClass]="{'is-invalid': filterNameInput.invalid && (filterNameInput.dirty || filterNameInput.touched)}">
                    <!-- Character count display - only visible when there's text -->
                    <small *ngIf="filters.name" class="text-muted d-block text-end mt-1">
                        {{ filters.name.length }}/30 characters
                    </small>
                    <div *ngIf="filterNameInput.invalid && (filterNameInput.dirty || filterNameInput.touched)" class="text-danger small mt-1">
                        <div *ngIf="filterNameInput.errors?.['pattern']">Inspection tool name should contain only alphabets.</div>
                    </div>
                </div>

                <div class="col-12">
                    <label class="form-label" for="filterEnabledTool">Enabled Status</label>
                    <select id="filterEnabledTool" class="form-select" [(ngModel)]="filters.enabled" name="enabled">
                        <!-- Bind to filters.enabled -->
                        <option [ngValue]="null">Any</option>
                        <option value="true">Yes</option>
                        <option value="false">No</option>
                    </select>
                </div>

                <div class="col-12">
                    <label class="form-label" for="filterSortByTool">Sort By</label>
                    <select id="filterSortByTool" class="form-select" [(ngModel)]="filters.sortField" name="sortField">
                        <!-- Bind to filters.sortField -->
                        <option [ngValue]="null">Default Sort (Name ASC)</option>
                        <option *ngFor="let field of availableSortFields" [value]="field.value">{{ field.label }}
                        </option>
                    </select>
                    <label class="form-label mt-2" for="filterSortDirTool">Sort Direction</label>
                    <select id="filterSortDirTool" class="form-select" [(ngModel)]="filters.sortDirection"
                        name="sortDirection"> <!-- Bind to filters.sortDirection -->
                        <option value="ASC">Ascending</option>
                        <option value="DESC">Descending</option>
                    </select>
                </div>

                <div class="col-12 mt-4 d-grid gap-2">
                    <button type="submit" class="btn adani-btn">
                        <i class="bi bi-search me-1"></i> Search
                    </button>
                    <button type="button" class="btn btn-secondary" (click)="resetFilters()">
                        <i class="bi bi-arrow-clockwise me-1"></i> Reset Filters
                    </button>
                </div>
            </div>
        </form>
    </div>
</app-offcanvas>


<!-- ********** NEW: Edit Offcanvas ********** -->
<app-offcanvas [title]="'Edit Inspection Tool'" *ngIf="isEditModalOpen" (onClickCross)="closeEditModal()">
    <div class="edit-container p-3">
        <form *ngIf="selectedInspectionTool" #editForm="ngForm" (ngSubmit)="submitEditForm()">
            <div class="row g-3">

                <!-- Tool ID (Readonly) -->
                <div class="col-12">
                    <label class="form-label" for="editToolId">Tool ID</label>
                    <input type="text" id="editToolId" class="form-control" [value]="selectedInspectionTool.id"
                        name="id" readonly disabled>
                </div>

                <!-- Tool Name (Editable) -->
                <div class="col-12">
                    <label class="form-label" for="editToolName">Inspection Tool Name</label>
                    <input type="text" id="editToolName" class="form-control" placeholder="Enter Tool Name"
                        [(ngModel)]="selectedInspectionTool.title" name="title" required #titleInput="ngModel"
                        maxlength="30" pattern="^[a-zA-Z\s]*$"
                        [ngClass]="{'is-invalid': titleInput.invalid && (titleInput.dirty || titleInput.touched)}">
                    <!-- Character count display - only visible when there's text -->
                    <small *ngIf="selectedInspectionTool.title" class="text-muted d-block text-end mt-1">
                        {{ selectedInspectionTool.title.length }}/30 characters
                    </small>
                    <div *ngIf="titleInput.invalid && (titleInput.dirty || titleInput.touched)"
                        class="text-danger small mt-1">
                        <div *ngIf="titleInput.errors?.['required']">Inspection tool name is required.</div>
                        <div *ngIf="titleInput.errors?.['pattern']">Inspection tool name should contain only alphabets.</div>
                        <div *ngIf="titleInput.errors?.['maxlength']">Inspection tool name cannot exceed 30 characters.</div>
                    </div>
                </div>

                <!-- Enabled Status -->
                <div class="col-12">
                    <label class="form-label d-block mb-2">Status</label>
                    <app-switch [(checked)]="selectedInspectionTool.enabled" name="enabled" onLabel="Active"
                        offLabel="Inactive">
                    </app-switch>
                </div>

                <!-- Action Buttons -->
                <div class="col-12 mt-4 d-flex justify-content-end gap-2">
                    <button type="button" class="btn btn-secondary" (click)="closeEditModal()">
                        Cancel
                    </button>
                    <button type="submit" class="btn adani-btn" [disabled]="editForm.invalid || editLoading">
                        <span *ngIf="!editLoading"><i class="bi bi-save me-1"></i> Save Changes</span>
                        <span *ngIf="editLoading" class="spinner-border spinner-border-sm" role="status"
                            aria-hidden="true"></span>
                        <span *ngIf="editLoading"> Saving...</span>
                    </button>
                </div>
            </div>
        </form>
        <div *ngIf="!selectedInspectionTool && isEditModalOpen" class="text-center p-5">
            <div class="spinner-border spinner-border-sm" role="status">
                <span class="visually-hidden">Loading form...</span>
            </div>
        </div>
    </div>
</app-offcanvas>
<!-- ********** END: Edit Offcanvas ********** -->

<!-- ********** NEW: Create Offcanvas ********** -->
<app-offcanvas [title]="'Create New Inspection Tool'" *ngIf="isCreateModalOpen" (onClickCross)="closeCreateModal()">
    <div class="create-container p-3">
        <form #createForm="ngForm" (ngSubmit)="submitCreateForm()">
            <div class="row g-3">

                <!-- Tool Name (Required) -->
                <div class="col-12">
                    <label class="form-label" for="createToolName">Inspection Tool Name</label>
                    <input type="text" id="createToolName" class="form-control" placeholder="Enter New Tool Name"
                        [(ngModel)]="newInspectionToolData.title" name="title" required #createTitleInput="ngModel"
                        maxlength="30" pattern="^[a-zA-Z\s]*$"
                        [ngClass]="{'is-invalid': createTitleInput.invalid && (createTitleInput.dirty || createTitleInput.touched)}">
                    <!-- Character count display - only visible when there's text -->
                    <small *ngIf="newInspectionToolData.title" class="text-muted d-block text-end mt-1">
                        {{ newInspectionToolData.title.length }}/30 characters
                    </small>
                    <div *ngIf="createTitleInput.invalid && (createTitleInput.dirty || createTitleInput.touched)"
                        class="text-danger small mt-1">
                        <div *ngIf="createTitleInput.errors?.['required']">Inspection tool name is required.</div>
                        <div *ngIf="createTitleInput.errors?.['pattern']">Inspection tool name should contain only alphabets.</div>
                        <div *ngIf="createTitleInput.errors?.['maxlength']">Inspection tool name cannot exceed 30 characters.</div>
                    </div>
                </div>

                <!-- Enabled Status (Default to Active/true) -->
                <div class="col-12">
                    <label class="form-label d-block mb-2">Status</label>
                    <app-switch [(checked)]="newInspectionToolData.enabled" name="enabled" onLabel="Active"
                        offLabel="Inactive">
                    </app-switch>
                </div>

                <!-- Action Buttons -->
                <div class="col-12 mt-4 d-flex justify-content-end gap-2">
                    <button type="button" class="btn btn-secondary" (click)="closeCreateModal()">
                        Cancel
                    </button>
                    <button type="submit" class="btn adani-btn" [disabled]="createForm.invalid || createLoading">
                        <span *ngIf="!createLoading"><i class="bi bi-plus-circle-fill me-1"></i> Create Tool</span>
                        <span *ngIf="createLoading" class="spinner-border spinner-border-sm" role="status"
                            aria-hidden="true"></span>
                        <span *ngIf="createLoading"> Creating...</span>
                    </button>
                </div>
            </div>
        </form>
    </div>
</app-offcanvas>
<!-- ********** END: Create Offcanvas ********** -->
