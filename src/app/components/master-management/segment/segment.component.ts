import { Component, OnInit, ViewChild } from '@angular/core'; // Added ViewChild
import { FormsModule, NgForm } from '@angular/forms'; // Added NgForm
import { SegmentService } from '../../../services/master-management/segment/segment.service';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { CommonModule } from '@angular/common';
import { SwitchComponent } from "../../../shared/switch/switch.component";
import { PaginationComponent } from "../../../shared/pagination/pagination.component";
import { OffcanvasComponent } from "../../../shared/offcanvas/offcanvas.component";
import { UpdateService } from '../../../services/update/update.service';
// Optional: Import ToastrService or similar
// import { ToastrService } from 'ngx-toastr';
import { ToastMessageComponent } from '../../../shared/toast-message/toast-message.component'; // Adjust path if needed
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap'; // For dropdown button
import * as XLSX from 'xlsx'; // For Excel generation


// Interface for filter structure
interface SegmentFilter {
    name?: string | null;
    enabled?: string | null;
    sortField?: string | null;
    sortDirection?: 'ASC' | 'DESC';
}

// *** NEW: Interface for Segment Data ***
export interface Segment {
    id: number;
    title: string;
    enabled: boolean;
    createdAt?: string; // Optional
    updatedAt?: string; // Optional
}

// *** NEW: Interface for Create Form Data ***
interface NewSegmentData {
    title: string;
    enabled: boolean;
}


@Component({
    selector: 'app-segment',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        SwitchComponent,
        PaginationComponent,
        OffcanvasComponent,
        NgbDropdownModule,
        ToastMessageComponent
    ],
    templateUrl: './segment.component.html',
    styleUrl: './segment.component.scss'
})
export class SegmentComponent implements OnInit {
    // Access the form template variable
    @ViewChild('createForm') createForm?: NgForm;
    @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent;

    // --- Modal States ---
    isFilterModalOpen = false; // Renamed from isEditUserModalOpen
    isEditModalOpen = false;   // <-- New state for edit modal
    isCreateModalOpen = false; // <-- New state for create modal

    // --- Data & Loading States ---
    segmentList: Segment[] = []; // Use Segment interface
    listLoading = false;
    editLoading = false;     // <-- New state for edit form submission
    createLoading = false;   // <-- New state for create form submission
    isDownloadingExcel = false;
    downloadType: 'current' | 'all' | null = null;

    // --- Pagination ---
    currentPage = 1;
    itemsPerPage = 10;
    totalItems = 0;

    // --- Filtering ---
    filters: SegmentFilter = {
        name: null,
        enabled: 'true',
        sortField: 'title',
        sortDirection: 'ASC'
    };
    availableSortFields = [
        { value: 'id', label: 'ID' },
        { value: 'title', label: 'Segment Name' },
        { value: 'enabled', label: 'Enabled Status' },
        { value: 'createdAt', label: 'Created Date' }
    ];

    // --- Editing ---
    selectedSegment: Segment | null = null; // <-- Object to hold data for editing

    // --- Creating ---
    newSegmentData: NewSegmentData = { // <-- Object to hold new segment form data
        title: '',
        enabled: true // Default new segments to active
    };

    constructor(
        readonly segmentService: SegmentService,
        readonly updateService: UpdateService,
        // Optional: Inject ToastrService
        // private toastr: ToastrService
    ) { }

    ngOnInit(): void {
        this.loadSegments(this.currentPage);
    }

    // Helper to get current list data
    getCurrentListData(): Segment[] | undefined {
        return this.segmentList;
    }

    // Fetch ALL segments matching current filters (no pagination)
    async fetchAllFilteredSegments(): Promise<Segment[] | null> {
        this.listLoading = true; // Indicate loading
        const filterParams: string[] = [];
        if (this.filters.name) { filterParams.push(`title||$contL||${this.filters.name}`); }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
             filterParams.push(`enabled||$eq||${this.filters.enabled}`);
        }

        const data = {
            limit: 10000, // Large limit for "all"
            sort: `${this.filters.sortField || 'title'},${this.filters.sortDirection || 'ASC'}`, // Match default sort
            filter: filterParams
        };

        try {
            const params = createAxiosConfig(data);
            console.log('API Request Params (Segments Download - All Data):', JSON.stringify(params, null, 2));
            const response = await this.segmentService.getSegment(params); // Use existing service method
            return response ?? [];
        } catch (error: any) {
            console.error("Error fetching all segments for download:", error);
            this.toast?.showErrorToast(error?.response?.data?.message || "Failed to retrieve full data for download.");
            return null;
        } finally {
             this.listLoading = false; // Reset loading indicator
        }
    }

    // Main download function
    async downloadExcel(type: 'current' | 'all') {
        if (this.isDownloadingExcel || this.listLoading) return; // Prevent concurrent actions

        this.isDownloadingExcel = true;
        this.downloadType = type;
        this.toast?.showSuccessToast(`Preparing download for ${type === 'current' ? 'current page' : 'all filtered'} segments...`);

        let dataToExport: Segment[] | null = null;

        try {
            // 1. Get Data
            if (type === 'all') {
                dataToExport = await this.fetchAllFilteredSegments();
            } else { // 'current'
                dataToExport = this.getCurrentListData() ?? null;
                if (dataToExport === undefined) { dataToExport = null; }
            }

            // 2. Check data
            if (dataToExport === null) { this.toast?.showErrorToast("Failed to retrieve data for download."); return; }
            if (!dataToExport || dataToExport.length === 0) { this.toast?.showErrorToast(`No segments available to download.`); return; }

            console.log(`Fetched ${dataToExport.length} segments for Excel export (${type}).`);

            // 3. Transform data
            const dataForExcel = dataToExport.map(seg => ({
                'Segment ID': seg.id,
                'Segment Name': seg.title,
                'Status': seg.enabled ? 'Active' : 'Inactive',
                'Created At': seg.createdAt ? new Date(seg.createdAt).toLocaleString() : 'N/A',
                'Updated At': seg.updatedAt ? new Date(seg.updatedAt).toLocaleString() : 'N/A',
            }));

            // 4. Create Worksheet and Workbook
            const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(dataForExcel);
            const wb: XLSX.WorkBook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'Segments'); // Sheet name

            // 5. Generate File Name
            const dateStr = new Date().toISOString().slice(0, 10);
            const typeStr = type === 'current' ? `Page${this.currentPage}` : 'All';
            const fileName = `Segments_${typeStr}_${dateStr}.xlsx`;

            // 6. Trigger Download
            XLSX.writeFile(wb, fileName);
            this.toast?.showSuccessToast(`Excel file download started (${type}).`);

        } catch (error) {
            console.error(`Error generating Excel file (${type}):`, error);
            this.toast?.showErrorToast(`An error occurred while generating the Excel file (${type}).`);
        } finally {
            this.isDownloadingExcel = false;
            this.downloadType = null;
        }
    }

    // --- Filter Modal Methods ---
    openFilterModal(): void { this.isFilterModalOpen = true; }
    closeFilterModal(): void { this.isFilterModalOpen = false; } // Renamed from closeModal

    @ViewChild('filterForm') filterForm!: NgForm;

    applyFilters(): void {
        // Check if form is valid before applying filters
        if (this.filterForm && this.filterForm.invalid) {
            this.toast?.showErrorToast("Please correct the validation errors before applying filters.");
            return;
        }

        // Trim string values to prevent whitespace-only searches
        if (this.filters.name) {
            this.filters.name = this.filters.name.trim();
        }

        this.currentPage = 1;
        this.loadSegments(this.currentPage);
        this.closeFilterModal();
    }

    resetFilters(): void {
        this.filters = {
            name: null,
            enabled: 'true',
            sortField: 'title',
            sortDirection: 'ASC'
        };
        this.currentPage = 1;
        this.loadSegments(this.currentPage);
        // Optionally close modal: this.closeFilterModal();
    }

    // --- *** NEW: Edit Modal Methods *** ---
    /** Opens the edit segment offcanvas modal. */
    openEditModal(segment: Segment): void {
        this.selectedSegment = { ...segment }; // Create a copy
        this.isEditModalOpen = true;
        this.editLoading = false;
    }

    /** Closes the edit segment offcanvas modal. */
    closeEditModal(): void {
        this.isEditModalOpen = false;
        this.selectedSegment = null;
    }

    @ViewChild('editForm') editForm!: NgForm;

    /** Handles the submission of the edit segment form. */
    async submitEditForm(): Promise<void> {
        if (!this.selectedSegment || this.selectedSegment.id == null) {
            console.error("Cannot save, selected segment is null or has no ID.");
            this.toast?.showErrorToast('Cannot save, no segment selected.');
            return;
        }

        // Check form validity
        if (this.editForm && this.editForm.invalid) {
            this.toast?.showErrorToast("Please correct the validation errors before saving.");
            // Mark fields as touched to show validation errors
            Object.values(this.editForm.controls).forEach(control => {
                control.markAsTouched();
            });
            return;
        }

        // Trim the title to remove any leading/trailing whitespace
        if (this.selectedSegment.title) {
            this.selectedSegment.title = this.selectedSegment.title.trim();
        }

        this.editLoading = true;
        const updatePayload = {
            tableName: 'segment', // Critical: Ensure this matches your backend/service expectation
            id: this.selectedSegment.id,
            data: {
                title: this.selectedSegment.title,
                enabled: this.selectedSegment.enabled
                // Add other editable fields if necessary
            }
        };

        try {
            await this.update(updatePayload); // Use the generic update method
            this.toast?.showSuccessToast('Segment updated successfully!');

            // Update list locally (optional)
            const index = this.segmentList.findIndex(s => s.id === this.selectedSegment?.id);
            if (index !== -1 && this.selectedSegment) {
                 this.segmentList[index] = { ...this.selectedSegment };
            }

            this.closeEditModal();
            // Consider reloading if necessary: this.loadSegments(this.currentPage);

        } catch (error) {
            console.error("Error submitting segment update:", error);
            this.toast?.showErrorToast('Failed to update segment.');
        } finally {
            this.editLoading = false;
        }
    }


    // --- *** NEW: Create Modal Methods *** ---

    /** Opens the create segment offcanvas modal and resets the form data. */
    openCreateModal(): void {
        this.newSegmentData = {
            title: '',
            enabled: true
        };
        this.createForm?.resetForm({ enabled: true }); // Reset form state with default
        this.isCreateModalOpen = true;
        this.createLoading = false;
    }

    /** Closes the create segment offcanvas modal. */
    closeCreateModal(): void {
        this.isCreateModalOpen = false;
    }

    /** Handles the submission of the create segment form. */
    async submitCreateForm(): Promise<void> {
        if (this.createForm?.invalid) {
             Object.values(this.createForm.controls).forEach(control => control.markAsTouched());
             this.toast?.showErrorToast("Please correct the validation errors before creating.");
             console.warn('Create form submitted while invalid.');
             return;
        }

        // Trim the title to remove any leading/trailing whitespace
        if (this.newSegmentData.title) {
            this.newSegmentData.title = this.newSegmentData.title.trim();
        }

        this.createLoading = true;
        console.log('Submitting new segment:', this.newSegmentData);

        try {
            // *** ASSUMPTION: segmentService has a createSegment method ***
            const createdSegment = await this.segmentService.createSegment(this.newSegmentData);
            console.log('Segment created successfully:', createdSegment);

            this.toast?.showSuccessToast(`Segment "${createdSegment.title}" created successfully!`);

            this.closeCreateModal();
            // Reload list, go to page 1
            this.currentPage = 1;
            this.loadSegments(this.currentPage);

        } catch (error) {
            console.error("Error creating segment:", error);
            this.toast?.showErrorToast('Failed to create segment. Please try again.');
        } finally {
            this.createLoading = false;
        }
    }


    // --- Data Loading & Update Methods ---

    /** Fetches segments from the backend. */
    async loadSegments(page: number): Promise<void> {
        this.listLoading = true;
        // this.segmentList = []; // Clear only after loading starts

        const filterParams: string[] = [];
        if (this.filters.name) {
            filterParams.push(`title||$contL||${this.filters.name}`);
        }
        if (this.filters.enabled !== null && this.filters.enabled !== undefined && this.filters.enabled !== '') {
            filterParams.push(`enabled||$eq||${this.filters.enabled}`);
        }

        const requestData = {
            page: page,
            limit: this.itemsPerPage,
            sort: `${this.filters.sortField || 'title'},${this.filters.sortDirection || 'ASC'}`,
            filter: filterParams
        };

        try {
            const params = createAxiosConfig(requestData);
            // Ensure service method returns { data: Segment[], total: number }
            const response = await this.segmentService.getSegment(params);
            this.segmentList = response?.data ?? [];
            this.totalItems = response?.total ?? 0;
        } catch (error) {
            console.error("Error fetching segments:", error);
            this.segmentList = [];
            this.totalItems = 0;
            // this.toastr.error('Failed to load segments.', 'Error');
        } finally {
            this.listLoading = false;
        }
    }

    /** Handles page changes. */
    onPageChange(page: number): void {
        if (page !== this.currentPage) {
            this.currentPage = page;
            this.loadSegments(this.currentPage);
        }
    }

    /** Handles the toggle switch change event in the table row. */
    async onSwitchToggle(isEnabled: boolean, segment: Segment): Promise<void> {
        const originalState = segment.enabled;
        segment.enabled = isEnabled; // Optimistic update

        const updatePayload = {
            tableName: 'segment', // Use correct table name
            id: segment.id,
            data: {
                enabled: isEnabled
            }
        };
        console.log(`Attempting to update segment ${segment.id} enabled status to: ${isEnabled}`);

        try {
            await this.update(updatePayload);
            console.log(`Successfully updated segment ${segment.id} enabled status.`);
            // this.toastr.success('Segment status updated.', 'Success');
        } catch (error) {
            console.error(`Error updating enabled status for segment ${segment.id}:`, error);
            // this.toastr.error('Failed to update segment status.', 'Update Error');
            // Revert UI change on error
            segment.enabled = originalState;
            // Force update detection if needed
            const index = this.segmentList.findIndex(s => s.id === segment.id);
            if (index !== -1) {
                 this.segmentList[index] = {...this.segmentList[index], enabled: originalState };
            }
        }
    }

    // Generic update via UpdateService (used by toggle and edit form)
    async update(data: { tableName: string, id: number, data: any }): Promise<void> {
        try {
            await this.updateService.update(data);
        } catch (error) {
            console.error("Update service call failed:", error);
            throw error; // Re-throw
        }
    }

    // --- Removed Placeholder/Unused Methods ---
    // Removed handleCreate, handleUpdate, toggleEnabled, handleDelete, sortBy, getSortClass
}