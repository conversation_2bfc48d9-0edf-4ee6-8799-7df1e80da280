<app-toast-message></app-toast-message>
<div class="card" id="activated-qrcode">
  <div class="card-header d-flex align-items-center justify-content-between">
    <div>
      <h6 class="mb-0">Designations</h6>
    </div>
    <div class="d-flex align-items-center">
        <!-- *** REPLACE the existing simple Download button with this Dropdown *** -->
        <div ngbDropdown class="d-inline-block me-2">
            <button type="button" class="btn btn-sm adani-btn dropdown-toggle" id="downloadDesigExcelDropdown" ngbDropdownToggle
                [disabled]="isDownloadingExcel || listLoading">
                <span *ngIf="!isDownloadingExcel">
                    <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
                </span>
                <span *ngIf="isDownloadingExcel">
                    <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                    Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '') }}...
                </span>
            </button>
            <ul ngbDropdownMenu aria-labelledby="downloadDesigExcelDropdown">
                <li>
                    <button ngbDropdownItem (click)="downloadExcel('current')" [disabled]="isDownloadingExcel || listLoading || (designationList?.length ?? 0) === 0">
                        <i class="bi bi-download me-1"></i> Download Current Page ({{ designationList?.length ?? 0 }})
                    </button>
                </li>
                <li>
                    <button ngbDropdownItem (click)="downloadExcel('all')" [disabled]="isDownloadingExcel || listLoading || totalItems === 0">
                        <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
                    </button>
                </li>
            </ul>
        </div>
        <!-- *** NEW: Create Button *** -->
        <button class="btn-sm adani-btn ms-2" (click)="openCreateModal()" title="Create New Designation">
          <i class="bi bi-plus-circle"></i> Create New
        </button>
        <img src="../../../assets/svg/filter.svg" class="filter-button ms-3" (click)="openFilterModal()" alt="Filter"
          style="width: 35px;" />
      </div>
    </div>
  <div class="card-body">
    <div class="table-container">
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead class="table-header">
                  <tr class="text-center">
                    <th scope="col">Id</th>
                    <th scope="col">Enabled/Disabled</th>
                    <th scope="col">Designation Name</th>
                    <th scope="col">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <!-- Loading Indicator -->
                   <tr *ngIf="listLoading">
                      <td colspan="4" class="text-center">
                          <div class="spinner-border spinner-border-sm" role="status">
                              <span class="visually-hidden">Loading...</span>
                          </div>
                          Loading Designations...
                      </td>
                  </tr>
                   <!-- No Data Message -->
                   <tr *ngIf="!listLoading && designationList.length === 0">
                      <td colspan="4" class="text-center">No designations found.</td>
                  </tr>
                   <!-- Data Rows -->
                  <tr *ngFor="let designation of designationList">
                    <td class="text-center">{{ designation.id }}</td>
                    <td>
                        <app-switch
                            [(checked)]="designation.enabled"
                            [requireConfirmation]="true"
                            (checkedChange)="onSwitchToggle($event, designation)"
                            onLabel="Active" offLabel="Inactive">
                        </app-switch>
                    </td>
                    <td>{{ designation.title }}</td>
                    <td class="actions text-center"> <!-- Added text-center -->
                      <!-- *** UPDATED: Edit Button Click Handler *** -->
                      <button class="adani-btn" (click)="openEditModal(designation)" title="Edit Designation">
                        <i class="bi bi-pencil edit"></i>
                    </button>
                    </td>
                  </tr>
                </tbody>
              </table>
        </div>
    </div>
  </div>
  <div class="card-footer text-muted text-center">
    <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
    (pageChange)="onPageChange($event)"></app-pagination>
  </div>
</div>

<!-- Filter Offcanvas (Renamed *ngIf variable) -->
<app-offcanvas [title]="'Filter Designations'" *ngIf="isFilterModalOpen" (onClickCross)="closeFilterModal()">
<div class="filter-container p-3">
    <form #filterForm="ngForm" (ngSubmit)="applyFilters()">
        <div class="row g-3">

            <div class="col-12">
                <label class="form-label" for="filterDesigName">Designation Name</label>
                <input type="text" id="filterDesigName" class="form-control" placeholder="Search by Designation Name"
                       [(ngModel)]="filters.name" name="name" #filterName="ngModel"
                       pattern="^[a-zA-Z\s]*$" maxlength="30"
                       [ngClass]="{'is-invalid': filterName.invalid && (filterName.dirty || filterName.touched)}">
                <div *ngIf="filterName.invalid && (filterName.dirty || filterName.touched)" class="invalid-feedback">
                    <div *ngIf="filterName.errors?.['pattern']">Designation name should contain only alphabets.</div>
                </div>
            </div>

             <div class="col-12">
                <label class="form-label" for="filterEnabledDesig">Enabled Status</label>
                <select id="filterEnabledDesig" class="form-select"
                        [(ngModel)]="filters.enabled" name="enabled">
                    <option [ngValue]="null">Any</option>
                    <option value="true">Yes</option>
                    <option value="false">No</option>
                </select>
            </div>

            <div class="col-12">
                <label class="form-label" for="filterSortByDesig">Sort By</label>
                <select id="filterSortByDesig" class="form-select"
                        [(ngModel)]="filters.sortField" name="sortField">
                    <option [ngValue]="null">Default Sort (Name ASC)</option>
                    <option *ngFor="let field of availableSortFields" [value]="field.value">{{ field.label }}</option>
                </select>
                 <label class="form-label mt-2" for="filterSortDirDesig">Sort Direction</label>
                 <select id="filterSortDirDesig" class="form-select"
                        [(ngModel)]="filters.sortDirection" name="sortDirection">
                    <option value="ASC">Ascending</option>
                    <option value="DESC">Descending</option>
                </select>
            </div>

            <div class="col-12 mt-4 d-grid gap-2">
                <button type="submit" class="btn adani-btn" [disabled]="filterForm.invalid">
                    <i class="bi bi-search me-1"></i> Search
                </button>
                 <button type="button" class="btn btn-secondary" (click)="resetFilters()">
                    <i class="bi bi-arrow-clockwise me-1"></i> Reset Filters
                </button>
            </div>
        </div>
    </form>
</div>
</app-offcanvas>

<!-- ********** NEW: Edit Offcanvas ********** -->
<app-offcanvas [title]="'Edit Designation'" *ngIf="isEditModalOpen" (onClickCross)="closeEditModal()">
<div class="edit-container p-3">
    <!-- Ensure selectedDesignation is not null before rendering the form -->
    <form *ngIf="selectedDesignation" #editForm="ngForm" (ngSubmit)="submitEditForm()">
        <div class="row g-3">

            <!-- Designation ID (Readonly) -->
            <div class="col-12">
                <label class="form-label" for="editDesigId">Designation ID</label>
                <input type="text" id="editDesigId" class="form-control"
                       [value]="selectedDesignation.id" name="id" readonly disabled>
            </div>

            <!-- Designation Name (Editable) -->
            <div class="col-12">
                <label class="form-label" for="editDesigName">Designation Name</label>
                <input type="text" id="editDesigName" class="form-control" placeholder="Enter Designation Name"
                       [(ngModel)]="selectedDesignation.title" name="title" required #titleInput="ngModel"
                       pattern="^[a-zA-Z\s]*$" maxlength="30"
                       [ngClass]="{'is-invalid': titleInput.invalid && (titleInput.dirty || titleInput.touched)}">
                 <!-- Validation Messages -->
                 <div *ngIf="titleInput.invalid && (titleInput.dirty || titleInput.touched)" class="invalid-feedback">
                    <div *ngIf="titleInput.errors?.['required']">Designation name is required.</div>
                    <div *ngIf="titleInput.errors?.['pattern']">Designation name should contain only alphabets.</div>
                    <div *ngIf="titleInput.errors?.['maxlength']">Designation name cannot exceed 30 characters.</div>
                </div>
                <!-- Character count display -->
                <small *ngIf="selectedDesignation?.title" class="text-muted">
                    {{ selectedDesignation.title.length }}/30 characters
                </small>
            </div>

             <!-- Enabled Status -->
             <div class="col-12">
                 <label class="form-label d-block mb-2">Status</label>
                  <app-switch
                        [(checked)]="selectedDesignation.enabled"
                        name="enabled"
                        onLabel="Active"
                        offLabel="Inactive">
                  </app-switch>
             </div>


            <!-- Action Buttons -->
            <div class="col-12 mt-4 d-flex justify-content-end gap-2">
                <button type="button" class="btn btn-secondary" (click)="closeEditModal()">
                    Cancel
                </button>
                <button type="submit" class="btn adani-btn" [disabled]="editForm.invalid || editLoading">
                     <span *ngIf="!editLoading"><i class="bi bi-save me-1"></i> Save Changes</span>
                     <span *ngIf="editLoading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                     <span *ngIf="editLoading"> Saving...</span>
                </button>
            </div>
        </div>
    </form>
     <!-- Show loading indicator if form is null (e.g., data fetching) -->
    <div *ngIf="!selectedDesignation && isEditModalOpen" class="text-center p-5">
         <div class="spinner-border spinner-border-sm" role="status">
             <span class="visually-hidden">Loading form...</span>
         </div>
    </div>
</div>
</app-offcanvas>
<!-- ********** END: Edit Offcanvas ********** -->

<!-- ********** NEW: Create Offcanvas ********** -->
<app-offcanvas [title]="'Create New Designation'" *ngIf="isCreateModalOpen" (onClickCross)="closeCreateModal()">
<div class="create-container p-3">
    <form #createForm="ngForm" (ngSubmit)="submitCreateForm()">
        <div class="row g-3">

            <!-- Designation Name (Required) -->
            <div class="col-12">
                <label class="form-label" for="createDesigName">Designation Name</label>
                <input type="text" id="createDesigName" class="form-control" placeholder="Enter New Designation Name"
                       [(ngModel)]="newDesignationData.title" name="title" required #createTitleInput="ngModel"
                       pattern="^[a-zA-Z\s]*$" maxlength="30"
                       [ngClass]="{'is-invalid': createTitleInput.invalid && (createTitleInput.dirty || createTitleInput.touched)}">
                <!-- Validation Messages -->
                <div *ngIf="createTitleInput.invalid && (createTitleInput.dirty || createTitleInput.touched)" class="invalid-feedback">
                    <div *ngIf="createTitleInput.errors?.['required']">Designation name is required.</div>
                    <div *ngIf="createTitleInput.errors?.['pattern']">Designation name should contain only alphabets.</div>
                    <div *ngIf="createTitleInput.errors?.['maxlength']">Designation name cannot exceed 30 characters.</div>
                </div>
                <!-- Character count display -->
                <small *ngIf="newDesignationData.title" class="text-muted">
                    {{ newDesignationData.title.length }}/30 characters
                </small>
            </div>

            <!-- Enabled Status (Default to Active/true) -->
            <div class="col-12">
                <label class="form-label d-block mb-2">Status</label>
                <app-switch
                      [(checked)]="newDesignationData.enabled"
                      name="enabled"
                      onLabel="Active"
                      offLabel="Inactive">
                </app-switch>
            </div>

            <!-- Action Buttons -->
            <div class="col-12 mt-4 d-flex justify-content-end gap-2">
                <button type="button" class="btn btn-secondary" (click)="closeCreateModal()">
                    Cancel
                </button>
                <button type="submit" class="btn adani-btn" [disabled]="createForm.invalid || createLoading">
                    <span *ngIf="!createLoading"><i class="bi bi-plus-circle-fill me-1"></i> Create Designation</span>
                    <span *ngIf="createLoading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                    <span *ngIf="createLoading"> Creating...</span>
                </button>
            </div>
        </div>
    </form>
</div>
</app-offcanvas>
<!-- ********** END: Create Offcanvas ********** -->
