<!-- Toast Message Component -->
<app-toast-message></app-toast-message>

<!-- Plant List Card -->
<div class="card" id="plant-list-card"> <!-- Changed ID for clarity -->
  <div class="card-header">
    <div class="row align-items-center">
      <div class="col">
        <h6 class="mb-0">List of Plants</h6>
      </div>
      <div class="col text-end d-flex align-items-center justify-content-end">
         <!-- Create Button -->
         <button type="button" class="btn btn-sm adani-btn me-2" (click)="openCreateModal()" title="Add New Plant">
             <i class="bi bi-plus-circle"></i> Add Plant
         </button>

         <!-- *** UPDATED: Download Excel Button with Dropdown *** -->
         <div ngbDropdown class="d-inline-block me-2">
             <button type="button" class="btn btn-sm adani-btn dropdown-toggle" id="downloadPlantExcelDropdown" ngbDropdownToggle
                 [disabled]="isDownloadingExcel || listLoading">
                 <span *ngIf="!isDownloadingExcel">
                     <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
                 </span>
                 <span *ngIf="isDownloadingExcel">
                     <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                     Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '') }}...
                 </span>
             </button>
             <ul ngbDropdownMenu aria-labelledby="downloadPlantExcelDropdown">
                 <li>
                     <button type="button" ngbDropdownItem (click)="downloadExcel('current')" [disabled]="isDownloadingExcel || listLoading || (plantList?.length ?? 0) === 0">
                         <i class="bi bi-download me-1"></i> Download Current Page ({{ plantList?.length ?? 0 }})
                     </button>
                 </li>
                 <li>
                     <button type="button" ngbDropdownItem (click)="downloadExcel('all')" [disabled]="isDownloadingExcel || listLoading || totalItems === 0">
                         <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
                     </button>
                 </li>
             </ul>
         </div>
         <!-- *** END: Download Excel Button Update *** -->

         <!-- Filter Button -->
         <img src="../../../assets/svg/filter.svg" class="filter-button ms-1" (click)="openFilterModal()" alt="Filter" title="Open Filters"/>
      </div>
    </div>
  </div>
    <div class="card-body">
      <div class="table-container">
          <div class="table-responsive">
              <table class="table table-bordered table-hover">
                  <thead class="table-header">
                    <tr class="text-center">
                      <th scope="col" class="col-actions">Actions</th>
                      <th scope="col" class="col-status">Status</th>
                      <th scope="col" class="col-details">Plant Details</th>
                    </tr>
                  </thead>
                  <tbody>
                     <tr *ngIf="listLoading">
                         <td colspan="3" class="text-center p-4">
                             <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                             Loading plants...
                         </td>
                     </tr>
                     <tr *ngIf="!listLoading && (!plantList || plantList.length === 0)">
                          <td colspan="3" class="text-center p-4 text-muted">
                             No plants found matching the criteria.
                         </td>
                     </tr>
                    <tr *ngFor="let plant of plantList">
                       <td class="actions text-center align-middle">
                          <div class="d-flex flex-column align-items-center gap-2">
                              <button type="button" class="btn btn-sm adani-btn edit-btn" (click)="openEditModal(plant)" title="Edit Plant">
                                  <i class="bi bi-pencil"></i> <span class="white-text">Edit</span>
                              </button>
                              <button type="button" class="btn btn-sm adani-btn export-qr-btn" (click)="exportQRCode(plant)" title="Export Plant QR Code" [disabled]="exportingQRPlantId === plant.id">
                                <span *ngIf="exportingQRPlantId !== plant.id" class="white-text">
                                    <i class="bi bi-qr-code"></i> Export QR
                                </span>
                                <span *ngIf="exportingQRPlantId === plant.id" class="white-text">
                                    <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span> Exporting...
                                </span>
                              </button>
                          </div>
                       </td>
                       <td class="status-cell text-center align-middle">
                          <app-switch
                              [(checked)]="plant.enabled"
                              [requireConfirmation]="true"
                              (checkedChange)="onSwitchToggle($event, plant)"
                              onLabel="Active" offLabel="Inactive">
                          </app-switch>
                       </td>
                       <td class="details-cell">
                            <div class="details-container">
                                <p class="label-value"><strong>Plant Name</strong> <span class="value-text">{{ plant.name }}</span></p>
                                <p class="label-value"><strong>Emergency Contact</strong> <span class="value-text">{{ plant.emergencyContactNumber ?? 'N/A'}}</span></p>
                                <p class="label-value"><strong>Plant Type</strong> <span class="value-text">{{ plant.plantType?.title || 'N/A' }}</span></p>
                                <p class="label-value"><strong>Cluster</strong> <span class="value-text">{{ plant.cluster?.title || 'N/A' }}</span></p>
                                <p class="label-value"><strong>Opco</strong> <span class="value-text">{{ plant.opco?.title || 'N/A' }}</span></p>
                                <p class="label-value"><strong>Plant Admin</strong> <span class="value-text">{{ plant.plantAdmin?.firstName }} {{ plant.plantAdmin?.lastName || '' }}</span></p>
                            </div>
                       </td>
                    </tr>
                  </tbody>
                </table>
          </div>
      </div>
    </div>
    <div class="card-footer text-muted text-center">
      <app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
      (pageChange)="onPageChange($event)"></app-pagination>
    </div>
</div>

<!-- Filter Offcanvas (Keep Existing) -->
<app-offcanvas [title]="'Filter Plants'" *ngIf="isFilterModalOpen" (onClickCross)="closeFilterModal()">
  <div class="filter-container p-3">
    <form #filterForm="ngForm" (ngSubmit)="applyFilters()">
      <div class="row g-3">
          <div class="col-12">
              <label class="form-label" for="filterName">Name</label>
              <input type="text" id="filterName" class="form-control" placeholder="Plant Name"
                     [(ngModel)]="filters.name" name="name" maxlength="30" pattern="^[a-zA-Z\s]*$"
                     #filterName="ngModel" [ngClass]="{'is-invalid': filterName.invalid && (filterName.dirty || filterName.touched)}"
                     (blur)="trimInputField(filters, 'name')">
              <div *ngIf="filterName.invalid && (filterName.dirty || filterName.touched)" class="invalid-feedback">
                  <div *ngIf="filterName.errors?.['pattern']">Plant name should contain only alphabets.</div>
                  <div *ngIf="filterName.errors?.['maxlength']">Plant name cannot exceed 30 characters.</div>
              </div>
              <!-- Character count display - only visible when there's text -->
              <small *ngIf="filters.name" class="text-muted d-block text-end mt-1">
                  {{ filters.name.length }}/30 characters
              </small>
          </div>
          <div class="col-12">
              <label class="form-label" for="filterPlantType">Plant Type</label>
              <select id="filterPlantType" class="form-select" [(ngModel)]="filters.plantTypeId" name="plantTypeId">
                <option [ngValue]="null">All Types</option>
                <option *ngFor="let type of availablePlantTypes" [ngValue]="type.id">{{ type.title }}</option>
              </select>
          </div>
          <div class="col-12">
              <label class="form-label" for="filterPlantAdmin">Plant Admin</label>
              <select id="filterPlantAdmin" class="form-select" [(ngModel)]="filters.plantAdminId" name="plantAdminId">
                <option [ngValue]="null">All Admins</option>
                <option *ngFor="let admin of availablePlantAdmins" [ngValue]="admin.id">{{ admin.firstName }} {{admin.lastName}}</option>
              </select>
          </div>
          <div class="col-12">
              <label class="form-label" for="filterEnabled">Enabled Status</label>
              <select id="filterEnabled" class="form-select" [(ngModel)]="filters.enabled" name="enabled">
                <option [ngValue]="null">Any</option>
                <option [ngValue]="'true'">Yes (Active)</option>
                <option [ngValue]="'false'">No (Inactive)</option>
              </select>
          </div>
          <div class="col-12">
              <label class="form-label" for="filterSortBy">Sort By</label>
              <select id="filterSortBy" class="form-select" [(ngModel)]="filters.sortField" name="sortField">
                  <option [ngValue]="'name'">Name</option>
                  <option [ngValue]="'id'">ID</option>
                  <option [ngValue]="'plantType.title'">Plant Type</option> <!-- Requires backend support -->
                  <option [ngValue]="'plantAdmin.firstName'">Plant Admin</option> <!-- Requires backend support -->
                  <option [ngValue]="'createdAt'">Created Date</option>
              </select>
               <label class="form-label mt-2" for="filterSortDir">Sort Direction</label>
              <select id="filterSortDir" class="form-select" [(ngModel)]="filters.sortDirection" name="sortDirection">
                <option [ngValue]="'ASC'">Ascending</option>
                <option [ngValue]="'DESC'">Descending</option>
              </select>
          </div>
          <div class="col-12 mt-4 d-grid gap-2">
               <button type="submit" class="btn adani-btn" [disabled]="listLoading">
                  <i class="bi bi-search me-1"></i> Apply Filters
               </button>
               <button type="button" class="btn btn-secondary" (click)="resetFilters()" [disabled]="listLoading">
                   <i class="bi bi-arrow-clockwise me-1"></i> Reset Filters
               </button>
          </div>
      </div>
    </form>
  </div>
</app-offcanvas>

<!-- Edit Offcanvas (Keep Existing) -->
<app-offcanvas [title]="'Edit Plant'" *ngIf="isEditModalOpen" (onClickCross)="closeEditModal()">
  <div class="edit-container p-3">
      <div *ngIf="!selectedPlant && isEditModalOpen" class="text-center p-5">
           <div class="spinner-border" role="status"> <span class="visually-hidden">Loading...</span> </div>
      </div>
      <form *ngIf="selectedPlant" #editForm="ngForm" (ngSubmit)="submitEditForm()">
          <div class="row g-3">
               <div class="col-12">
                  <label class="form-label" for="editPlantId">Plant ID</label>
                  <input type="text" id="editPlantId" class="form-control" [value]="selectedPlant.id" name="id" readonly disabled>
              </div>
              <div class="col-12">
                  <label class="form-label" for="editPlantName">Plant Name <span class="text-danger">*</span></label>
                  <input type="text" id="editPlantName" class="form-control" [(ngModel)]="selectedPlant.name"
                         name="name" required #editNameCtrl="ngModel" maxlength="30" pattern="^[a-zA-Z\s]*$"
                         [class.is-invalid]="editNameCtrl.invalid && (editNameCtrl.dirty || editNameCtrl.touched)"
                         (blur)="trimInputField(selectedPlant, 'name')">
                   <div *ngIf="editNameCtrl.invalid && (editNameCtrl.dirty || editNameCtrl.touched)" class="invalid-feedback">
                     <div *ngIf="editNameCtrl.errors?.['required']">Plant name is required.</div>
                     <div *ngIf="editNameCtrl.errors?.['pattern']">Plant name should contain only alphabets.</div>
                     <div *ngIf="editNameCtrl.errors?.['maxlength']">Plant name cannot exceed 30 characters.</div>
                  </div>
                  <!-- Character count display - only visible when there's text -->
                  <small *ngIf="selectedPlant?.name" class="text-muted d-block text-end mt-1">
                      {{ selectedPlant.name.length }}/30 characters
                  </small>
              </div>
              <div class="col-12">
                  <label class="form-label" for="editEmergencyContact">Emergency Contact No. <span class="text-danger">*</span></label>
                  <input type="text" id="editEmergencyContact" class="form-control"
                         [(ngModel)]="selectedPlant.emergencyContactNumber" name="emergencyContactNumber"
                         pattern="^[0-9]{10}$" maxlength="10" required #editContactCtrl="ngModel"
                         [class.is-invalid]="editContactCtrl.invalid && (editContactCtrl.dirty || editContactCtrl.touched)"
                         (blur)="trimInputField(selectedPlant, 'emergencyContactNumber')">
                  <div *ngIf="editContactCtrl.invalid && (editContactCtrl.dirty || editContactCtrl.touched)" class="invalid-feedback">
                      <div *ngIf="editContactCtrl.errors?.['required']">Emergency contact number is required.</div>
                      <div *ngIf="editContactCtrl.errors?.['pattern']">Please enter a valid 10-digit number.</div>
                  </div>
              </div>
              <div class="col-12">
                  <label class="form-label" for="editPlantType">Plant Type <span class="text-danger">*</span></label>
                  <select id="editPlantType" class="form-select" [(ngModel)]="selectedPlant.plantTypeId"
                          name="plantTypeId" required #editPlantTypeCtrl="ngModel"
                          [class.is-invalid]="editPlantTypeCtrl.invalid && (editPlantTypeCtrl.dirty || editPlantTypeCtrl.touched)">
                      <option [ngValue]="null">-- Select Type --</option>
                      <option *ngFor="let type of availablePlantTypes" [ngValue]="type.id">{{ type.title }}</option>
                  </select>
                  <div *ngIf="editPlantTypeCtrl.invalid && (editPlantTypeCtrl.dirty || editPlantTypeCtrl.touched)" class="invalid-feedback">
                      <div *ngIf="editPlantTypeCtrl.errors?.['required']">Plant type is required.</div>
                  </div>
              </div>
              <div class="col-12">
                  <label class="form-label" for="editCluster">Cluster <span class="text-danger">*</span></label>
                  <select id="editCluster" class="form-select" [(ngModel)]="selectedPlant.clusterId"
                          name="clusterId" required #editClusterCtrl="ngModel"
                          [class.is-invalid]="editClusterCtrl.invalid && (editClusterCtrl.dirty || editClusterCtrl.touched)">
                      <option [ngValue]="null">-- Select Cluster --</option>
                      <option *ngFor="let cluster of availableClusters" [ngValue]="cluster.id">{{ cluster.title }}</option>
                  </select>
                  <div *ngIf="editClusterCtrl.invalid && (editClusterCtrl.dirty || editClusterCtrl.touched)" class="invalid-feedback">
                      <div *ngIf="editClusterCtrl.errors?.['required']">Cluster is required.</div>
                  </div>
              </div>
              <div class="col-12">
                  <label class="form-label" for="editOpco">OPCO <span class="text-danger">*</span></label>
                  <select id="editOpco" class="form-select" [(ngModel)]="selectedPlant.opcoId"
                          name="opcoId" required #editOpcoCtrl="ngModel"
                          [class.is-invalid]="editOpcoCtrl.invalid && (editOpcoCtrl.dirty || editOpcoCtrl.touched)">
                       <option [ngValue]="null">-- Select OPCO --</option>
                       <option *ngFor="let opco of availableOpcos" [ngValue]="opco.id">{{ opco.title }}</option>
                  </select>
                  <div *ngIf="editOpcoCtrl.invalid && (editOpcoCtrl.dirty || editOpcoCtrl.touched)" class="invalid-feedback">
                      <div *ngIf="editOpcoCtrl.errors?.['required']">OPCO is required.</div>
                  </div>
              </div>
              <div class="col-12">
                  <label class="form-label" for="editPlantAdmin">Plant Admin <span class="text-danger">*</span></label>
                  <select id="editPlantAdmin" class="form-select" [(ngModel)]="selectedPlant.plantAdminId"
                          name="plantAdminId" required #editPlantAdminCtrl="ngModel"
                          [class.is-invalid]="editPlantAdminCtrl.invalid && (editPlantAdminCtrl.dirty || editPlantAdminCtrl.touched)">
                       <option [ngValue]="null">-- Select Admin --</option>
                       <option *ngFor="let admin of availablePlantAdmins" [ngValue]="admin.id">{{ admin.firstName }} {{ admin.lastName }}</option>
                  </select>
                  <div *ngIf="editPlantAdminCtrl.invalid && (editPlantAdminCtrl.dirty || editPlantAdminCtrl.touched)" class="invalid-feedback">
                      <div *ngIf="editPlantAdminCtrl.errors?.['required']">Plant admin is required.</div>
                  </div>
              </div>
              <div class="col-12">
                 <div class="form-check form-switch mt-2">
                      <input class="form-check-input" type="checkbox" role="switch" id="editPlantEnabled" name="enabled"
                             [(ngModel)]="selectedPlant.enabled" [attr.aria-checked]="selectedPlant.enabled">
                      <label class="form-check-label" for="editPlantEnabled">Enabled (Active)</label>
                  </div>
              </div>
              <div class="col-12 mt-4 d-flex justify-content-end gap-2">
                   <button type="button" class="btn btn-secondary" (click)="closeEditModal()" [disabled]="editLoading">Cancel</button>
                  <button type="submit" class="btn adani-btn" [disabled]="editForm.invalid || editLoading">
                     <span *ngIf="!editLoading"><i class="bi bi-save me-1"></i> Save Changes</span>
                     <span *ngIf="editLoading">
                         <span class="spinner-border spinner-border-sm me-1"></span> Saving...
                     </span>
                  </button>
              </div>
          </div>
      </form>
  </div>
</app-offcanvas>

<!-- Create Offcanvas (Keep Existing) -->
<app-offcanvas [title]="'Add New Plant'" *ngIf="isCreateModalOpen" (onClickCross)="closeCreateModal()">
  <div class="create-container p-3">
       <form #createForm="ngForm" (ngSubmit)="submitCreateForm()">
          <div class="row g-3">
              <div class="col-12">
                  <label class="form-label" for="createPlantName">Plant Name <span class="text-danger">*</span></label>
                  <input type="text" id="createPlantName" class="form-control" [(ngModel)]="newPlantData.name"
                         name="name" required #createNameCtrl="ngModel" maxlength="30" pattern="^[a-zA-Z\s]*$"
                         [class.is-invalid]="createNameCtrl.invalid && (createNameCtrl.dirty || createNameCtrl.touched)"
                         (blur)="trimInputField(newPlantData, 'name')">
                   <div *ngIf="createNameCtrl.invalid && (createNameCtrl.dirty || createNameCtrl.touched)" class="invalid-feedback">
                     <div *ngIf="createNameCtrl.errors?.['required']">Plant name is required.</div>
                     <div *ngIf="createNameCtrl.errors?.['pattern']">Plant name should contain only alphabets.</div>
                     <div *ngIf="createNameCtrl.errors?.['maxlength']">Plant name cannot exceed 30 characters.</div>
                  </div>
                  <!-- Character count display - only visible when there's text -->
                  <small *ngIf="newPlantData.name" class="text-muted d-block text-end mt-1">
                      {{ newPlantData.name.length }}/30 characters
                  </small>
              </div>
              <div class="col-12">
                  <label class="form-label" for="createEmergencyContact">Emergency Contact No. <span class="text-danger">*</span></label>
                  <input type="text" id="createEmergencyContact" class="form-control"
                         [(ngModel)]="newPlantData.emergencyContactNumber" name="emergencyContactNumber"
                         pattern="^[0-9]{10}$" maxlength="10" required #createContactCtrl="ngModel"
                         [class.is-invalid]="createContactCtrl.invalid && (createContactCtrl.dirty || createContactCtrl.touched)"
                         (blur)="trimInputField(newPlantData, 'emergencyContactNumber')">
                  <div *ngIf="createContactCtrl.invalid && (createContactCtrl.dirty || createContactCtrl.touched)" class="invalid-feedback">
                      <div *ngIf="createContactCtrl.errors?.['required']">Emergency contact number is required.</div>
                      <div *ngIf="createContactCtrl.errors?.['pattern']">Please enter a valid 10-digit number.</div>
                  </div>
              </div>
              <div class="col-12">
                  <label class="form-label" for="createPlantType">Plant Type <span class="text-danger">*</span></label>
                  <select id="createPlantType" class="form-select" [(ngModel)]="newPlantData.plantTypeId"
                          name="plantTypeId" required #createPlantTypeCtrl="ngModel"
                          [class.is-invalid]="createPlantTypeCtrl.invalid && (createPlantTypeCtrl.dirty || createPlantTypeCtrl.touched)">
                      <option [ngValue]="null">-- Select Type --</option>
                      <option *ngFor="let type of availablePlantTypes" [ngValue]="type.id">{{ type.title }}</option>
                  </select>
                  <div *ngIf="createPlantTypeCtrl.invalid && (createPlantTypeCtrl.dirty || createPlantTypeCtrl.touched)" class="invalid-feedback">
                      <div *ngIf="createPlantTypeCtrl.errors?.['required']">Plant type is required.</div>
                  </div>
              </div>
              <div class="col-12">
                  <label class="form-label" for="createCluster">Cluster <span class="text-danger">*</span></label>
                  <select id="createCluster" class="form-select" [(ngModel)]="newPlantData.clusterId"
                          name="clusterId" required #createClusterCtrl="ngModel"
                          [class.is-invalid]="createClusterCtrl.invalid && (createClusterCtrl.dirty || createClusterCtrl.touched)">
                      <option [ngValue]="null">-- Select Cluster --</option>
                      <option *ngFor="let cluster of availableClusters" [ngValue]="cluster.id">{{ cluster.title }}</option>
                  </select>
                  <div *ngIf="createClusterCtrl.invalid && (createClusterCtrl.dirty || createClusterCtrl.touched)" class="invalid-feedback">
                      <div *ngIf="createClusterCtrl.errors?.['required']">Cluster is required.</div>
                  </div>
              </div>
              <div class="col-12">
                  <label class="form-label" for="createOpco">OPCO <span class="text-danger">*</span></label>
                  <select id="createOpco" class="form-select" [(ngModel)]="newPlantData.opcoId"
                          name="opcoId" required #createOpcoCtrl="ngModel"
                          [class.is-invalid]="createOpcoCtrl.invalid && (createOpcoCtrl.dirty || createOpcoCtrl.touched)">
                       <option [ngValue]="null">-- Select OPCO --</option>
                       <option *ngFor="let opco of availableOpcos" [ngValue]="opco.id">{{ opco.title }}</option>
                  </select>
                  <div *ngIf="createOpcoCtrl.invalid && (createOpcoCtrl.dirty || createOpcoCtrl.touched)" class="invalid-feedback">
                      <div *ngIf="createOpcoCtrl.errors?.['required']">OPCO is required.</div>
                  </div>
              </div>
              <div class="col-12">
                  <label class="form-label" for="createPlantAdmin">Plant Admin <span class="text-danger">*</span></label>
                  <select id="createPlantAdmin" class="form-select" [(ngModel)]="newPlantData.plantAdminId"
                          name="plantAdminId" required #createPlantAdminCtrl="ngModel"
                          [class.is-invalid]="createPlantAdminCtrl.invalid && (createPlantAdminCtrl.dirty || createPlantAdminCtrl.touched)">
                       <option [ngValue]="null">-- Select Admin --</option>
                       <option *ngFor="let admin of availablePlantAdmins" [ngValue]="admin.id">{{ admin.firstName }} {{ admin.lastName }}</option>
                  </select>
                  <div *ngIf="createPlantAdminCtrl.invalid && (createPlantAdminCtrl.dirty || createPlantAdminCtrl.touched)" class="invalid-feedback">
                      <div *ngIf="createPlantAdminCtrl.errors?.['required']">Plant admin is required.</div>
                  </div>
              </div>
               <div class="col-12">
                  <div class="form-check form-switch mt-2">
                      <input class="form-check-input" type="checkbox" role="switch" id="createPlantEnabled" name="enabled"
                             [(ngModel)]="newPlantData.enabled" [attr.aria-checked]="newPlantData.enabled">
                      <label class="form-check-label" for="createPlantEnabled">Enabled (Active)</label>
                  </div>
                  <small class="form-text text-muted">Newly created plants will be active by default.</small>
              </div>
              <div class="col-12 mt-4 d-flex justify-content-end gap-2">
                   <button type="button" class="btn btn-secondary" (click)="closeCreateModal()" [disabled]="createLoading">Cancel</button>
                  <button type="submit" class="btn adani-btn" [disabled]="createForm.invalid || createLoading">
                     <span *ngIf="!createLoading"><i class="bi bi-plus-circle-fill me-1"></i> Add Plant</span>
                     <span *ngIf="createLoading">
                         <span class="spinner-border spinner-border-sm me-1"></span> Adding...
                     </span>
                  </button>
              </div>
          </div>
      </form>
  </div>
</app-offcanvas>