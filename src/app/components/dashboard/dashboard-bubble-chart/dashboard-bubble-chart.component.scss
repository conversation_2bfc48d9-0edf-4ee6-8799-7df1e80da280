.bubble-chart-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.bubble-wrapper {
    position: relative;
    width: 200px;
    height: 160px;
    // margin-right: 11px;
}

.bubble {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-weight: bold;
    text-align: center;
    position: absolute;
}

.big {
    width: 114px;
    height: 114px;
    left: 0px;
    top: 0px
}

.medium {
    width: 70px;
    height: 70px;
    left: 114px;
    top: 0px;
}

.small {
    width: 44px;
    height: 44px;
    left: 110px;
    top: 72px;
}

.smaller {
    width: 38px;
    height: 38px;
    left: 156px;
    top: 68px;
}

.text {
    color: #000;
    font-size: 12px;
}

.legend {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    margin-bottom: 35px;
    font-size: 12px;
    height: auto;
    width: 280px;
    margin-left: -18px;
}

.legend-item-purple,
.legend-item-red,
.legend-item-green,
.legend-item-yellow {
    display: flex;
    align-items: center;
    padding: 8px;
}

.legend-item-purple {
    border-right: dashed 1px #CFCFCF;
    border-bottom: dashed 1px #CFCFCF;
}

.legend-item-red {
    border-bottom: dashed 1px #CFCFCF;
}

.legend-item-green {
    border-right: dashed 1px #CFCFCF;
}

.legend-item-yellow {}

.legend-box {
    display: inline-block;
    width: 11px;
    height: 11px;
    margin-right: 8px;
    border-radius: 4px;
}

.purple {
    background-color: #3A0AAA;
    margin-bottom: 16px;
}

.red {
    background-color: #C00000;
    margin-bottom: 16px;
}

.green {
    background-color: #00663C;
    margin-bottom: 16px;
}

.yellow {
    background-color: #F3E657;
    margin-bottom: 16px;
}


.card {
    width: 472px;
    height: 128px;
    padding: 5px;
    border-radius: 8px;
    background: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: start;
    margin-bottom: 5px;
}

.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 132px;
    width: 100%;
}

.legend-item {
    transition: background-color 0.3s ease;
}

.highlight {
    background-color: #EFEAFE;
}

.legend-content #number {
    transition: font-weight 0.3s ease, color 0.3s ease;
}

.legend-content #number.highlight {
    font-weight: bold;
    color: #000;
}

.legend-content #title.highlight {
    font-weight: bold;
    color: #000;
}

.big-circle-bg-color {
    background-color: #EFEAFE
}

.medium-circle-bg-color {
    background-color: #FCE5E8
}

.small-circle-bg-color {
    background-color: #E0F6ED
}

.activatedQR-title {
    text-align: left;
    margin-right: 26px;
}

.activatedQR-count {
    font-size: 11px;
    color: #4A4A4A;
    text-align: start;
    margin-right: 75px;
}

.deactivatedQR-title {
    margin-right: 10px;
}

.deactivatedQR-count {
    font-size: 11px;
    color: #4A4A4A;
    text-align: start;
    margin-right: 75px;
}

.unregisteredQr-title {
    margin-right: 8px;
}

.unregisteredQr-count {
    font-size: 11px;
    color: #4A4A4A;
    text-align: start;
    margin-right: 75px;
}

.totalScanQr-count {
    font-size: 11px;
    color: #4A4A4A;
    text-align: start;
    font-weight: bold;
}

@media (max-width: 600px) {
    .bubble-chart-container {
        flex-direction: column;
        align-items: flex-start;
        /* or center, depending on your preference */
    }

    .legend {
        width: 100%;
        grid-template-columns: repeat(1, 1fr);
        margin-left: 0;
        /* reset margin */
        margin-top: 100px;
        /* spacing between bubble and legend */
        // grid-template-columns: 1fr;
        /* single column layout for legend items */
    }

    .legend-item-purple,
    .legend-item-red,
    .legend-item-green,
    .legend-item-yellow {
        border-right: none;
        /* remove right borders */
        border-bottom: dashed 1px #CFCFCF;
        /* keep bottom border for separation */
    }

    /* Optional: adjust bubble-wrapper width if needed */
    .bubble-wrapper {
        width: 100%;
        height: auto;
        position: relative;
        margin-bottom: 10px;
    }

    .card {
        width: 200px;
        height: 335px;
        padding: 5px;
        border-radius: 8px;
        background: #fff;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: start;
        margin-bottom: 5px;
    }
}