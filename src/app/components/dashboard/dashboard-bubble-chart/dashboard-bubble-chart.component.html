<div class="card" *ngIf="chartInitializedStatus">
  <div class="bubble-chart-container">
    <div class="bubble-wrapper">
      <div class="bubble big big-circle-bg-color" (mouseover)="highlightLegendNumber('activatedQr')"
        (mouseout)="removeHighlightLegendNumber()">
        <span class="text">{{ activatedQrPercentage }}%</span>
      </div>


      <div class="bubble medium medium-circle-bg-color" (mouseover)="highlightLegendNumber('deActivatedQr')"
        (mouseout)="removeHighlightLegendNumber()">
        <span class="text">{{ deActivatedQrPercentage }}%</span>
      </div>


      <div class="bubble small red small-circle-bg-color" (mouseover)="highlightLegendNumber('unregisteredQr')"
        (mouseout)="removeHighlightLegendNumber()">
        <span class="text">{{ unregisteredQrPercentage }}%</span>
      </div>
    </div>

    <div class="legend">
      <div class="legend-item-purple legend-item">
        <div class="legend-box purple"></div>
        <div class="legend-content">
          <div id="title" class="activatedQR-title" [class.highlight]="highlightedLegend === 'activatedQr'">Activated QR
          </div>
          <div id="number" [class.highlight]="highlightedLegend === 'activatedQr'" class="activatedQR-count">
            {{ activatedQr }}
          </div>
        </div>
      </div>
      <div class="legend-item-red">
        <div class="legend-box red"></div>
        <div class="legend-content">
          <div id="title" class="deactivatedQR-title" [class.highlight]="highlightedLegend === 'deActivatedQr'">
            De-Activated QR</div>
          <div id="number" [class.highlight]="highlightedLegend === 'deActivatedQr'" class="deactivatedQR-count">
            {{ deActivatedQr }}
          </div>
        </div>
      </div>
      <div class="legend-item-green">
        <div class="legend-box green"></div>
        <div class="legend-content">
          <div id="title" class="unregisteredQr-title" [class.highlight]="highlightedLegend === 'unregisteredQr'">
            Unregistered QR</div>
          <div id="number" [class.highlight]="highlightedLegend === 'unregisteredQr'" class="unregisteredQr-count">
            {{ unregisteredQr }}
          </div>
        </div>
      </div>
      <div class="legend-item-yellow">
        <div class="legend-box yellow"></div>
        <div class="legend-content">
          <div id="title">Total QR Scanned</div>
          <div id="number" class="totalScanQr-count">{{ totalQrScan
            }}</div>
        </div>
      </div>
    </div>

  </div>
</div>


<div class="loading-container" *ngIf="!chartInitializedStatus">
  <div class="spinner-border text-primary" style="width: 5rem; height: 5rem;" role="status">
    <span class="visually-hidden">Loading...</span>
  </div>
</div>