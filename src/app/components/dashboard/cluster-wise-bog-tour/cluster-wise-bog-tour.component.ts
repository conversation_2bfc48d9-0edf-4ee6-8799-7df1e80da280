import { Component, OnInit, ViewChild } from '@angular/core';
import { DashboardService } from '../../../services/dashboard/dashboard.service';
import { AuthService } from '../../../services/auth.service'; // Import AuthService
import { ChartComponent, NgApexchartsModule } from "ng-apexcharts";
import {
  ApexNonAxisChartSeries,
  ApexResponsive,
  ApexChart
} from "ng-apexcharts";
import { CommonModule } from '@angular/common';
import { createAxiosConfig } from '../../../core/utilities/axios-param-config';
import { MatDialog } from '@angular/material/dialog';
import { ClusterWiseBogTourDialog } from './cluster-wise-bog-tour-dialog/cluster-wise-bog-tour.dialog.component';

export type ChartOptions = {
  series: ApexNonAxisChartSeries | any;
  chart: ApexChart | any;
  responsive: ApexResponsive[] | any;
  labels: any;
  title: any;
  dataLabels?: any;
  tooltip?: any;
  colors?: any;
  legend: any;
  plotOptions: ApexPlotOptions | any;
};

@Component({
  selector: 'app-cluster-wise-bog-tour',
  standalone: true, // Add standalone: true for standalone components
  imports: [NgApexchartsModule, CommonModule],
  templateUrl: './cluster-wise-bog-tour.component.html',
  styleUrl: './cluster-wise-bog-tour.component.scss'
})
export class ClusterWiseBogTourComponent implements OnInit {

  chartInitializedStatus: boolean = false;
  @ViewChild("chart") chart!: ChartComponent;
  businessUnitId: string | null = null; // Declare businessUnitId property
  noDataAvailable: boolean = false; // New property to indicate no data

  public chartOptions!: Partial<ChartOptions>;

  constructor(
    private dashboardService: DashboardService,
    public dialog: MatDialog,
    private authService: AuthService // Inject AuthService
  ) {
    this.chartOptions = {}
  }

  ngOnInit() {
    this.businessUnitId = this.authService.getBusinessUnitId(); // Get businessUnitId
    this.getTourObservationCount();
  }

  private openDetailsDialog(title: string, count: number): void {
    this.dialog.open(ClusterWiseBogTourDialog, {
      width: '550px',
      height: '400px',
      data: {
        clusterTitle: title,
        observationCount: count
      }
    });
  }

  private onSeriesClick(event: any, chartContext: any, config: any): void {
    const clusterTitle = this.chartOptions.labels[config.dataPointIndex];
    const count = this.chartOptions.series[config.seriesIndex];

    this.openDetailsDialog(clusterTitle, count);
  }


  getTourObservationCount() {
    const body: any = { // Initialize body as any to allow adding properties
      page: 1,
      limit: 10,
      sort: 'title,ASC',
      filter: ['enabled||eq||true']
    }
    if (this.businessUnitId) {
      body.businessUnitId = this.businessUnitId; // Add businessUnitId to the body
    }
    const params = createAxiosConfig(body);
    this.dashboardService.getTourObservationCount(params).then((res) => {


      if (res && res.cluterWiseTourCount && Array.isArray(res.cluterWiseTourCount) && res.cluterWiseTourCount.length > 0) {
        const clusterData = res.cluterWiseTourCount;

        this.chartOptions.series = clusterData.map((cluster: any) => parseInt(cluster.count, 10));
        this.chartOptions.labels = clusterData.map((cluster: any) => cluster.title);

        this.chartOptions.dataLabels = {
          enabled: true,
          formatter: (value: any, options: any) => {
            return options.w.config.series[options.seriesIndex];
          },
          style: {
            fontSize: '11px',
            fontWeight: '400',
            position: 'center',
            colors: ['#000000'],
          }
        };

        this.chartOptions.chart = {
          type: 'donut',
          height: 300,
          width: '302px',
          events: {
            dataPointSelection: (event: any, chartContext: any, config: any) => {
              this.onSeriesClick(event, chartContext, config);
            }
          }
        };

        this.chartOptions.legend = {
          position: 'bottom',
          horizontalAlign: 'center',
          floating: false,
          fontSize: '11px',
          offsetY: -20, // Adjust as needed
          labels: {
            colors: ['#000'],
            useSeriesColors: false
          }
        };

        this.chartOptions.plotOptions = {
          pie: {
            donut: {
              size: "25%",
              columnWidth: '72%',
              borderRadius: 13
            }
          }
        };

        this.chartOptions.responsive = [
          {
            breakpoint: 180,
            options: {
              chart: {
                width: 70,
                height: 70
              },
              legend: {
                position: 'center'
              }
            }
          }
        ];

        this.chartOptions.colors = ['#FFD4E8', '#BAE3FF', '#F5D7B0', '#E1D1FF'];

        this.chartInitializedStatus = true;
      } else {
        this.chartInitializedStatus = false;
        this.chartOptions = {}; // Clear chart options to prevent rendering an empty chart
        this.noDataAvailable = true; // Set noDataAvailable to true

        if (res && Array.isArray(res.cluterWiseTourCount) && res.cluterWiseTourCount.length === 0) {
          console.info('Cluster-wise tour data is empty.');
        } else {
          console.error('Unexpected response format or missing cluster-wise tour data', res);
        }
      }
    }).catch(error => {
      console.error('Error fetching cluster-wise data', error);
    });

  }
}
