import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DashboardComponent } from './dashboard.component';
import { DashboardService } from '../../services/dashboard/dashboard.service';
import { AuthService } from '../../services/auth.service';
import { CommonModule } from '@angular/common';

describe('DashboardComponent', () => {
  let component: DashboardComponent;
  let fixture: ComponentFixture<DashboardComponent>;
  let dashboardService: jasmine.SpyObj<DashboardService>;
  let authService: jasmine.SpyObj<AuthService>;

  const mockDashboardData = {
    user: 150,
    tour: 250,
    incident: 75,
    observation: 300,
    activatedQr: 180,
    deActivatedQr: 45,
    unregisterdQr: 25,
    noOfScan: 500
  };

  const mockTourObservationData = {
    totalTours: 250,
    totalObservations: 300,
    completedTours: 200,
    pendingObservations: 50
  };

  beforeEach(async () => {
    const dashboardSpy = jasmine.createSpyObj('DashboardService', [
      'getDashboard',
      'getTourObservationCount',
      'getClusterWiseGraph',
      'getClusterWiseStatusGraph',
      'getPlantTourCount'
    ]);
    const authSpy = jasmine.createSpyObj('AuthService', ['getBusinessUnitId']);

    await TestBed.configureTestingModule({
      imports: [DashboardComponent, CommonModule],
      providers: [
        { provide: DashboardService, useValue: dashboardSpy },
        { provide: AuthService, useValue: authSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(DashboardComponent);
    component = fixture.componentInstance;
    dashboardService = TestBed.inject(DashboardService) as jasmine.SpyObj<DashboardService>;
    authService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
  });

  beforeEach(() => {
    spyOn(console, 'log');
    spyOn(console, 'error');

    // Setup default return values
    authService.getBusinessUnitId.and.returnValue('1');
    dashboardService.getDashboard.and.returnValue(Promise.resolve(mockDashboardData));
    dashboardService.getTourObservationCount.and.returnValue(Promise.resolve(mockTourObservationData));
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Component Initialization', () => {
    it('should initialize with default card values', () => {
      expect(component.cards).toBeDefined();
      expect(component.cards.length).toBe(4);
      expect(component.cards[0].title).toBe('User');
      expect(component.cards[1].title).toBe('Tour');
      expect(component.cards[2].title).toBe('Incident');
      expect(component.cards[3].title).toBe('Observation');
    });

    it('should initialize with zero counts', () => {
      component.cards.forEach(card => {
        expect(card.count).toBe(0);
      });
    });

    it('should have correct card colors', () => {
      expect(component.cards[0].color).toBe('#522ACC'); // User - Purple
      expect(component.cards[1].color).toBe('#FDA02C'); // Tour - Orange
      expect(component.cards[2].color).toBe('#FB6EA4'); // Incident - Pink
      expect(component.cards[3].color).toBe('#046332'); // Observation - Green
    });

    it('should have correct card icons', () => {
      expect(component.cards[0].icon).toBe('assets/img/icons/user.png');
      expect(component.cards[1].icon).toBe('assets/img/icons/tour.png');
      expect(component.cards[2].icon).toBe('assets/img/icons/incident.png');
      expect(component.cards[3].icon).toBe('assets/img/icons/observation.png');
    });
  });

  describe('ngOnInit', () => {
    it('should call getBusinessUnitId on initialization', () => {
      component.ngOnInit();
      expect(authService.getBusinessUnitId).toHaveBeenCalled();
    });

    it('should set businessUnitId from auth service', () => {
      authService.getBusinessUnitId.and.returnValue('2');
      component.ngOnInit();
      expect(component.businessUnitId).toBe('2');
    });

    it('should call getDashboardData on initialization', async () => {
      spyOn(component, 'getDashboardData');
      component.ngOnInit();
      expect(component.getDashboardData).toHaveBeenCalled();
    });

    it('should call getTourObservationCount on initialization', async () => {
      spyOn(component, 'getTourObservationCount');
      component.ngOnInit();
      expect(component.getTourObservationCount).toHaveBeenCalled();
    });
  });

  describe('getDashboardData', () => {
    it('should fetch dashboard data successfully', async () => {
      await component.getDashboardData();

      expect(dashboardService.getDashboard).toHaveBeenCalled();
      expect(component.dashboardList).toEqual(mockDashboardData);
    });

    it('should include businessUnitId in request when available', async () => {
      component.businessUnitId = '1';
      await component.getDashboardData();

      const callArgs = dashboardService.getDashboard.calls.mostRecent().args[0];
      expect(callArgs.params.businessUnitId).toBe('1');
    });

    it('should call mapCardData after fetching data', async () => {
      spyOn(component, 'mapCardData');
      await component.getDashboardData();

      expect(component.mapCardData).toHaveBeenCalledWith(mockDashboardData);
    });

    it('should handle API errors gracefully', async () => {
      const error = new Error('API Error');
      dashboardService.getDashboard.and.returnValue(Promise.reject(error));

      await expectAsync(component.getDashboardData()).toBeRejected();
    });
  });

  describe('getTourObservationCount', () => {
    it('should fetch tour observation count successfully', async () => {
      await component.getTourObservationCount();

      expect(dashboardService.getTourObservationCount).toHaveBeenCalled();
      expect(component.tourObservationCount).toEqual(mockTourObservationData);
    });

    it('should include businessUnitId in request when available', async () => {
      component.businessUnitId = '1';
      await component.getTourObservationCount();

      const callArgs = dashboardService.getTourObservationCount.calls.mostRecent().args[0];
      expect(callArgs.params.businessUnitId).toBe('1');
    });

    it('should handle API errors gracefully', async () => {
      const error = new Error('API Error');
      dashboardService.getTourObservationCount.and.returnValue(Promise.reject(error));

      await expectAsync(component.getTourObservationCount()).toBeRejected();
    });
  });

  describe('mapCardData', () => {
    it('should map user count correctly', () => {
      const testData = { user: 100 };
      component.mapCardData(testData);
      expect(component.cards[0].count).toBe(100);
    });

    it('should map tour count correctly', () => {
      const testData = { tour: 200 };
      component.mapCardData(testData);
      expect(component.cards[1].count).toBe(200);
    });

    it('should map incident count correctly', () => {
      const testData = { incident: 50 };
      component.mapCardData(testData);
      expect(component.cards[2].count).toBe(50);
    });

    it('should map observation count correctly', () => {
      const testData = { observation: 350 };
      component.mapCardData(testData);
      expect(component.cards[3].count).toBe(350);
    });

    it('should handle partial data mapping', () => {
      const partialData = { user: 75, observation: 125 };
      component.mapCardData(partialData);

      expect(component.cards[0].count).toBe(75);
      expect(component.cards[1].count).toBe(0); // Should remain unchanged
      expect(component.cards[2].count).toBe(0); // Should remain unchanged
      expect(component.cards[3].count).toBe(125);
    });

    it('should handle empty data object', () => {
      const originalCounts = component.cards.map(card => card.count);
      component.mapCardData({});

      // Counts should remain unchanged
      component.cards.forEach((card, index) => {
        expect(card.count).toBe(originalCounts[index]);
      });
    });

    it('should handle null data', () => {
      const originalCounts = component.cards.map(card => card.count);
      component.mapCardData(null);

      // Counts should remain unchanged
      component.cards.forEach((card, index) => {
        expect(card.count).toBe(originalCounts[index]);
      });
    });
  });
});
