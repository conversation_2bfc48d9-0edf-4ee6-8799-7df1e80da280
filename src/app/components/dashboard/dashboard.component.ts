import { Component, OnInit } from '@angular/core';
import { CountDashboardComponent } from "./count-dashboard/count-dashboard.component";
import { CommonModule } from '@angular/common';
import { createAxiosConfig } from '../../core/utilities/axios-param-config';
import { DashboardService } from '../../services/dashboard/dashboard.service'
import { AuthService } from '../../services/auth.service'; // Assuming AuthService path
import { ClusterWiseGraphComponent } from './cluster-wise-graph/cluster-wise-graph.component';
import { ClusterWiseStatusComponent } from './cluster-wise-status/cluster-wise-status.component';
import { TopFindAndFixedItComponent } from './top-find-and-fixed-it/top-find-and-fixed-it.component';
import { ObservationStatusComponent } from './observation-status/observation-status.component';
import { ClusterWiseBogTourComponent } from './cluster-wise-bog-tour/cluster-wise-bog-tour.component';
import { DashboardBubbleChartComponent } from './dashboard-bubble-chart/dashboard-bubble-chart.component';
// import { DashboardBubbleChartComponent } from './dashboard-bubble-chart/dashboard-bubble-chart.component';
interface DashboardCard {
  icon: string;
  title: string;
  count: number;
  total: number;
  color: string;
}

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CountDashboardComponent,
    CommonModule,
    ClusterWiseGraphComponent,
    ClusterWiseStatusComponent,
    TopFindAndFixedItComponent,
    ObservationStatusComponent,
    ClusterWiseBogTourComponent,
    DashboardBubbleChartComponent
  ],
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.scss'
})
export class DashboardComponent implements OnInit {
  cards: DashboardCard[] = [
    {
      icon: 'assets/img/icons/user.png',
      title: 'User',
      count: 0,
      total: 5000,
      color: '#522ACC'
    },
    {
      icon: 'assets/img/icons/tour.png',
      title: 'Tour',
      count: 0,
      total: 100000,
      color: '#FDA02C'
    },
    {
      icon: 'assets/img/icons/incident.png',
      title: 'Incident',
      count: 0,
      total: 5000,
      color: '#FB6EA4'
    },
    // Enable it again after further discussion
    // {
    //   icon: 'assets/img/icons/qr-scan.png',
    //   title: 'Total No. QR',
    //   count: 0,
    //   total: 100000,
    //   color: '#116AAD'
    // },
    {
      icon: 'assets/img/icons/observation.png',
      title: 'Observation',
      count: 0,
      total: 100000,
      color: '#046332'
    }
  ];
  dashboardList: any = [];
  tourObservationCount: any = [];
  businessUnitId: string | null = null;

  constructor(
    readonly dashboardService: DashboardService,
    private authService: AuthService // Inject AuthService
  ) { }

  ngOnInit() {
    this.businessUnitId = this.authService.getBusinessUnitId();
    this.getDashboardData();
    this.getTourObservationCount();
  }

  async getTourObservationCount() {
    const body: any = {
      page: 1,
      limit: 10,
      sort: 'title,ASC',
      filter: ['enabled||eq||true']
    }
    if (this.businessUnitId) {
      body.businessUnitId = this.businessUnitId;
    }
    const params = createAxiosConfig(body);
    const response = await this.dashboardService.getTourObservationCount(params);
    this.tourObservationCount = response;

  }

  async getDashboardData() {
    const body: any = {
      page: 1,
      limit: 10,
      sort: 'title,ASC',
      filter: ['enabled||eq||true']
    }
    if (this.businessUnitId) {
      body.businessUnitId = this.businessUnitId;
    }
    const params = createAxiosConfig(body);
    const response = await this.dashboardService.getDashboard(params);
    this.dashboardList = response;

    this.mapCardData(this.dashboardList);
  }
  mapCardData(data: any) {

    if (data?.user) {
      this.cards[0].count = data?.user;
    }
    if (data?.tour) {
      this.cards[1].count = data?.tour;
    }
    if (data?.incident) {
      this.cards[2].count = data?.incident;
    }
    // if (data?.noOfScan) {
    //   this.cards[3].count = data?.noOfScan;
    // }
    if (data?.observation) {
      this.cards[3].count = data?.observation;
    }
  }
}
