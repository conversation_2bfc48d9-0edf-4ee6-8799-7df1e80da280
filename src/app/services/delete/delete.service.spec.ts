import { TestBed } from '@angular/core/testing';
import { DeleteService } from './delete.service';
import { ApiService } from '../api.service';

describe('DeleteService', () => {
  let service: DeleteService;
  let apiService: jasmine.SpyObj<ApiService>;

  beforeEach(() => {
    const apiSpy = jasmine.createSpyObj('ApiService', ['postData']);

    TestBed.configureTestingModule({
      providers: [
        DeleteService,
        { provide: ApiService, useValue: apiSpy }
      ]
    });

    service = TestBed.inject(DeleteService);
    apiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
  });

  beforeEach(() => {
    spyOn(console, 'log');
    spyOn(console, 'error');
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('deleteData', () => {
    it('should delete data successfully', async () => {
      // Arrange
      const deleteData = {
        tableName: 'admins',
        id: 1,
        isDeleted: true,
        deletedBy: 2
      };
      const mockResponse = {
        responseCode: 200,
        message: 'Record deleted successfully',
        data: { id: 1, isDeleted: true }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.deleteData(deleteData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), deleteData);
      expect(result).toEqual(mockResponse);
    });

    it('should handle soft delete operation', async () => {
      // Arrange
      const softDeleteData = {
        tableName: 'plants',
        id: 5,
        isDeleted: true,
        enabled: false,
        deletedBy: 1,
        deletedAt: new Date().toISOString()
      };
      const mockResponse = {
        responseCode: 200,
        message: 'Plant soft deleted successfully'
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.deleteData(softDeleteData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), softDeleteData);
      expect(result).toEqual(mockResponse);
    });

    it('should handle bulk delete operation', async () => {
      // Arrange
      const bulkDeleteData = {
        tableName: 'qr_codes',
        ids: [1, 2, 3, 4, 5],
        isDeleted: true,
        deletedBy: 1
      };
      const mockResponse = {
        responseCode: 200,
        message: '5 records deleted successfully',
        data: { deletedCount: 5 }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.deleteData(bulkDeleteData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), bulkDeleteData);
      expect(result).toEqual(mockResponse);
    });

    it('should handle delete errors', async () => {
      // Arrange
      const deleteData = { tableName: 'admins', id: 999 };
      const error = new Error('Record not found');
      apiService.postData.and.returnValue(Promise.reject(error));

      // Act & Assert
      await expectAsync(service.deleteData(deleteData)).toBeRejectedWith(error);
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), deleteData);
    });

    it('should handle validation errors', async () => {
      // Arrange
      const invalidDeleteData = { tableName: '', id: null };
      const validationError = {
        responseCode: 400,
        message: 'Validation failed',
        errors: ['Table name is required', 'ID is required']
      };
      apiService.postData.and.returnValue(Promise.reject(validationError));

      // Act & Assert
      await expectAsync(service.deleteData(invalidDeleteData)).toBeRejectedWith(validationError);
    });

    it('should handle permission errors', async () => {
      // Arrange
      const deleteData = { tableName: 'admins', id: 1, deletedBy: 999 };
      const permissionError = {
        responseCode: 403,
        message: 'Insufficient permissions to delete this record'
      };
      apiService.postData.and.returnValue(Promise.reject(permissionError));

      // Act & Assert
      await expectAsync(service.deleteData(deleteData)).toBeRejectedWith(permissionError);
    });

    it('should handle cascade delete operation', async () => {
      // Arrange
      const cascadeDeleteData = {
        tableName: 'plants',
        id: 1,
        isDeleted: true,
        cascade: true,
        deletedBy: 1,
        relatedTables: ['zones', 'qr_codes', 'tours']
      };
      const mockResponse = {
        responseCode: 200,
        message: 'Plant and related records deleted successfully',
        data: {
          deletedPlant: 1,
          deletedZones: 5,
          deletedQrCodes: 20,
          deletedTours: 10
        }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.deleteData(cascadeDeleteData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), cascadeDeleteData);
      expect(result).toEqual(mockResponse);
    });

    it('should handle empty data', async () => {
      // Arrange
      const emptyData = {};
      const mockResponse = {
        responseCode: 400,
        message: 'No data provided for deletion'
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.deleteData(emptyData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), emptyData);
      expect(result).toEqual(mockResponse);
    });

    it('should handle null data', async () => {
      // Arrange
      const nullData = null;
      const mockResponse = {
        responseCode: 400,
        message: 'Invalid data provided'
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.deleteData(nullData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), nullData);
      expect(result).toEqual(mockResponse);
    });
  });
});
