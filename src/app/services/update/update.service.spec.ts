import { TestBed } from '@angular/core/testing';
import { UpdateService } from './update.service';
import { ApiService } from '../api.service';

describe('UpdateService', () => {
  let service: UpdateService;
  let apiService: jasmine.SpyObj<ApiService>;

  beforeEach(() => {
    const apiSpy = jasmine.createSpyObj('ApiService', ['postData']);

    TestBed.configureTestingModule({
      providers: [
        UpdateService,
        { provide: ApiService, useValue: apiSpy }
      ]
    });

    service = TestBed.inject(UpdateService);
    apiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
  });

  beforeEach(() => {
    spyOn(console, 'log');
    spyOn(console, 'error');
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('update', () => {
    it('should update record successfully', async () => {
      // Arrange
      const updateData = {
        tableName: 'admins',
        id: 1,
        data: {
          firstName: 'Updated Name',
          email: '<EMAIL>'
        }
      };

      const mockResponse = {
        responseCode: 200,
        message: 'Record updated successfully',
        data: {
          id: 1,
          firstName: 'Updated Name',
          email: '<EMAIL>'
        }
      };

      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.update(updateData);

      // Assert
      expect(console.log).toHaveBeenCalledWith('Update service called with data:', jasmine.any(String));
      expect(apiService.postData).toHaveBeenCalledWith('/update', updateData);
      expect(result).toEqual(mockResponse);
    });

    it('should handle update errors', async () => {
      // Arrange
      const updateData = {
        tableName: 'admins',
        id: 999,
        data: { firstName: 'Test' }
      };

      const error = new Error('Record not found');
      apiService.postData.and.returnValue(Promise.reject(error));

      // Act & Assert
      await expectAsync(service.update(updateData)).toBeRejectedWith(error);
      expect(console.log).toHaveBeenCalledWith('Update service called with data:', jasmine.any(String));
      expect(apiService.postData).toHaveBeenCalledWith('/update', updateData);
    });

    it('should log update data correctly', async () => {
      // Arrange
      const updateData = {
        tableName: 'plants',
        id: 5,
        data: {
          name: 'Updated Plant Name',
          location: 'New Location'
        }
      };

      const mockResponse = { responseCode: 200, message: 'Success' };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      await service.update(updateData);

      // Assert
      expect(console.log).toHaveBeenCalledWith(
        'Update service called with data:',
        JSON.stringify(updateData, null, 2)
      );
    });

    it('should handle empty update data', async () => {
      // Arrange
      const updateData = {
        tableName: 'admins',
        id: 1,
        data: {}
      };

      const mockResponse = {
        responseCode: 400,
        message: 'No data to update'
      };

      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.update(updateData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith('/update', updateData);
      expect(result).toEqual(mockResponse);
    });

    it('should handle null data', async () => {
      // Arrange
      const updateData = null;
      const mockResponse = {
        responseCode: 400,
        message: 'Invalid data'
      };

      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.update(updateData);

      // Assert
      expect(console.log).toHaveBeenCalledWith('Update service called with data:', 'null');
      expect(apiService.postData).toHaveBeenCalledWith('/update', updateData);
      expect(result).toEqual(mockResponse);
    });

    it('should handle complex nested data', async () => {
      // Arrange
      const updateData = {
        tableName: 'admins',
        id: 1,
        data: {
          profile: {
            personal: {
              firstName: 'John',
              lastName: 'Doe'
            },
            contact: {
              email: '<EMAIL>',
              phone: '1234567890'
            }
          },
          permissions: ['read', 'write', 'delete']
        }
      };

      const mockResponse = {
        responseCode: 200,
        message: 'Complex data updated successfully'
      };

      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.update(updateData);

      // Assert
      expect(console.log).toHaveBeenCalledWith(
        'Update service called with data:',
        JSON.stringify(updateData, null, 2)
      );
      expect(apiService.postData).toHaveBeenCalledWith('/update', updateData);
      expect(result).toEqual(mockResponse);
    });
  });
});
