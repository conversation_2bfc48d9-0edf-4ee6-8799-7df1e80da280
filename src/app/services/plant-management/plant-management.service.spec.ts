import { TestBed } from '@angular/core/testing';
import { PlantManagementService } from './plant-management.service';
import { ApiService } from '../api.service';

describe('PlantManagementService', () => {
  let service: PlantManagementService;
  let apiService: jasmine.SpyObj<ApiService>;

  const mockPlantResponse = {
    data: [
      {
        id: 1,
        name: 'Test Plant',
        location: 'Test Location',
        enabled: true,
        businessUnitId: 0
      }
    ],
    total: 1,
    page: 1,
    limit: 10
  };

  beforeEach(() => {
    const apiSpy = jasmine.createSpyObj('ApiService', ['getData', 'postData']);

    TestBed.configureTestingModule({
      providers: [
        PlantManagementService,
        { provide: ApiService, useValue: apiSpy }
      ]
    });

    service = TestBed.inject(PlantManagementService);
    apiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
  });

  beforeEach(() => {
    spyOn(console, 'log');
    spyOn(console, 'error');
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('trimStringFields', () => {
    it('should trim string values in simple object', () => {
      // Arrange
      const data = {
        name: '  Test Plant  ',
        location: '  Test Location  ',
        id: 1
      };

      // Act
      const result = (service as any).trimStringFields(data);

      // Assert
      expect(result.name).toBe('Test Plant');
      expect(result.location).toBe('Test Location');
      expect(result.id).toBe(1);
    });

    it('should trim string values in nested objects', () => {
      // Arrange
      const data = {
        plant: {
          name: '  Nested Plant  ',
          details: {
            description: '  Nested Description  '
          }
        },
        count: 5
      };

      // Act
      const result = (service as any).trimStringFields(data);

      // Assert
      expect(result.plant.name).toBe('Nested Plant');
      expect(result.plant.details.description).toBe('Nested Description');
      expect(result.count).toBe(5);
    });

    it('should handle arrays of objects', () => {
      // Arrange
      const data = [
        { name: '  Plant 1  ', id: 1 },
        { name: '  Plant 2  ', id: 2 }
      ];

      // Act
      const result = (service as any).trimStringFields(data);

      // Assert
      expect(result[0].name).toBe('Plant 1');
      expect(result[1].name).toBe('Plant 2');
      expect(result[0].id).toBe(1);
      expect(result[1].id).toBe(2);
    });

    it('should handle null and undefined values', () => {
      // Act & Assert
      expect((service as any).trimStringFields(null)).toBeNull();
      expect((service as any).trimStringFields(undefined)).toBeUndefined();
      expect((service as any).trimStringFields('test')).toBe('test');
    });

    it('should handle empty objects and arrays', () => {
      // Act & Assert
      expect((service as any).trimStringFields({})).toEqual({});
      expect((service as any).trimStringFields([])).toEqual([]);
    });

    it('should preserve non-string values', () => {
      // Arrange
      const data = {
        number: 123,
        boolean: true,
        nullValue: null,
        undefinedValue: undefined
      };

      // Act
      const result = (service as any).trimStringFields(data);

      // Assert
      expect(result.number).toBe(123);
      expect(result.boolean).toBe(true);
      expect(result.nullValue).toBeNull();
      expect(result.undefinedValue).toBeUndefined();
    });

    it('should handle Date objects correctly', () => {
      // Arrange
      const originalDate = new Date('2024-01-01');
      const data = {
        date: originalDate,
        name: '  test  '
      };

      // Act
      const result = (service as any).trimStringFields(data);

      // Assert
      expect(result.name).toBe('test');
      // Date objects are treated as objects and get spread, so they become empty objects
      expect(typeof result.date).toBe('object');
    });
  });

  describe('getPlants', () => {
    it('should fetch plants successfully', async () => {
      // Arrange
      const params = { page: 1, limit: 10 };
      apiService.getData.and.returnValue(Promise.resolve(mockPlantResponse));

      // Act
      const result = await service.getPlants(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(mockPlantResponse);
    });

    it('should trim filter values in params', async () => {
      // Arrange
      const params = {
        params: {
          filter: [
            'name||like||  Test Plant  ',
            'location||eq||  Test Location  ',
            'enabled||eq||true'
          ]
        }
      };
      apiService.getData.and.returnValue(Promise.resolve(mockPlantResponse));

      // Act
      await service.getPlants(params);

      // Assert
      expect(params.params.filter[0]).toBe('name||like||Test Plant');
      expect(params.params.filter[1]).toBe('location||eq||Test Location');
      expect(params.params.filter[2]).toBe('enabled||eq||true');
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
    });

    it('should handle params without filters', async () => {
      // Arrange
      const params = { page: 1, limit: 10 };
      apiService.getData.and.returnValue(Promise.resolve(mockPlantResponse));

      // Act
      const result = await service.getPlants(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(mockPlantResponse);
    });

    it('should handle errors when fetching plants', async () => {
      // Arrange
      const params = { page: 1, limit: 10 };
      const error = new Error('Network Error');
      apiService.getData.and.returnValue(Promise.reject(error));

      // Act & Assert
      await expectAsync(service.getPlants(params)).toBeRejectedWith(error);
    });
  });

  describe('getPlantAdmin', () => {
    it('should fetch plant admin successfully', async () => {
      // Arrange
      const data = { plantId: 1, role: '  admin  ' };
      const mockAdminResponse = { data: [{ id: 1, name: 'Admin User' }] };
      apiService.postData.and.returnValue(Promise.resolve(mockAdminResponse));

      // Act
      const result = await service.getPlantAdmin(data);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(
        jasmine.any(String),
        { plantId: 1, role: 'admin' }
      );
      expect(result).toEqual(mockAdminResponse);
    });

    it('should handle errors when fetching plant admin', async () => {
      // Arrange
      const data = { plantId: 999 };
      const error = new Error('Plant not found');
      apiService.postData.and.returnValue(Promise.reject(error));

      // Act & Assert
      await expectAsync(service.getPlantAdmin(data)).toBeRejectedWith(error);
    });
  });

  describe('createPlant', () => {
    it('should create plant successfully', async () => {
      // Arrange
      const plantData = {
        name: '  New Plant  ',
        location: '  New Location  ',
        enabled: true
      };
      const mockCreateResponse = {
        responseCode: 200,
        message: 'Plant created successfully',
        data: { id: 2, name: 'New Plant', location: 'New Location' }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockCreateResponse));

      // Act
      const result = await service.createPlant(plantData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(
        jasmine.any(String),
        { name: 'New Plant', location: 'New Location', enabled: true }
      );
      expect(result).toEqual(mockCreateResponse);
    });

    it('should handle validation errors when creating plant', async () => {
      // Arrange
      const invalidPlantData = { name: '', location: '' };
      const validationError = {
        responseCode: 400,
        message: 'Validation failed',
        errors: ['Name is required', 'Location is required']
      };
      apiService.postData.and.returnValue(Promise.reject(validationError));

      // Act & Assert
      await expectAsync(service.createPlant(invalidPlantData)).toBeRejectedWith(validationError);
    });
  });

  describe('exportPlantQr', () => {
    it('should export plant QR code successfully', async () => {
      // Arrange
      const qrData = {
        plantId: 1,
        format: '  PDF  ',
        includeDetails: true
      };
      const mockQrResponse = {
        responseCode: 200,
        message: 'QR code generated successfully',
        data: { url: 'https://example.com/qr-code.pdf' }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockQrResponse));

      // Act
      const result = await service.exportPlantQr(qrData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(
        jasmine.any(String),
        { plantId: 1, format: 'PDF', includeDetails: true }
      );
      expect(result).toEqual(mockQrResponse);
    });

    it('should handle errors when exporting QR code', async () => {
      // Arrange
      const qrData = { plantId: 999 };
      const error = new Error('Plant not found for QR generation');
      apiService.postData.and.returnValue(Promise.reject(error));

      // Act & Assert
      await expectAsync(service.exportPlantQr(qrData)).toBeRejectedWith(error);
    });
  });

  describe('requestPlantTransfer', () => {
    it('should request plant transfer successfully', async () => {
      // Arrange
      const transferData = {
        fromPlantId: 1,
        toPlantId: 2,
        userId: 5,
        reason: '  Transfer for better opportunity  ',
        requestedBy: 1
      };
      const mockTransferResponse = {
        responseCode: 200,
        message: 'Transfer request submitted successfully',
        data: { requestId: 123, status: 'pending' }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockTransferResponse));

      // Act
      const result = await service.requestPlantTransfer(transferData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(
        jasmine.any(String),
        {
          fromPlantId: 1,
          toPlantId: 2,
          userId: 5,
          reason: 'Transfer for better opportunity',
          requestedBy: 1
        }
      );
      expect(result).toEqual(mockTransferResponse);
    });

    it('should handle errors when requesting plant transfer', async () => {
      // Arrange
      const transferData = { fromPlantId: 1, toPlantId: 1 }; // Same plant transfer
      const error = new Error('Cannot transfer to the same plant');
      apiService.postData.and.returnValue(Promise.reject(error));

      // Act & Assert
      await expectAsync(service.requestPlantTransfer(transferData)).toBeRejectedWith(error);
    });
  });
});
