import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { AuthService } from '../auth.service';
import { ENDPOINTS } from '../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class DigisafeService {

  constructor(readonly apiService: ApiService, private authService: AuthService) { }

  async getDigisafe(params: any) {
    return await this.apiService.getData(ENDPOINTS.DIGISAFE.GET_DIGISAFE, params);
  }

  async getMisReport(params: any) {
    return await this.apiService.getData(ENDPOINTS.DIGISAFE.MIS_REPORT, params);
  }

  async createDigisafe(data: any) {
    return await this.apiService.postData(ENDPOINTS.DIGISAFE.CREATE, data);
  }

  async getMisCountData(config: any) {
    const businessUnitId = this.authService.getBusinessUnitId();
    const finalConfig = { ...config, params: { ...config.params, businessUnitId: businessUnitId } };
    return await this.apiService.getData(ENDPOINTS.DIGISAFE.MIS_COUNT, finalConfig);
  }

  async getGmrCount(params: any): Promise<any> {
    const businessUnitId = this.authService.getBusinessUnitId();
    const finalParams = { ...params, params: { ...params.params, businessUnitId: businessUnitId } };
    return await this.apiService.getData(ENDPOINTS.DIGISAFE.GMR_COUNT, finalParams);
  }
}
