import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { ENDPOINTS } from '../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class AddDjpService {

  constructor(readonly apiService: ApiService) { }

  async createDjp(data: any) {
    return await this.apiService.postData(ENDPOINTS.ADD_DJP.CREATE_DJP, data);
  }

  async getDjp(param: any) {
    return await this.apiService.getData(ENDPOINTS.ADD_DJP.GET_DJP, param);
  }

  async bulkUploadExcel(data: any) {
    return await this.apiService.postData(ENDPOINTS.ADD_DJP.BULK_UPLOAD_EXCEL, data);
  }

  async bulkUploadValidRecords(data: any) {
    return await this.apiService.postData(ENDPOINTS.ADD_DJP.VALID_RECORDS, data);
  }

  async djpComplete(data: any) {
    return await this.apiService.postData(ENDPOINTS.ADD_DJP.DJP_COMPLETE, data);
  }

  async updateDjp(data: any) {
    return await this.apiService.postData(ENDPOINTS.ADD_DJP.UPDATE_DJP, data);
  }
}
