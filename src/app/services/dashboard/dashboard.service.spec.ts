import { TestBed } from '@angular/core/testing';
import { DashboardService } from './dashboard.service';
import { ApiService } from '../api.service';

describe('DashboardService', () => {
  let service: DashboardService;
  let apiService: jasmine.SpyObj<ApiService>;

  beforeEach(() => {
    const apiSpy = jasmine.createSpyObj('ApiService', ['getData', 'postData']);

    TestBed.configureTestingModule({
      providers: [
        DashboardService,
        { provide: ApiService, useValue: apiSpy }
      ]
    });

    service = TestBed.inject(DashboardService);
    apiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
  });

  beforeEach(() => {
    spyOn(console, 'log');
    spyOn(console, 'error');
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getDashboard', () => {
    it('should fetch dashboard data successfully', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const mockResponse = {
        responseCode: 200,
        user: 150,
        tour: 250,
        incident: 75,
        observation: 300,
        activatedQr: 180,
        deActivatedQr: 45,
        unregisterdQr: 25,
        noOfScan: 500
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getDashboard(mockParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), mockParams);
      expect(result).toEqual(mockResponse);
    });

    it('should fetch dashboard data without parameters', async () => {
      // Arrange
      const mockResponse = {
        responseCode: 200,
        user: 0,
        tour: 0,
        incident: 0,
        observation: 0
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getDashboard();

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), undefined);
      expect(result).toEqual(mockResponse);
    });

    it('should handle API errors when fetching dashboard data', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const mockError = new Error('Network Error');
      apiService.getData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.getDashboard(mockParams)).toBeRejectedWith(mockError);
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), mockParams);
    });

    it('should handle empty response data', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const emptyResponse = {
        responseCode: 200,
        user: 0,
        tour: 0,
        incident: 0,
        observation: 0
      };
      apiService.getData.and.returnValue(Promise.resolve(emptyResponse));

      // Act
      const result = await service.getDashboard(mockParams);

      // Assert
      expect(result).toEqual(emptyResponse);
      expect(result.user).toBe(0);
      expect(result.tour).toBe(0);
    });
  });

  describe('getClusterWiseGraph', () => {
    it('should fetch cluster wise graph data successfully', async () => {
      // Arrange
      const mockParams = {
        startDate: '2024-01-01',
        endDate: '2024-12-31',
        businessUnitId: 1
      };
      const mockResponse = {
        responseCode: 200,
        data: [
          { cluster: 'Cluster A', observations: 150, tours: 200 },
          { cluster: 'Cluster B', observations: 120, tours: 180 }
        ]
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getClusterWiseGraph(mockParams);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), mockParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle API errors when fetching cluster wise graph', async () => {
      // Arrange
      const mockParams = { startDate: '2024-01-01', endDate: '2024-12-31' };
      const mockError = new Error('Unauthorized');
      apiService.postData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.getClusterWiseGraph(mockParams)).toBeRejectedWith(mockError);
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), mockParams);
    });

    it('should handle date range parameters', async () => {
      // Arrange
      const dateRangeParams = {
        startDate: '2024-06-01',
        endDate: '2024-06-30',
        clusterId: 5
      };
      const mockResponse = {
        responseCode: 200,
        data: [{ cluster: 'June Data', observations: 75, tours: 100 }]
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getClusterWiseGraph(dateRangeParams);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), dateRangeParams);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('getClusterWiseStatusGraph', () => {
    it('should fetch cluster wise status graph data successfully', async () => {
      // Arrange
      const mockParams = {
        startDate: '2024-01-01',
        endDate: '2024-12-31',
        businessUnitId: 1
      };
      const mockResponse = {
        responseCode: 200,
        data: [
          { cluster: 'Cluster A', completed: 80, pending: 20, inProgress: 15 },
          { cluster: 'Cluster B', completed: 90, pending: 10, inProgress: 5 }
        ]
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getClusterWiseStatusGraph(mockParams);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), mockParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle API errors when fetching cluster wise status', async () => {
      // Arrange
      const mockParams = { startDate: '2024-01-01', endDate: '2024-12-31' };
      const mockError = new Error('Server Error');
      apiService.postData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.getClusterWiseStatusGraph(mockParams)).toBeRejectedWith(mockError);
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), mockParams);
    });
  });

  describe('getTourObservationCount', () => {
    it('should fetch tour observation count successfully', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10, businessUnitId: 1 };
      const mockResponse = {
        responseCode: 200,
        totalTours: 250,
        totalObservations: 300,
        completedTours: 200,
        pendingObservations: 50
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getTourObservationCount(mockParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), mockParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle API errors when fetching tour observation count', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const mockError = new Error('Network Timeout');
      apiService.getData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.getTourObservationCount(mockParams)).toBeRejectedWith(mockError);
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), mockParams);
    });
  });

  describe('getPlantTourCount', () => {
    it('should fetch plant tour count successfully', async () => {
      // Arrange
      const mockParams = {
        plantIds: [1, 2, 3],
        startDate: '2024-01-01',
        endDate: '2024-12-31'
      };
      const mockResponse = {
        responseCode: 200,
        data: [
          { plantId: 1, plantName: 'Plant A', tourCount: 150 },
          { plantId: 2, plantName: 'Plant B', tourCount: 120 },
          { plantId: 3, plantName: 'Plant C', tourCount: 180 }
        ]
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getPlantTourCount(mockParams);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), mockParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle API errors when fetching plant tour count', async () => {
      // Arrange
      const mockParams = { plantIds: [1, 2] };
      const mockError = new Error('Forbidden');
      apiService.postData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.getPlantTourCount(mockParams)).toBeRejectedWith(mockError);
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), mockParams);
    });

    it('should handle empty plant IDs array', async () => {
      // Arrange
      const mockParams = { plantIds: [] };
      const mockResponse = {
        responseCode: 200,
        data: []
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getPlantTourCount(mockParams);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), mockParams);
      expect(result.data).toEqual([]);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle network timeout errors', async () => {
      // Arrange
      const timeoutError = new Error('Request timeout');
      timeoutError.name = 'TimeoutError';
      apiService.getData.and.returnValue(Promise.reject(timeoutError));

      // Act & Assert
      await expectAsync(service.getDashboard()).toBeRejectedWith(timeoutError);
    });

    it('should handle server errors (500)', async () => {
      // Arrange
      const serverError = new Error('Internal Server Error');
      serverError.name = 'ServerError';
      apiService.postData.and.returnValue(Promise.reject(serverError));

      // Act & Assert
      await expectAsync(service.getClusterWiseGraph({})).toBeRejectedWith(serverError);
    });

    it('should handle malformed response data', async () => {
      // Arrange
      const malformedResponse = { invalidStructure: true };
      apiService.getData.and.returnValue(Promise.resolve(malformedResponse));

      // Act
      const result = await service.getDashboard();

      // Assert
      expect(result).toEqual(malformedResponse);
    });
  });
});
