import { TestBed } from '@angular/core/testing';
import { UploadService } from './upload.service';
import { ApiService } from '../api.service';

describe('UploadService', () => {
  let service: UploadService;
  let apiService: jasmine.SpyObj<ApiService>;

  beforeEach(() => {
    const apiSpy = jasmine.createSpyObj('ApiService', ['postData']);

    TestBed.configureTestingModule({
      providers: [
        UploadService,
        { provide: ApiService, useValue: apiSpy }
      ]
    });

    service = TestBed.inject(UploadService);
    apiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
  });

  beforeEach(() => {
    spyOn(console, 'log');
    spyOn(console, 'error');
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('upload', () => {
    it('should upload file successfully', async () => {
      // Arrange
      const mockFile = new File(['test content'], 'test.jpg', { type: 'image/jpeg' });
      const formData = new FormData();
      formData.append('file', mockFile);

      const mockResponse = {
        responseCode: 200,
        message: 'File uploaded successfully',
        data: {
          url: 'https://example.com/uploads/test.jpg',
          filename: 'test.jpg'
        }
      };

      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.upload(formData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), formData);
      expect(result).toEqual(mockResponse);
    });

    it('should handle upload errors', async () => {
      // Arrange
      const mockFile = new File(['test content'], 'test.jpg', { type: 'image/jpeg' });
      const formData = new FormData();
      formData.append('file', mockFile);

      const error = new Error('Upload failed');
      apiService.postData.and.returnValue(Promise.reject(error));

      // Act & Assert
      await expectAsync(service.upload(formData)).toBeRejectedWith(error);
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), formData);
    });

    it('should handle empty FormData', async () => {
      // Arrange
      const emptyFormData = new FormData();
      const mockResponse = {
        responseCode: 400,
        message: 'No file provided'
      };

      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.upload(emptyFormData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), emptyFormData);
      expect(result).toEqual(mockResponse);
    });

    it('should handle large file upload', async () => {
      // Arrange
      const largeContent = 'x'.repeat(10000); // 10KB content
      const mockFile = new File([largeContent], 'large-file.jpg', { type: 'image/jpeg' });
      const formData = new FormData();
      formData.append('file', mockFile);

      const mockResponse = {
        responseCode: 200,
        message: 'Large file uploaded successfully',
        data: {
          url: 'https://example.com/uploads/large-file.jpg',
          filename: 'large-file.jpg',
          size: largeContent.length
        }
      };

      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.upload(formData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), formData);
      expect(result).toEqual(mockResponse);
    });

    it('should handle multiple files in FormData', async () => {
      // Arrange
      const file1 = new File(['content1'], 'file1.jpg', { type: 'image/jpeg' });
      const file2 = new File(['content2'], 'file2.png', { type: 'image/png' });
      const formData = new FormData();
      formData.append('file1', file1);
      formData.append('file2', file2);

      const mockResponse = {
        responseCode: 200,
        message: 'Multiple files uploaded successfully',
        data: {
          files: [
            { url: 'https://example.com/uploads/file1.jpg', filename: 'file1.jpg' },
            { url: 'https://example.com/uploads/file2.png', filename: 'file2.png' }
          ]
        }
      };

      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.upload(formData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), formData);
      expect(result).toEqual(mockResponse);
    });
  });
});
