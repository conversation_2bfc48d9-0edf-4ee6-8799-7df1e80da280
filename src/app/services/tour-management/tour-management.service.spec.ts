import { TestBed } from '@angular/core/testing';
import { TourManagementService } from './tour-management.service';
import { ApiService } from '../api.service';

describe('TourManagementService', () => {
  let service: TourManagementService;
  let apiService: jasmine.SpyObj<ApiService>;

  const mockTourResponse = {
    data: [
      {
        id: 1,
        title: 'Safety Tour',
        description: 'Daily safety inspection tour',
        plantId: 1,
        status: 0,
        createdAt: '2024-01-01T00:00:00Z',
        admin: { id: 1, firstName: 'John', lastName: 'Doe' },
        plant: { id: 1, name: 'Plant Alpha' }
      },
      {
        id: 2,
        title: 'Quality Tour',
        description: 'Quality control inspection',
        plantId: 1,
        status: 1,
        createdAt: '2024-01-02T00:00:00Z',
        admin: { id: 2, firstName: 'Jane', lastName: 'Smith' },
        plant: { id: 1, name: 'Plant Alpha' }
      }
    ],
    total: 2,
    page: 1,
    limit: 10
  };

  const mockScanPointResponse = {
    data: [
      {
        id: 1,
        tourId: 1,
        qrCodeId: 1,
        sequence: 1,
        isCompleted: false,
        scannedAt: null,
        qrCode: { id: 1, qrCode: 'QR123456', zoneName: 'Zone A' }
      },
      {
        id: 2,
        tourId: 1,
        qrCodeId: 2,
        sequence: 2,
        isCompleted: true,
        scannedAt: '2024-01-01T10:30:00Z',
        qrCode: { id: 2, qrCode: 'QR789012', zoneName: 'Zone B' }
      }
    ],
    total: 2,
    page: 1,
    limit: 10
  };

  beforeEach(() => {
    const apiSpy = jasmine.createSpyObj('ApiService', ['getData']);

    TestBed.configureTestingModule({
      providers: [
        TourManagementService,
        { provide: ApiService, useValue: apiSpy }
      ]
    });

    service = TestBed.inject(TourManagementService);
    apiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
  });

  beforeEach(() => {
    spyOn(console, 'log');
    spyOn(console, 'error');
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getTours', () => {
    it('should fetch tours successfully', async () => {
      // Arrange
      const params = { page: 1, limit: 10, status: 0 };
      apiService.getData.and.returnValue(Promise.resolve(mockTourResponse));

      // Act
      const result = await service.getTours(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(mockTourResponse);
    });

    it('should fetch tours with filters', async () => {
      // Arrange
      const params = {
        page: 1,
        limit: 10,
        filter: ['status||eq||0', 'plantId||eq||1'],
        sort: 'createdAt,DESC',
        join: ['admin', 'plant']
      };
      apiService.getData.and.returnValue(Promise.resolve(mockTourResponse));

      // Act
      const result = await service.getTours(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(mockTourResponse);
    });

    it('should handle empty tour response', async () => {
      // Arrange
      const params = { page: 1, limit: 10, plantId: 999 };
      const emptyResponse = { data: [], total: 0, page: 1, limit: 10 };
      apiService.getData.and.returnValue(Promise.resolve(emptyResponse));

      // Act
      const result = await service.getTours(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(emptyResponse);
    });

    it('should handle errors when fetching tours', async () => {
      // Arrange
      const params = { page: 1, limit: 10 };
      const error = new Error('Network Error');
      apiService.getData.and.returnValue(Promise.reject(error));

      // Act & Assert
      await expectAsync(service.getTours(params)).toBeRejectedWith(error);
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
    });

    it('should fetch tours by status', async () => {
      // Arrange
      const params = {
        page: 1,
        limit: 10,
        filter: ['status||eq||1'] // Completed tours
      };
      const completedToursResponse = {
        data: [mockTourResponse.data[1]], // Only completed tour
        total: 1,
        page: 1,
        limit: 10
      };
      apiService.getData.and.returnValue(Promise.resolve(completedToursResponse));

      // Act
      const result = await service.getTours(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(completedToursResponse);
      expect(result.data.length).toBe(1);
      expect(result.data[0].status).toBe(1);
    });

    it('should fetch tours by plant', async () => {
      // Arrange
      const params = {
        page: 1,
        limit: 10,
        plantId: 1,
        filter: ['plantId||eq||1']
      };
      apiService.getData.and.returnValue(Promise.resolve(mockTourResponse));

      // Act
      const result = await service.getTours(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(mockTourResponse);
      expect(result.data.every((tour: any) => tour.plantId === 1)).toBe(true);
    });

    it('should handle null or undefined params', async () => {
      // Arrange
      apiService.getData.and.returnValue(Promise.resolve(mockTourResponse));

      // Act
      const resultWithNull = await service.getTours(null);
      const resultWithUndefined = await service.getTours(undefined);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), null);
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), undefined);
      expect(resultWithNull).toEqual(mockTourResponse);
      expect(resultWithUndefined).toEqual(mockTourResponse);
    });
  });

  describe('getTourScanPoint', () => {
    it('should fetch tour scan points successfully', async () => {
      // Arrange
      const params = { tourId: 1, page: 1, limit: 10 };
      apiService.getData.and.returnValue(Promise.resolve(mockScanPointResponse));

      // Act
      const result = await service.getTourScanPoint(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(mockScanPointResponse);
    });

    it('should fetch scan points with filters', async () => {
      // Arrange
      const params = {
        tourId: 1,
        page: 1,
        limit: 10,
        filter: ['isCompleted||eq||false'],
        sort: 'sequence,ASC',
        join: ['qrCode']
      };
      apiService.getData.and.returnValue(Promise.resolve(mockScanPointResponse));

      // Act
      const result = await service.getTourScanPoint(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(mockScanPointResponse);
    });

    it('should handle empty scan points response', async () => {
      // Arrange
      const params = { tourId: 999, page: 1, limit: 10 };
      const emptyResponse = { data: [], total: 0, page: 1, limit: 10 };
      apiService.getData.and.returnValue(Promise.resolve(emptyResponse));

      // Act
      const result = await service.getTourScanPoint(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(emptyResponse);
    });

    it('should handle errors when fetching scan points', async () => {
      // Arrange
      const params = { tourId: 1, page: 1, limit: 10 };
      const error = new Error('Tour not found');
      apiService.getData.and.returnValue(Promise.reject(error));

      // Act & Assert
      await expectAsync(service.getTourScanPoint(params)).toBeRejectedWith(error);
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
    });

    it('should fetch completed scan points', async () => {
      // Arrange
      const params = {
        tourId: 1,
        page: 1,
        limit: 10,
        filter: ['isCompleted||eq||true']
      };
      const completedScanPointsResponse = {
        data: [mockScanPointResponse.data[1]], // Only completed scan point
        total: 1,
        page: 1,
        limit: 10
      };
      apiService.getData.and.returnValue(Promise.resolve(completedScanPointsResponse));

      // Act
      const result = await service.getTourScanPoint(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(completedScanPointsResponse);
      expect(result.data.length).toBe(1);
      expect(result.data[0].isCompleted).toBe(true);
    });

    it('should fetch pending scan points', async () => {
      // Arrange
      const params = {
        tourId: 1,
        page: 1,
        limit: 10,
        filter: ['isCompleted||eq||false']
      };
      const pendingScanPointsResponse = {
        data: [mockScanPointResponse.data[0]], // Only pending scan point
        total: 1,
        page: 1,
        limit: 10
      };
      apiService.getData.and.returnValue(Promise.resolve(pendingScanPointsResponse));

      // Act
      const result = await service.getTourScanPoint(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(pendingScanPointsResponse);
      expect(result.data.length).toBe(1);
      expect(result.data[0].isCompleted).toBe(false);
    });

    it('should handle null or undefined params', async () => {
      // Arrange
      apiService.getData.and.returnValue(Promise.resolve(mockScanPointResponse));

      // Act
      const resultWithNull = await service.getTourScanPoint(null);
      const resultWithUndefined = await service.getTourScanPoint(undefined);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), null);
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), undefined);
      expect(resultWithNull).toEqual(mockScanPointResponse);
      expect(resultWithUndefined).toEqual(mockScanPointResponse);
    });
  });
});
