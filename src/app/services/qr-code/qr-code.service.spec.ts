import { TestBed } from '@angular/core/testing';
import { QrCodeService } from './qr-code.service';
import { ApiService } from '../api.service';

describe('QrCodeService', () => {
  let service: QrCodeService;
  let apiService: jasmine.SpyObj<ApiService>;

  const mockQrCodeResponse = {
    data: [
      {
        id: 1,
        qrCode: 'QR123456',
        plantId: 1,
        zoneId: 1,
        type: 'permanent',
        enabled: true,
        createdAt: '2024-01-01T00:00:00Z'
      },
      {
        id: 2,
        qrCode: 'QR789012',
        plantId: 1,
        zoneId: 2,
        type: 'temporary',
        enabled: true,
        createdAt: '2024-01-01T00:00:00Z'
      }
    ],
    total: 2,
    page: 1,
    limit: 10
  };

  beforeEach(() => {
    const apiSpy = jasmine.createSpyObj('ApiService', ['getData']);

    TestBed.configureTestingModule({
      providers: [
        QrCodeService,
        { provide: ApiService, useValue: apiSpy }
      ]
    });

    service = TestBed.inject(QrCodeService);
    apiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
  });

  beforeEach(() => {
    spyOn(console, 'log');
    spyOn(console, 'error');
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getQrCode', () => {
    it('should fetch QR codes successfully', async () => {
      // Arrange
      const params = { page: 1, limit: 10, plantId: 1 };
      apiService.getData.and.returnValue(Promise.resolve(mockQrCodeResponse));

      // Act
      const result = await service.getQrCode(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(mockQrCodeResponse);
    });

    it('should fetch QR codes with filters', async () => {
      // Arrange
      const params = {
        page: 1,
        limit: 10,
        filter: ['enabled||eq||true', 'type||eq||permanent'],
        sort: 'createdAt,DESC'
      };
      apiService.getData.and.returnValue(Promise.resolve(mockQrCodeResponse));

      // Act
      const result = await service.getQrCode(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(mockQrCodeResponse);
    });

    it('should handle empty QR code response', async () => {
      // Arrange
      const params = { page: 1, limit: 10, plantId: 999 };
      const emptyResponse = { data: [], total: 0, page: 1, limit: 10 };
      apiService.getData.and.returnValue(Promise.resolve(emptyResponse));

      // Act
      const result = await service.getQrCode(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(emptyResponse);
    });

    it('should handle errors when fetching QR codes', async () => {
      // Arrange
      const params = { page: 1, limit: 10 };
      const error = new Error('Network Error');
      apiService.getData.and.returnValue(Promise.reject(error));

      // Act & Assert
      await expectAsync(service.getQrCode(params)).toBeRejectedWith(error);
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
    });

    it('should fetch QR codes by specific zone', async () => {
      // Arrange
      const params = {
        page: 1,
        limit: 10,
        plantId: 1,
        zoneId: 2,
        filter: ['zoneId||eq||2']
      };
      const zoneSpecificResponse = {
        data: [mockQrCodeResponse.data[1]], // Only QR code for zone 2
        total: 1,
        page: 1,
        limit: 10
      };
      apiService.getData.and.returnValue(Promise.resolve(zoneSpecificResponse));

      // Act
      const result = await service.getQrCode(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(zoneSpecificResponse);
      expect(result.data.length).toBe(1);
      expect(result.data[0].zoneId).toBe(2);
    });

    it('should fetch QR codes by type', async () => {
      // Arrange
      const params = {
        page: 1,
        limit: 10,
        type: 'permanent',
        filter: ['type||eq||permanent']
      };
      const permanentQrResponse = {
        data: [mockQrCodeResponse.data[0]], // Only permanent QR code
        total: 1,
        page: 1,
        limit: 10
      };
      apiService.getData.and.returnValue(Promise.resolve(permanentQrResponse));

      // Act
      const result = await service.getQrCode(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(permanentQrResponse);
      expect(result.data.length).toBe(1);
      expect(result.data[0].type).toBe('permanent');
    });

    it('should handle null or undefined params', async () => {
      // Arrange
      apiService.getData.and.returnValue(Promise.resolve(mockQrCodeResponse));

      // Act
      const resultWithNull = await service.getQrCode(null);
      const resultWithUndefined = await service.getQrCode(undefined);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), null);
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), undefined);
      expect(resultWithNull).toEqual(mockQrCodeResponse);
      expect(resultWithUndefined).toEqual(mockQrCodeResponse);
    });

    it('should handle complex filter parameters', async () => {
      // Arrange
      const complexParams = {
        page: 1,
        limit: 20,
        filter: [
          'enabled||eq||true',
          'plantId||eq||1',
          'type||in||permanent,temporary',
          'createdAt||gte||2024-01-01'
        ],
        sort: 'qrCode,ASC',
        join: ['plant', 'zone']
      };
      apiService.getData.and.returnValue(Promise.resolve(mockQrCodeResponse));

      // Act
      const result = await service.getQrCode(complexParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), complexParams);
      expect(result).toEqual(mockQrCodeResponse);
    });
  });
});
