import { TestBed } from '@angular/core/testing';
import { OtherTaskService } from './other-task.service';
import { ApiService } from '../api.service';

describe('OtherTaskService', () => {
  let service: OtherTaskService;
  let apiService: jasmine.SpyObj<ApiService>;

  const mockOtherTaskResponse = {
    data: [
      {
        id: 1,
        title: 'Equipment Inspection',
        description: 'Monthly equipment inspection task',
        type: 'inspection',
        priority: 'high',
        status: 'pending',
        assignedTo: 1,
        plantId: 1,
        dueDate: '2024-01-15T00:00:00Z',
        createdAt: '2024-01-01T00:00:00Z',
        createdBy: 2,
        images: ['image1.jpg', 'image2.jpg'],
        videos: ['video1.mp4']
      },
      {
        id: 2,
        title: 'Safety Training',
        description: 'Quarterly safety training session',
        type: 'training',
        priority: 'medium',
        status: 'completed',
        assignedTo: 2,
        plantId: 1,
        dueDate: '2024-01-10T00:00:00Z',
        completedAt: '2024-01-09T00:00:00Z',
        createdAt: '2024-01-01T00:00:00Z',
        createdBy: 1,
        images: [],
        videos: []
      }
    ],
    total: 2,
    page: 1,
    limit: 10
  };

  beforeEach(() => {
    const apiSpy = jasmine.createSpyObj('ApiService', ['getData', 'postData']);

    TestBed.configureTestingModule({
      providers: [
        OtherTaskService,
        { provide: ApiService, useValue: apiSpy }
      ]
    });

    service = TestBed.inject(OtherTaskService);
    apiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
  });

  beforeEach(() => {
    spyOn(console, 'log');
    spyOn(console, 'error');
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getOtherTasks', () => {
    it('should fetch other tasks successfully', async () => {
      // Arrange
      const params = { page: 1, limit: 10, plantId: 1 };
      apiService.getData.and.returnValue(Promise.resolve(mockOtherTaskResponse));

      // Act
      const result = await service.getOtherTasks(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(mockOtherTaskResponse);
    });

    it('should fetch tasks with filters', async () => {
      // Arrange
      const params = {
        page: 1,
        limit: 10,
        filter: ['status||eq||pending', 'priority||eq||high'],
        sort: 'dueDate,ASC'
      };
      apiService.getData.and.returnValue(Promise.resolve(mockOtherTaskResponse));

      // Act
      const result = await service.getOtherTasks(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(mockOtherTaskResponse);
    });

    it('should handle empty tasks response', async () => {
      // Arrange
      const params = { page: 1, limit: 10, plantId: 999 };
      const emptyResponse = { data: [], total: 0, page: 1, limit: 10 };
      apiService.getData.and.returnValue(Promise.resolve(emptyResponse));

      // Act
      const result = await service.getOtherTasks(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(emptyResponse);
    });

    it('should handle errors when fetching tasks', async () => {
      // Arrange
      const params = { page: 1, limit: 10 };
      const error = new Error('Network Error');
      apiService.getData.and.returnValue(Promise.reject(error));

      // Act & Assert
      await expectAsync(service.getOtherTasks(params)).toBeRejectedWith(error);
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
    });

    it('should fetch tasks by status', async () => {
      // Arrange
      const params = {
        page: 1,
        limit: 10,
        filter: ['status||eq||pending']
      };
      const pendingTasksResponse = {
        data: [mockOtherTaskResponse.data[0]], // Only pending task
        total: 1,
        page: 1,
        limit: 10
      };
      apiService.getData.and.returnValue(Promise.resolve(pendingTasksResponse));

      // Act
      const result = await service.getOtherTasks(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(pendingTasksResponse);
      expect(result.data.length).toBe(1);
      expect(result.data[0].status).toBe('pending');
    });

    it('should fetch tasks by priority', async () => {
      // Arrange
      const params = {
        page: 1,
        limit: 10,
        filter: ['priority||eq||high']
      };
      const highPriorityResponse = {
        data: [mockOtherTaskResponse.data[0]], // Only high priority task
        total: 1,
        page: 1,
        limit: 10
      };
      apiService.getData.and.returnValue(Promise.resolve(highPriorityResponse));

      // Act
      const result = await service.getOtherTasks(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(highPriorityResponse);
      expect(result.data.length).toBe(1);
      expect(result.data[0].priority).toBe('high');
    });

    it('should fetch tasks by type', async () => {
      // Arrange
      const params = {
        page: 1,
        limit: 10,
        filter: ['type||eq||inspection']
      };
      const inspectionTasksResponse = {
        data: [mockOtherTaskResponse.data[0]], // Only inspection task
        total: 1,
        page: 1,
        limit: 10
      };
      apiService.getData.and.returnValue(Promise.resolve(inspectionTasksResponse));

      // Act
      const result = await service.getOtherTasks(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(inspectionTasksResponse);
      expect(result.data.length).toBe(1);
      expect(result.data[0].type).toBe('inspection');
    });

    it('should fetch tasks assigned to specific user', async () => {
      // Arrange
      const params = {
        page: 1,
        limit: 10,
        filter: ['assignedTo||eq||1']
      };
      const userTasksResponse = {
        data: [mockOtherTaskResponse.data[0]], // Only tasks assigned to user 1
        total: 1,
        page: 1,
        limit: 10
      };
      apiService.getData.and.returnValue(Promise.resolve(userTasksResponse));

      // Act
      const result = await service.getOtherTasks(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(userTasksResponse);
      expect(result.data.length).toBe(1);
      expect(result.data[0].assignedTo).toBe(1);
    });
  });

  describe('createOtherTask', () => {
    it('should create other task successfully', async () => {
      // Arrange
      const taskData = {
        title: 'New Maintenance Task',
        description: 'Routine maintenance check',
        type: 'maintenance',
        priority: 'medium',
        assignedTo: 1,
        plantId: 1,
        dueDate: '2024-02-01T00:00:00Z',
        createdBy: 2
      };
      const mockCreateResponse = {
        responseCode: 200,
        message: 'Task created successfully',
        data: { id: 3, ...taskData, status: 'pending', createdAt: '2024-01-15T00:00:00Z' }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockCreateResponse));

      // Act
      const result = await service.createOtherTask(taskData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), taskData);
      expect(result).toEqual(mockCreateResponse);
    });

    it('should handle validation errors when creating task', async () => {
      // Arrange
      const invalidTaskData = { title: '', description: '', assignedTo: null };
      const validationError = {
        responseCode: 400,
        message: 'Validation failed',
        errors: ['Title is required', 'Description is required', 'Assigned user is required']
      };
      apiService.postData.and.returnValue(Promise.reject(validationError));

      // Act & Assert
      await expectAsync(service.createOtherTask(invalidTaskData))
        .toBeRejectedWith(validationError);
    });

    it('should create task with images and videos', async () => {
      // Arrange
      const taskDataWithMedia = {
        title: 'Inspection with Media',
        description: 'Detailed inspection with photo evidence',
        type: 'inspection',
        priority: 'high',
        assignedTo: 1,
        plantId: 1,
        dueDate: '2024-02-01T00:00:00Z',
        images: ['inspection1.jpg', 'inspection2.jpg'],
        videos: ['inspection_video.mp4'],
        createdBy: 2
      };
      const mockCreateResponse = {
        responseCode: 200,
        message: 'Task with media created successfully',
        data: { id: 4, ...taskDataWithMedia, status: 'pending' }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockCreateResponse));

      // Act
      const result = await service.createOtherTask(taskDataWithMedia);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), taskDataWithMedia);
      expect(result).toEqual(mockCreateResponse);
    });

    it('should create urgent task', async () => {
      // Arrange
      const urgentTaskData = {
        title: 'Emergency Repair',
        description: 'Critical equipment failure needs immediate attention',
        type: 'repair',
        priority: 'urgent',
        assignedTo: 1,
        plantId: 1,
        dueDate: '2024-01-16T00:00:00Z', // Due tomorrow
        isUrgent: true,
        createdBy: 2
      };
      const mockCreateResponse = {
        responseCode: 200,
        message: 'Urgent task created successfully',
        data: { id: 5, ...urgentTaskData, status: 'pending' }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockCreateResponse));

      // Act
      const result = await service.createOtherTask(urgentTaskData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), urgentTaskData);
      expect(result).toEqual(mockCreateResponse);
    });

    it('should handle errors when creating task', async () => {
      // Arrange
      const taskData = { title: 'Test Task', assignedTo: 999 }; // Invalid user
      const error = new Error('User not found');
      apiService.postData.and.returnValue(Promise.reject(error));

      // Act & Assert
      await expectAsync(service.createOtherTask(taskData)).toBeRejectedWith(error);
    });

    it('should create recurring task', async () => {
      // Arrange
      const recurringTaskData = {
        title: 'Weekly Safety Check',
        description: 'Weekly safety inspection routine',
        type: 'inspection',
        priority: 'medium',
        assignedTo: 1,
        plantId: 1,
        dueDate: '2024-01-22T00:00:00Z',
        isRecurring: true,
        recurringInterval: 'weekly',
        createdBy: 2
      };
      const mockCreateResponse = {
        responseCode: 200,
        message: 'Recurring task created successfully',
        data: { id: 6, ...recurringTaskData, status: 'pending' }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockCreateResponse));

      // Act
      const result = await service.createOtherTask(recurringTaskData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), recurringTaskData);
      expect(result).toEqual(mockCreateResponse);
    });

    it('should create task with custom fields', async () => {
      // Arrange
      const customTaskData = {
        title: 'Custom Task',
        description: 'Task with custom fields',
        type: 'custom',
        priority: 'low',
        assignedTo: 1,
        plantId: 1,
        dueDate: '2024-02-01T00:00:00Z',
        customFields: {
          equipment: 'Pump #123',
          location: 'Building A',
          estimatedHours: 4
        },
        tags: ['maintenance', 'pump', 'routine'],
        createdBy: 2
      };
      const mockCreateResponse = {
        responseCode: 200,
        message: 'Custom task created successfully',
        data: { id: 7, ...customTaskData, status: 'pending' }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockCreateResponse));

      // Act
      const result = await service.createOtherTask(customTaskData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), customTaskData);
      expect(result).toEqual(mockCreateResponse);
    });
  });
});
