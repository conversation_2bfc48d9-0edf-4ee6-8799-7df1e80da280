import { TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { ApiService } from './api.service';

describe('ApiService', () => {
  let service: ApiService;
  let router: jasmine.SpyObj<Router>;

  beforeEach(() => {
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);

    TestBed.configureTestingModule({
      providers: [
        ApiService,
        { provide: Router, useValue: routerSpy }
      ]
    });

    service = TestBed.inject(ApiService);
    router = TestBed.inject(Router) as jasmine.SpyObj<Router>;
  });

  beforeEach(() => {
    // Clear localStorage and setup spies
    localStorage.clear();
    spyOn(console, 'log');
    spyOn(console, 'error');
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getData method', () => {
    it('should make GET request successfully', async () => {
      // Arrange
      const mockResponse = { message: 'success', data: [] };
      spyOn(service['axiosInstance'], 'get').and.returnValue(Promise.resolve({ data: mockResponse }));

      // Act
      const result = await service.getData('/test-endpoint');

      // Assert
      expect(service['axiosInstance'].get).toHaveBeenCalledWith('/test-endpoint', jasmine.any(Object));
      expect(result).toEqual(mockResponse);
    });

    it('should make GET request with params', async () => {
      // Arrange
      const mockResponse = { message: 'success', data: [] };
      const params = { page: 1, limit: 10 };
      spyOn(service['axiosInstance'], 'get').and.returnValue(Promise.resolve({ data: mockResponse }));

      // Act
      const result = await service.getData('/test-endpoint', params);

      // Assert
      expect(service['axiosInstance'].get).toHaveBeenCalledWith('/test-endpoint', jasmine.any(Object));
      expect(result).toEqual(mockResponse);
    });

    it('should handle GET request errors', async () => {
      // Arrange
      const mockError = new Error('Network Error');
      spyOn(service['axiosInstance'], 'get').and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.getData('/test-endpoint')).toBeRejectedWith(mockError);
    });

    it('should add businessUnitId filter to GET requests', async () => {
      // Arrange
      const mockResponse = { message: 'success', data: [] };
      spyOn(service['axiosInstance'], 'get').and.returnValue(Promise.resolve({ data: mockResponse }));

      // Act
      await service.getData('/test-endpoint');

      // Assert
      expect(service['axiosInstance'].get).toHaveBeenCalledWith('/test-endpoint', jasmine.objectContaining({
        params: jasmine.objectContaining({
          filter: jasmine.arrayContaining(['businessUnitId||eq||0'])
        })
      }));
    });
  });

  describe('postData method', () => {
    it('should make POST request successfully', async () => {
      // Arrange
      const mockResponse = { message: 'created', data: { id: 1 } };
      const postData = { name: 'Test' };
      spyOn(service['axiosInstance'], 'post').and.returnValue(Promise.resolve({ data: mockResponse }));

      // Act
      const result = await service.postData('/test-endpoint', postData);

      // Assert
      expect(service['axiosInstance'].post).toHaveBeenCalledWith('/test-endpoint', jasmine.objectContaining({
        name: 'Test',
        businessUnitId: 0
      }));
      expect(result).toEqual(mockResponse);
    });

    it('should handle FormData without adding businessUnitId', async () => {
      // Arrange
      const mockResponse = { message: 'uploaded', data: { url: 'test.jpg' } };
      const formData = new FormData();
      formData.append('file', new File([''], 'test.jpg'));
      spyOn(service['axiosInstance'], 'post').and.returnValue(Promise.resolve({ data: mockResponse }));

      // Act
      const result = await service.postData('/common/upload', formData);

      // Assert
      expect(service['axiosInstance'].post).toHaveBeenCalledWith('/common/upload', formData);
      expect(result).toEqual(mockResponse);
    });

    it('should handle POST request errors', async () => {
      // Arrange
      const mockError = new Error('Validation Error');
      const postData = { name: 'Test' };
      spyOn(service['axiosInstance'], 'post').and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.postData('/test-endpoint', postData)).toBeRejectedWith(mockError);
    });
  });

  describe('putData method', () => {
    it('should make PUT request successfully', async () => {
      // Arrange
      const mockResponse = { message: 'updated', data: { id: 1, name: 'Updated' } };
      const putData = { id: 1, name: 'Updated' };
      spyOn(service['axiosInstance'], 'put').and.returnValue(Promise.resolve({ data: mockResponse }));

      // Act
      const result = await service.putData('/test-endpoint/1', putData);

      // Assert
      expect(service['axiosInstance'].put).toHaveBeenCalledWith('/test-endpoint/1', putData);
      expect(result).toEqual(mockResponse);
    });

    it('should handle PUT request errors', async () => {
      // Arrange
      const mockError = new Error('Not Found');
      const putData = { id: 1, name: 'Updated' };
      spyOn(service['axiosInstance'], 'put').and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.putData('/test-endpoint/1', putData)).toBeRejectedWith(mockError);
    });
  });

  describe('deleteData method', () => {
    it('should make DELETE request successfully', async () => {
      // Arrange
      const mockResponse = { message: 'deleted' };
      const deleteData = { isDeleted: true };
      spyOn(service['axiosInstance'], 'patch').and.returnValue(Promise.resolve({ data: mockResponse }));

      // Act
      const result = await service.deleteData(1, '/test-endpoint', deleteData);

      // Assert
      expect(service['axiosInstance'].patch).toHaveBeenCalledWith('/test-endpoint/1', deleteData);
      expect(result).toEqual(mockResponse);
    });

    it('should handle DELETE request errors', async () => {
      // Arrange
      const mockError = new Error('Forbidden');
      const deleteData = { isDeleted: true };
      spyOn(service['axiosInstance'], 'patch').and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.deleteData(1, '/test-endpoint', deleteData)).toBeRejectedWith(mockError);
    });
  });

  describe('Business Unit ID handling', () => {
    it('should get businessUnitId from localStorage', () => {
      // Arrange
      const mockUser = { businessUnitId: 5 };
      localStorage.setItem('user', JSON.stringify(mockUser));

      // Act
      const businessUnitId = service['getBusinessUnitIdFromLocalStorage']();

      // Assert
      expect(businessUnitId).toBe(5);
    });

    it('should default to 0 when no user in localStorage', () => {
      // Act
      const businessUnitId = service['getBusinessUnitIdFromLocalStorage']();

      // Assert
      expect(businessUnitId).toBe(0);
    });

    it('should handle invalid JSON in localStorage', () => {
      // Arrange
      localStorage.setItem('user', 'invalid-json');

      // Act
      const businessUnitId = service['getBusinessUnitIdFromLocalStorage']();

      // Assert
      expect(businessUnitId).toBe(0);
      expect(console.error).toHaveBeenCalled();
    });
  });
});
