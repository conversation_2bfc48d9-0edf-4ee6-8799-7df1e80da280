import { TestBed } from '@angular/core/testing';
import { AdminService } from './admin.service';
import { ApiService } from '../api.service';

describe('AdminService', () => {
  let service: AdminService;
  let apiService: jasmine.SpyObj<ApiService>;

  const mockAdminResponse = {
    data: [
      {
        id: 1,
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        enabled: true,
        status: 1
      }
    ],
    total: 1,
    page: 1,
    limit: 10
  };

  beforeEach(() => {
    const apiSpy = jasmine.createSpyObj('ApiService', ['getData', 'postData']);

    TestBed.configureTestingModule({
      providers: [
        AdminService,
        { provide: ApiService, useValue: apiSpy }
      ]
    });

    service = TestBed.inject(AdminService);
    apiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
  });

  beforeEach(() => {
    spyOn(console, 'log');
    spyOn(console, 'error');
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getAdmin', () => {
    it('should fetch admins successfully', async () => {
      // Arrange
      const params = { page: 1, limit: 10 };
      apiService.getData.and.returnValue(Promise.resolve(mockAdminResponse));

      // Act
      const result = await service.getAdmin(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(mockAdminResponse);
    });

    it('should handle errors when fetching admins', async () => {
      // Arrange
      const params = { page: 1, limit: 10 };
      const error = new Error('Network Error');
      apiService.getData.and.returnValue(Promise.reject(error));

      // Act & Assert
      await expectAsync(service.getAdmin(params)).toBeRejectedWith(error);
    });
  });

  describe('getDeletedUserLog', () => {
    it('should fetch deleted user log successfully', async () => {
      // Arrange
      const params = { page: 1, limit: 10 };
      const mockDeletedUserResponse = { data: [], total: 0 };
      apiService.getData.and.returnValue(Promise.resolve(mockDeletedUserResponse));

      // Act
      const result = await service.getDeletedUserLog(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(mockDeletedUserResponse);
    });
  });

  describe('getPlantTransferRequest', () => {
    it('should fetch plant transfer requests successfully', async () => {
      // Arrange
      const params = { page: 1, limit: 10 };
      const mockTransferResponse = { data: [], total: 0 };
      apiService.getData.and.returnValue(Promise.resolve(mockTransferResponse));

      // Act
      const result = await service.getPlantTransferRequest(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(mockTransferResponse);
    });
  });

  describe('getAdminRoles', () => {
    it('should fetch admin roles successfully', async () => {
      // Arrange
      const params = { enabled: true };
      const mockRolesResponse = {
        data: [
          { id: 1, name: 'Super Admin' },
          { id: 2, name: 'Plant Admin' },
          { id: 3, name: 'Regular User' }
        ]
      };
      apiService.getData.and.returnValue(Promise.resolve(mockRolesResponse));

      // Act
      const result = await service.getAdminRoles(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(mockRolesResponse);
    });
  });

  describe('checkContactNumber', () => {
    it('should check contact number successfully', async () => {
      // Arrange
      const contactData = { contactNumber: '1234567890' };
      const mockResponse = { exists: false };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.checkContactNumber(contactData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), contactData);
      expect(result).toEqual(mockResponse);
    });

    it('should handle contact number check errors', async () => {
      // Arrange
      const contactData = { contactNumber: '1234567890' };
      const error = new Error('Validation Error');
      apiService.postData.and.returnValue(Promise.reject(error));

      // Act & Assert
      await expectAsync(service.checkContactNumber(contactData)).toBeRejectedWith(error);
    });
  });

  describe('getAdminsByIds', () => {
    it('should throw error for unimplemented method', () => {
      // Arrange
      const ids = [1, 2, 3];

      // Act & Assert
      expect(() => service.getAdminsByIds(ids)).toThrowError('Method not implemented.');
    });
  });
});
