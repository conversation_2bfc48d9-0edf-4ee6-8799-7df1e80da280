import { TestBed } from '@angular/core/testing';
import { NotificationManagementService } from './notification-management.service';
import { ApiService } from '../api.service';

describe('NotificationManagementService', () => {
  let service: NotificationManagementService;
  let apiService: jasmine.SpyObj<ApiService>;

  const mockNotificationResponse = {
    data: [
      {
        id: 1,
        title: 'Safety Alert',
        message: 'Emergency safety drill scheduled',
        type: 'alert',
        priority: 'high',
        plantIds: [1, 2],
        userIds: [1, 2, 3],
        isRead: false,
        createdAt: '2024-01-01T00:00:00Z',
        createdBy: 1
      },
      {
        id: 2,
        title: 'System Maintenance',
        message: 'Scheduled maintenance tonight',
        type: 'info',
        priority: 'medium',
        plantIds: [1],
        userIds: [1, 2],
        isRead: true,
        createdAt: '2024-01-02T00:00:00Z',
        createdBy: 2
      }
    ],
    total: 2,
    page: 1,
    limit: 10
  };

  const mockUsersResponse = {
    data: [
      {
        id: 1,
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        plantId: 1,
        enabled: true
      },
      {
        id: 2,
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        plantId: 1,
        enabled: true
      }
    ]
  };

  beforeEach(() => {
    const apiSpy = jasmine.createSpyObj('ApiService', ['getData', 'postData', 'putData']);

    TestBed.configureTestingModule({
      providers: [
        NotificationManagementService,
        { provide: ApiService, useValue: apiSpy }
      ]
    });

    service = TestBed.inject(NotificationManagementService);
    apiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
  });

  beforeEach(() => {
    spyOn(console, 'log');
    spyOn(console, 'error');
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getNotifications', () => {
    it('should fetch notifications successfully', async () => {
      // Arrange
      const params = { page: 1, limit: 10, userId: 1 };
      apiService.getData.and.returnValue(Promise.resolve(mockNotificationResponse));

      // Act
      const result = await service.getNotifications(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(mockNotificationResponse);
    });

    it('should fetch notifications with filters', async () => {
      // Arrange
      const params = {
        page: 1,
        limit: 10,
        filter: ['isRead||eq||false', 'priority||eq||high'],
        sort: 'createdAt,DESC'
      };
      apiService.getData.and.returnValue(Promise.resolve(mockNotificationResponse));

      // Act
      const result = await service.getNotifications(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(mockNotificationResponse);
    });

    it('should handle empty notifications response', async () => {
      // Arrange
      const params = { page: 1, limit: 10, userId: 999 };
      const emptyResponse = { data: [], total: 0, page: 1, limit: 10 };
      apiService.getData.and.returnValue(Promise.resolve(emptyResponse));

      // Act
      const result = await service.getNotifications(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(emptyResponse);
    });

    it('should handle errors when fetching notifications', async () => {
      // Arrange
      const params = { page: 1, limit: 10 };
      const error = new Error('Network Error');
      apiService.getData.and.returnValue(Promise.reject(error));

      // Act & Assert
      await expectAsync(service.getNotifications(params)).toBeRejectedWith(error);
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
    });

    it('should fetch unread notifications', async () => {
      // Arrange
      const params = {
        page: 1,
        limit: 10,
        filter: ['isRead||eq||false']
      };
      const unreadNotificationsResponse = {
        data: [mockNotificationResponse.data[0]], // Only unread notification
        total: 1,
        page: 1,
        limit: 10
      };
      apiService.getData.and.returnValue(Promise.resolve(unreadNotificationsResponse));

      // Act
      const result = await service.getNotifications(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(unreadNotificationsResponse);
      expect(result.data.length).toBe(1);
      expect(result.data[0].isRead).toBe(false);
    });

    it('should fetch notifications by priority', async () => {
      // Arrange
      const params = {
        page: 1,
        limit: 10,
        filter: ['priority||eq||high']
      };
      const highPriorityResponse = {
        data: [mockNotificationResponse.data[0]], // Only high priority notification
        total: 1,
        page: 1,
        limit: 10
      };
      apiService.getData.and.returnValue(Promise.resolve(highPriorityResponse));

      // Act
      const result = await service.getNotifications(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(highPriorityResponse);
      expect(result.data.length).toBe(1);
      expect(result.data[0].priority).toBe('high');
    });
  });

  describe('createNotification', () => {
    it('should create notification successfully', async () => {
      // Arrange
      const notificationData = {
        title: 'New Alert',
        message: 'Important announcement',
        type: 'alert',
        priority: 'high',
        plantIds: [1, 2],
        userIds: [1, 2, 3],
        createdBy: 1
      };
      const mockCreateResponse = {
        responseCode: 200,
        message: 'Notification created successfully',
        data: { id: 3, ...notificationData }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockCreateResponse));

      // Act
      const result = await service.createNotification(notificationData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), notificationData);
      expect(result).toEqual(mockCreateResponse);
    });

    it('should handle validation errors when creating notification', async () => {
      // Arrange
      const invalidNotificationData = { title: '', message: '' };
      const validationError = {
        responseCode: 400,
        message: 'Validation failed',
        errors: ['Title is required', 'Message is required']
      };
      apiService.postData.and.returnValue(Promise.reject(validationError));

      // Act & Assert
      await expectAsync(service.createNotification(invalidNotificationData))
        .toBeRejectedWith(validationError);
    });
  });

  describe('updateNotification', () => {
    it('should update notification successfully', async () => {
      // Arrange
      const notificationId = 1;
      const updateData = {
        title: 'Updated Alert',
        message: 'Updated message',
        isRead: true
      };
      const mockUpdateResponse = {
        responseCode: 200,
        message: 'Notification updated successfully',
        data: { id: notificationId, ...updateData }
      };
      apiService.putData.and.returnValue(Promise.resolve(mockUpdateResponse));

      // Act
      const result = await service.updateNotification(notificationId, updateData);

      // Assert
      expect(apiService.putData).toHaveBeenCalledWith(
        jasmine.stringContaining(`/${notificationId}`),
        updateData
      );
      expect(result).toEqual(mockUpdateResponse);
    });

    it('should handle errors when updating notification', async () => {
      // Arrange
      const notificationId = 999;
      const updateData = { isRead: true };
      const error = new Error('Notification not found');
      apiService.putData.and.returnValue(Promise.reject(error));

      // Act & Assert
      await expectAsync(service.updateNotification(notificationId, updateData))
        .toBeRejectedWith(error);
    });
  });

  describe('createNotificationCustom', () => {
    it('should create custom notification successfully', async () => {
      // Arrange
      const customNotificationData = {
        title: 'Plant-wise Alert',
        message: 'Custom notification for specific plants',
        plantIds: [1, 2],
        priority: 'high',
        type: 'custom'
      };
      const mockCustomResponse = {
        responseCode: 200,
        message: 'Custom notification sent successfully',
        data: { sentCount: 5, plantIds: [1, 2] }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockCustomResponse));

      // Act
      const result = await service.createNotificationCustom(customNotificationData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(
        '/Notifications/send-notification-plant-wise',
        customNotificationData
      );
      expect(result).toEqual(mockCustomResponse);
    });

    it('should handle errors when creating custom notification', async () => {
      // Arrange
      const customNotificationData = { plantIds: [], message: '' };
      const error = new Error('Invalid plant IDs or message');
      apiService.postData.and.returnValue(Promise.reject(error));

      // Act & Assert
      await expectAsync(service.createNotificationCustom(customNotificationData))
        .toBeRejectedWith(error);
    });
  });

  describe('getUsersByPlantIds', () => {
    it('should fetch users by plant IDs successfully', async () => {
      // Arrange
      const plantData = { plantIds: [1, 2] };
      apiService.postData.and.returnValue(Promise.resolve(mockUsersResponse));

      // Act
      const result = await service.getUsersByPlantIds(plantData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith('/Notifications/get-user', plantData);
      expect(result).toEqual(mockUsersResponse);
    });

    it('should handle empty plant IDs', async () => {
      // Arrange
      const emptyPlantData = { plantIds: [] };
      const emptyUsersResponse = { data: [] };
      apiService.postData.and.returnValue(Promise.resolve(emptyUsersResponse));

      // Act
      const result = await service.getUsersByPlantIds(emptyPlantData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith('/Notifications/get-user', emptyPlantData);
      expect(result).toEqual(emptyUsersResponse);
    });

    it('should handle errors when fetching users', async () => {
      // Arrange
      const plantData = { plantIds: [999] };
      const error = new Error('Plant not found');
      apiService.postData.and.returnValue(Promise.reject(error));

      // Act & Assert
      await expectAsync(service.getUsersByPlantIds(plantData)).toBeRejectedWith(error);
    });
  });
});
