import { Injectable } from '@angular/core';
import { ENDPOINTS } from '../../core/endpoints';
import { AppSettingsResponse } from '../../model/app-settings.model';
import { ApiService } from '../api.service';

@Injectable({
  providedIn: 'root'
})
export class AppSettingsService {

  constructor(readonly apiService: ApiService) { }

  async getSettings(params: any): Promise<AppSettingsResponse[]> {
    const response = await this.apiService.getData(ENDPOINTS.SETTINGS.GET_SETTINGS, params);
    return response as AppSettingsResponse[];
  }
}
