import { TestBed } from '@angular/core/testing';
import { SafetyTrainingService } from './safety-training.service';
import { ApiService } from '../api.service';
import { AuthService } from '../auth.service';

describe('SafetyTrainingService', () => {
  let service: SafetyTrainingService;
  let apiService: jasmine.SpyObj<ApiService>;
  let authService: jasmine.SpyObj<AuthService>;

  const mockSafetyTrainingResponse = {
    data: [
      {
        id: 1,
        title: 'Fire Safety Training',
        description: 'Comprehensive fire safety and emergency response training',
        type: 'Fire Safety',
        participantType: 'Employee',
        duration: 4, // hours
        maxParticipants: 30,
        actualParticipants: 25,
        maleParticipants: 15,
        femaleParticipants: 10,
        trainingDate: '2024-01-15T09:00:00Z',
        venue: 'Training Hall A',
        instructor: 'John Safety Expert',
        plantId: 1,
        status: 'Completed',
        certificateIssued: true,
        manHours: 100, // 25 participants * 4 hours
        feedback: {
          averageRating: 4.5,
          totalResponses: 23
        },
        materials: ['Safety Manual', 'Fire Extinguisher Demo', 'Emergency Procedures'],
        createdAt: '2024-01-01T00:00:00Z',
        createdBy: 1
      },
      {
        id: 2,
        title: 'Chemical Handling Safety',
        description: 'Safe handling and storage of hazardous chemicals',
        type: 'Chemical Safety',
        participantType: 'Contractor',
        duration: 6, // hours
        maxParticipants: 20,
        actualParticipants: 18,
        maleParticipants: 12,
        femaleParticipants: 6,
        trainingDate: '2024-01-20T08:00:00Z',
        venue: 'Laboratory Training Room',
        instructor: 'Jane Chemical Expert',
        plantId: 1,
        status: 'Completed',
        certificateIssued: true,
        manHours: 108, // 18 participants * 6 hours
        feedback: {
          averageRating: 4.8,
          totalResponses: 18
        },
        materials: ['Chemical Safety Guide', 'PPE Demonstration', 'MSDS Training'],
        createdAt: '2024-01-05T00:00:00Z',
        createdBy: 2
      }
    ],
    total: 2,
    page: 1,
    limit: 10
  };

  const mockMaleFemaleCountResponse = {
    data: {
      male: 150,
      female: 80,
      total: 230,
      year: 2024
    }
  };

  const mockTrainingTypeCountResponse = {
    data: [
      { type: 'Fire Safety', count: 45 },
      { type: 'Chemical Safety', count: 32 },
      { type: 'Electrical Safety', count: 28 },
      { type: 'Mechanical Safety', count: 35 },
      { type: 'General Safety', count: 60 }
    ]
  };

  const mockParticipantTypeCountResponse = {
    data: [
      { participantType: 'Employee', count: 120 },
      { participantType: 'Contractor', count: 80 },
      { participantType: 'Visitor', count: 30 }
    ]
  };

  const mockMonthWiseCountResponse = {
    data: [
      { month: 1, count: 15 },
      { month: 2, count: 18 },
      { month: 3, count: 22 },
      { month: 4, count: 20 },
      { month: 5, count: 25 },
      { month: 6, count: 19 },
      { month: 7, count: 23 },
      { month: 8, count: 21 },
      { month: 9, count: 24 },
      { month: 10, count: 26 },
      { month: 11, count: 18 },
      { month: 12, count: 16 }
    ]
  };

  const mockMonthWiseManHourResponse = {
    data: [
      { month: 1, manHours: 450 },
      { month: 2, manHours: 540 },
      { month: 3, manHours: 660 },
      { month: 4, manHours: 600 },
      { month: 5, manHours: 750 },
      { month: 6, manHours: 570 },
      { month: 7, manHours: 690 },
      { month: 8, manHours: 630 },
      { month: 9, manHours: 720 },
      { month: 10, manHours: 780 },
      { month: 11, manHours: 540 },
      { month: 12, manHours: 480 }
    ]
  };

  const mockAverageParticipantResponse = {
    data: {
      averageParticipants: 22.5,
      totalTrainings: 200,
      totalParticipants: 4500,
      year: 2024
    }
  };

  beforeEach(() => {
    const apiSpy = jasmine.createSpyObj('ApiService', ['getData', 'postData']);
    const authSpy = jasmine.createSpyObj('AuthService', ['getBusinessUnitId']);

    TestBed.configureTestingModule({
      providers: [
        SafetyTrainingService,
        { provide: ApiService, useValue: apiSpy },
        { provide: AuthService, useValue: authSpy }
      ]
    });

    service = TestBed.inject(SafetyTrainingService);
    apiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
    authService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;

    // Setup default auth service behavior
    authService.getBusinessUnitId.and.returnValue('0');
  });

  beforeEach(() => {
    spyOn(console, 'log');
    spyOn(console, 'error');
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('createSafetyTraining', () => {
    it('should create safety training successfully', async () => {
      // Arrange
      const newTrainingData = {
        title: 'Electrical Safety Training',
        description: 'Electrical safety procedures and lockout/tagout',
        type: 'Electrical Safety',
        participantType: 'Employee',
        duration: 5,
        maxParticipants: 25,
        trainingDate: '2024-02-01T09:00:00Z',
        venue: 'Training Hall B',
        instructor: 'Bob Electrical Expert',
        plantId: 1,
        materials: ['Electrical Safety Manual', 'LOTO Procedures', 'PPE Requirements'],
        createdBy: 1
      };
      const mockCreateResponse = {
        responseCode: 200,
        message: 'Safety training created successfully',
        data: {
          id: 3,
          ...newTrainingData,
          status: 'Scheduled',
          certificateIssued: false,
          createdAt: '2024-01-25T00:00:00Z'
        }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockCreateResponse));

      // Act
      const result = await service.createSafetyTraining(newTrainingData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), newTrainingData);
      expect(result).toEqual(mockCreateResponse);
    });

    it('should handle validation errors when creating training', async () => {
      // Arrange
      const invalidTrainingData = {
        title: '', // Empty title should cause validation error
        description: '',
        type: '',
        duration: 0
      };
      const validationError = {
        responseCode: 400,
        message: 'Validation failed',
        errors: ['Title is required', 'Description is required', 'Type is required', 'Duration must be greater than 0']
      };
      apiService.postData.and.returnValue(Promise.reject(validationError));

      // Act & Assert
      await expectAsync(service.createSafetyTraining(invalidTrainingData))
        .toBeRejectedWith(validationError);
    });
  });

  describe('getSafetyTraining', () => {
    it('should fetch safety trainings successfully', async () => {
      // Arrange
      const params = { page: 1, limit: 10, status: 'Completed' };
      apiService.getData.and.returnValue(Promise.resolve(mockSafetyTrainingResponse));

      // Act
      const result = await service.getSafetyTraining(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(mockSafetyTrainingResponse);
    });

    it('should handle errors when fetching trainings', async () => {
      // Arrange
      const params = { page: 1, limit: 10 };
      const error = new Error('Network Error');
      apiService.getData.and.returnValue(Promise.reject(error));

      // Act & Assert
      await expectAsync(service.getSafetyTraining(params)).toBeRejectedWith(error);
    });
  });

  describe('getMaleFemaleCount', () => {
    it('should fetch male/female count successfully', async () => {
      // Arrange
      const year = 2024;
      apiService.getData.and.returnValue(Promise.resolve(mockMaleFemaleCountResponse));

      // Act
      const result = await service.getMaleFemaleCount(year);

      // Assert
      expect(authService.getBusinessUnitId).toHaveBeenCalled();
      expect(apiService.getData).toHaveBeenCalledWith(
        jasmine.any(String),
        { params: { year: year, businessUnitId: '0' } }
      );
      expect(result).toEqual(mockMaleFemaleCountResponse);
    });

    it('should handle errors when fetching male/female count', async () => {
      // Arrange
      const year = 2024;
      const error = new Error('Data fetch error');
      apiService.getData.and.returnValue(Promise.reject(error));

      // Act & Assert
      await expectAsync(service.getMaleFemaleCount(year)).toBeRejectedWith(error);
    });

    it('should use correct business unit ID', async () => {
      // Arrange
      const year = 2024;
      const customBusinessUnitId = '5';
      authService.getBusinessUnitId.and.returnValue(customBusinessUnitId);
      apiService.getData.and.returnValue(Promise.resolve(mockMaleFemaleCountResponse));

      // Act
      await service.getMaleFemaleCount(year);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(
        jasmine.any(String),
        { params: { year: year, businessUnitId: customBusinessUnitId } }
      );
    });
  });

  describe('getTrainingTypeCount', () => {
    it('should fetch training type count successfully', async () => {
      // Arrange
      const year = 2024;
      apiService.getData.and.returnValue(Promise.resolve(mockTrainingTypeCountResponse));

      // Act
      const result = await service.getTrainingTypeCount(year);

      // Assert
      expect(authService.getBusinessUnitId).toHaveBeenCalled();
      expect(apiService.getData).toHaveBeenCalledWith(
        jasmine.any(String),
        { params: { year: year, businessUnitId: '0' } }
      );
      expect(result).toEqual(mockTrainingTypeCountResponse);
    });

    it('should handle errors when fetching training type count', async () => {
      // Arrange
      const year = 2024;
      const error = new Error('Training type data error');
      apiService.getData.and.returnValue(Promise.reject(error));

      // Act & Assert
      await expectAsync(service.getTrainingTypeCount(year)).toBeRejectedWith(error);
    });
  });

  describe('getParticipantTypeCount', () => {
    it('should fetch participant type count successfully', async () => {
      // Arrange
      const year = 2024;
      apiService.getData.and.returnValue(Promise.resolve(mockParticipantTypeCountResponse));

      // Act
      const result = await service.getParticipantTypeCount(year);

      // Assert
      expect(authService.getBusinessUnitId).toHaveBeenCalled();
      expect(apiService.getData).toHaveBeenCalledWith(
        jasmine.any(String),
        { params: { year: year, businessUnitId: '0' } }
      );
      expect(result).toEqual(mockParticipantTypeCountResponse);
    });

    it('should handle errors when fetching participant type count', async () => {
      // Arrange
      const year = 2024;
      const error = new Error('Participant type data error');
      apiService.getData.and.returnValue(Promise.reject(error));

      // Act & Assert
      await expectAsync(service.getParticipantTypeCount(year)).toBeRejectedWith(error);
    });
  });

  describe('getTrainingMonthWiseCount', () => {
    it('should fetch month-wise training count successfully', async () => {
      // Arrange
      const year = 2024;
      apiService.getData.and.returnValue(Promise.resolve(mockMonthWiseCountResponse));

      // Act
      const result = await service.getTrainingMonthWiseCount(year);

      // Assert
      expect(authService.getBusinessUnitId).toHaveBeenCalled();
      expect(apiService.getData).toHaveBeenCalledWith(
        jasmine.any(String),
        { params: { year: year, businessUnitId: '0' } }
      );
      expect(result).toEqual(mockMonthWiseCountResponse);
    });

    it('should handle errors when fetching month-wise count', async () => {
      // Arrange
      const year = 2024;
      const error = new Error('Month-wise data error');
      apiService.getData.and.returnValue(Promise.reject(error));

      // Act & Assert
      await expectAsync(service.getTrainingMonthWiseCount(year)).toBeRejectedWith(error);
    });
  });

  describe('getTrainingMonthWiseManHourCount', () => {
    it('should fetch month-wise man hour count successfully', async () => {
      // Arrange
      const year = 2024;
      apiService.getData.and.returnValue(Promise.resolve(mockMonthWiseManHourResponse));

      // Act
      const result = await service.getTrainingMonthWiseManHourCount(year);

      // Assert
      expect(authService.getBusinessUnitId).toHaveBeenCalled();
      expect(apiService.getData).toHaveBeenCalledWith(
        jasmine.any(String),
        { params: { year: year, businessUnitId: '0' } }
      );
      expect(result).toEqual(mockMonthWiseManHourResponse);
    });

    it('should handle errors when fetching man hour count', async () => {
      // Arrange
      const year = 2024;
      const error = new Error('Man hour data error');
      apiService.getData.and.returnValue(Promise.reject(error));

      // Act & Assert
      await expectAsync(service.getTrainingMonthWiseManHourCount(year)).toBeRejectedWith(error);
    });
  });

  describe('getAverageParticipantPerTraining', () => {
    it('should fetch average participant count successfully', async () => {
      // Arrange
      const year = 2024;
      apiService.getData.and.returnValue(Promise.resolve(mockAverageParticipantResponse));

      // Act
      const result = await service.getAverageParticipantPerTraining(year);

      // Assert
      expect(authService.getBusinessUnitId).toHaveBeenCalled();
      expect(apiService.getData).toHaveBeenCalledWith(
        jasmine.any(String),
        { params: { year: year, businessUnitId: '0' } }
      );
      expect(result).toEqual(mockAverageParticipantResponse);
    });

    it('should handle errors when fetching average participant count', async () => {
      // Arrange
      const year = 2024;
      const error = new Error('Average participant data error');
      apiService.getData.and.returnValue(Promise.reject(error));

      // Act & Assert
      await expectAsync(service.getAverageParticipantPerTraining(year)).toBeRejectedWith(error);
    });
  });

  describe('Integration Tests', () => {
    it('should handle multiple API calls with different years', async () => {
      // Arrange
      const year2023 = 2023;
      const year2024 = 2024;
      apiService.getData.and.returnValue(Promise.resolve(mockMaleFemaleCountResponse));

      // Act
      await service.getMaleFemaleCount(year2023);
      await service.getTrainingTypeCount(year2024);

      // Assert
      expect(apiService.getData).toHaveBeenCalledTimes(2);
      expect(apiService.getData).toHaveBeenCalledWith(
        jasmine.any(String),
        { params: { year: year2023, businessUnitId: '0' } }
      );
      expect(apiService.getData).toHaveBeenCalledWith(
        jasmine.any(String),
        { params: { year: year2024, businessUnitId: '0' } }
      );
    });

    it('should handle business unit changes', async () => {
      // Arrange
      const year = 2024;
      authService.getBusinessUnitId.and.returnValue('1');
      apiService.getData.and.returnValue(Promise.resolve(mockMaleFemaleCountResponse));

      // Act
      await service.getMaleFemaleCount(year);

      // Reset business unit
      authService.getBusinessUnitId.and.returnValue('2');
      await service.getTrainingTypeCount(year);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(
        jasmine.any(String),
        { params: { year: year, businessUnitId: '1' } }
      );
      expect(apiService.getData).toHaveBeenCalledWith(
        jasmine.any(String),
        { params: { year: year, businessUnitId: '2' } }
      );
    });
  });
});
