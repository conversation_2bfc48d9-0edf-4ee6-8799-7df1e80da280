import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { AuthService } from '../auth.service';
import { ENDPOINTS } from '../../core/endpoints';

@Injectable({
  providedIn: 'root'
})
export class SafetyTrainingService {

  constructor(private apiService:ApiService, private authService: AuthService) {

  }
  async createSafetyTraining(data: any) {
    return await this.apiService.postData(ENDPOINTS.SAFETY_TRAINING.SAFETY_TRAINING, data);
  }
  async getSafetyTraining(data: any) {
    return await this.apiService.getData(ENDPOINTS.SAFETY_TRAINING.SAFETY_TRAINING, data);
  }
  async getMaleFemaleCount(year: any) {
    const businessUnitId = this.authService.getBusinessUnitId();
    return await this.apiService.getData(ENDPOINTS.SAFETY_TRAINING.MALE_FEMALE_COUNT, { params: { year: year, businessUnitId: businessUnitId } });
  }
  async  getTrainingTypeCount(year: any){
    const businessUnitId = this.authService.getBusinessUnitId();
    return await this.apiService.getData(ENDPOINTS.SAFETY_TRAINING.TRAINING_TYPE_COUNT, { params: { year: year, businessUnitId: businessUnitId } });
  }

  async getParticipantTypeCount(year: any){
    const businessUnitId = this.authService.getBusinessUnitId();
    return await this.apiService.getData(ENDPOINTS.SAFETY_TRAINING.PARTICIPANT_TYPE_COUNT, { params: { year: year, businessUnitId: businessUnitId } });
  }
  async getTrainingMonthWiseCount(year: any){
    const businessUnitId = this.authService.getBusinessUnitId();
    return await this.apiService.getData(ENDPOINTS.SAFETY_TRAINING.TRAINING_MONTH_WISE_COUNT, { params: { year: year, businessUnitId: businessUnitId } });
  }
  async getTrainingMonthWiseManHourCount(year: any){
    const businessUnitId = this.authService.getBusinessUnitId();
    return await this.apiService.getData(ENDPOINTS.SAFETY_TRAINING.TRAINING_MONTH_WISE_MAN_HOUR_COUNT, { params: { year: year, businessUnitId: businessUnitId } });
  }
  async getAverageParticipantPerTraining(year: any){
    const businessUnitId = this.authService.getBusinessUnitId();
    return await this.apiService.getData(ENDPOINTS.SAFETY_TRAINING.AVERAGE_PARTICIPANT_PER_TRAINING, { params: { year: year, businessUnitId: businessUnitId } });
  }
}
