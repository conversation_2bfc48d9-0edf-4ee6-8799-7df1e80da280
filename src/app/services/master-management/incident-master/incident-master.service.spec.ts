import { TestBed } from '@angular/core/testing';
import { IncidentMasterService } from './incident-master.service';
import { ApiService } from '../../api.service';

describe('IncidentMasterService', () => {
  let service: IncidentMasterService;
  let apiService: jasmine.SpyObj<ApiService>;

  beforeEach(() => {
    const apiSpy = jasmine.createSpyObj('ApiService', ['getData', 'postData']);

    TestBed.configureTestingModule({
      providers: [
        IncidentMasterService,
        { provide: ApiService, useValue: apiSpy }
      ]
    });

    service = TestBed.inject(IncidentMasterService);
    apiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
  });

  beforeEach(() => {
    spyOn(console, 'log');
    spyOn(console, 'error');
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getIncidentMaster', () => {
    it('should fetch incident masters successfully', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const mockResponse = {
        responseCode: 200,
        data: [
          { id: 1, title: 'Fire Incident', enabled: true, severity: 'High' },
          { id: 2, title: 'Chemical Spill', enabled: true, severity: 'Medium' }
        ],
        total: 2
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getIncidentMaster(mockParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), mockParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle API errors when fetching incident masters', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const mockError = new Error('Network Error');
      apiService.getData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.getIncidentMaster(mockParams)).toBeRejectedWith(mockError);
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), mockParams);
    });

    it('should handle filtering by severity', async () => {
      // Arrange
      const filterParams = {
        page: 1,
        limit: 10,
        filter: ['severity||eq||High', 'enabled||eq||true']
      };
      const mockResponse = {
        responseCode: 200,
        data: [{ id: 1, title: 'Fire Incident', enabled: true, severity: 'High' }],
        total: 1
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getIncidentMaster(filterParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), filterParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle sorting parameters', async () => {
      // Arrange
      const sortParams = {
        page: 1,
        limit: 10,
        sort: 'title,ASC'
      };
      const mockResponse = {
        responseCode: 200,
        data: [
          { id: 2, title: 'Chemical Spill', enabled: true, severity: 'Medium' },
          { id: 1, title: 'Fire Incident', enabled: true, severity: 'High' }
        ],
        total: 2
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getIncidentMaster(sortParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), sortParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle empty response data', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const emptyResponse = {
        responseCode: 200,
        data: [],
        total: 0
      };
      apiService.getData.and.returnValue(Promise.resolve(emptyResponse));

      // Act
      const result = await service.getIncidentMaster(mockParams);

      // Assert
      expect(result.data.length).toBe(0);
      expect(result.total).toBe(0);
    });
  });

  describe('createIncidentMaster', () => {
    it('should create incident master successfully', async () => {
      // Arrange
      const newIncidentData = {
        title: 'Equipment Failure',
        description: 'Equipment malfunction incident',
        severity: 'Medium',
        enabled: true
      };
      const mockResponse = {
        responseCode: 200,
        message: 'Incident master created successfully',
        data: { id: 3, ...newIncidentData }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.createIncidentMaster(newIncidentData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), newIncidentData);
      expect(result).toEqual(mockResponse);
    });

    it('should handle validation errors when creating incident master', async () => {
      // Arrange
      const invalidIncidentData = { title: '', severity: '' }; // Invalid data
      const mockError = new Error('Validation Error');
      apiService.postData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.createIncidentMaster(invalidIncidentData)).toBeRejectedWith(mockError);
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), invalidIncidentData);
    });

    it('should create incident master with all severity levels', async () => {
      // Arrange
      const severityLevels = ['Low', 'Medium', 'High', 'Critical'];

      for (const severity of severityLevels) {
        const incidentData = {
          title: `${severity} Incident`,
          description: `${severity} level incident`,
          severity: severity,
          enabled: true
        };
        const mockResponse = {
          responseCode: 200,
          message: 'Incident master created successfully',
          data: { id: Math.random(), ...incidentData }
        };
        apiService.postData.and.returnValue(Promise.resolve(mockResponse));

        // Act
        const result = await service.createIncidentMaster(incidentData);

        // Assert
        expect(result.data.severity).toBe(severity);
      }
    });

    it('should handle duplicate incident master creation', async () => {
      // Arrange
      const duplicateData = {
        title: 'Fire Incident', // Already exists
        severity: 'High',
        enabled: true
      };
      const mockError = new Error('Incident master already exists');
      apiService.postData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.createIncidentMaster(duplicateData)).toBeRejectedWith(mockError);
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), duplicateData);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle network timeout errors', async () => {
      // Arrange
      const timeoutError = new Error('Request timeout');
      timeoutError.name = 'TimeoutError';
      apiService.getData.and.returnValue(Promise.reject(timeoutError));

      // Act & Assert
      await expectAsync(service.getIncidentMaster({})).toBeRejectedWith(timeoutError);
    });

    it('should handle server errors (500)', async () => {
      // Arrange
      const serverError = new Error('Internal Server Error');
      serverError.name = 'ServerError';
      apiService.postData.and.returnValue(Promise.reject(serverError));

      // Act & Assert
      await expectAsync(service.createIncidentMaster({})).toBeRejectedWith(serverError);
    });

    it('should handle unauthorized access errors', async () => {
      // Arrange
      const unauthorizedError = new Error('Unauthorized');
      unauthorizedError.name = 'UnauthorizedError';
      apiService.getData.and.returnValue(Promise.reject(unauthorizedError));

      // Act & Assert
      await expectAsync(service.getIncidentMaster({})).toBeRejectedWith(unauthorizedError);
    });
  });
});
