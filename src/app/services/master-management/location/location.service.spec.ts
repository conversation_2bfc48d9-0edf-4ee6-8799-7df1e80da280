import { TestBed } from '@angular/core/testing';
import { LocationService } from './location.service';
import { ApiService } from '../../api.service';

describe('LocationService', () => {
  let service: LocationService;
  let apiService: jasmine.SpyObj<ApiService>;

  const mockLocationResponse = {
    data: [
      {
        id: 1,
        name: 'Main Production Hall',
        code: 'MPH-001',
        type: 'Production',
        description: 'Primary manufacturing area',
        plantId: 1,
        zoneId: 1,
        areaId: 1,
        enabled: true,
        businessUnitId: 0,
        coordinates: {
          latitude: 23.0225,
          longitude: 72.5714
        },
        address: {
          building: 'Building A',
          floor: 'Ground Floor',
          room: 'Hall 1',
          street: '123 Industrial Street',
          city: 'Ahmedabad',
          state: 'Gujarat',
          country: 'India',
          pincode: '380001'
        },
        capacity: {
          maxOccupancy: 500,
          area: '5000 sqm',
          volume: '25000 cubic meters'
        },
        facilities: ['HVAC', 'Fire Safety', 'Emergency Exits', 'First Aid'],
        safetyFeatures: ['Fire Extinguishers', 'Smoke Detectors', 'Emergency Lighting'],
        accessControl: 'Restricted',
        operatingHours: '24/7',
        createdAt: '2024-01-01T00:00:00Z'
      },
      {
        id: 2,
        name: 'Storage Warehouse B',
        code: 'SWB-002',
        type: 'Storage',
        description: 'Raw materials storage facility',
        plantId: 1,
        zoneId: 2,
        areaId: 2,
        enabled: true,
        businessUnitId: 0,
        coordinates: {
          latitude: 23.0235,
          longitude: 72.5724
        },
        address: {
          building: 'Building B',
          floor: 'Ground Floor',
          room: 'Warehouse B',
          street: '124 Industrial Street',
          city: 'Ahmedabad',
          state: 'Gujarat',
          country: 'India',
          pincode: '380001'
        },
        capacity: {
          maxOccupancy: 50,
          area: '2000 sqm',
          volume: '10000 cubic meters'
        },
        facilities: ['Climate Control', 'Security System', 'Loading Dock'],
        safetyFeatures: ['Security Cameras', 'Motion Sensors', 'Alarm System'],
        accessControl: 'Controlled',
        operatingHours: '6:00 AM - 10:00 PM',
        createdAt: '2024-01-02T00:00:00Z'
      }
    ],
    total: 2,
    page: 1,
    limit: 10
  };

  beforeEach(() => {
    const apiSpy = jasmine.createSpyObj('ApiService', ['getData', 'postData']);

    TestBed.configureTestingModule({
      providers: [
        LocationService,
        { provide: ApiService, useValue: apiSpy }
      ]
    });

    service = TestBed.inject(LocationService);
    apiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
  });

  beforeEach(() => {
    spyOn(console, 'log');
    spyOn(console, 'error');
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getLocation', () => {
    it('should fetch locations successfully', async () => {
      // Arrange
      const params = { page: 1, limit: 10, enabled: true };
      apiService.getData.and.returnValue(Promise.resolve(mockLocationResponse));

      // Act
      const result = await service.getLocation(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(mockLocationResponse);
    });

    it('should fetch locations with filters', async () => {
      // Arrange
      const params = {
        page: 1,
        limit: 10,
        filter: ['enabled||eq||true', 'type||eq||Production', 'plantId||eq||1'],
        sort: 'name,ASC',
        join: ['plant', 'zone', 'area']
      };
      apiService.getData.and.returnValue(Promise.resolve(mockLocationResponse));

      // Act
      const result = await service.getLocation(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(mockLocationResponse);
    });

    it('should handle empty locations response', async () => {
      // Arrange
      const params = { page: 1, limit: 10 };
      const emptyResponse = { data: [], total: 0, page: 1, limit: 10 };
      apiService.getData.and.returnValue(Promise.resolve(emptyResponse));

      // Act
      const result = await service.getLocation(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(emptyResponse);
    });

    it('should handle errors when fetching locations', async () => {
      // Arrange
      const params = { page: 1, limit: 10 };
      const error = new Error('Network Error');
      apiService.getData.and.returnValue(Promise.reject(error));

      // Act & Assert
      await expectAsync(service.getLocation(params)).toBeRejectedWith(error);
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
    });

    it('should fetch locations by type', async () => {
      // Arrange
      const params = {
        page: 1,
        limit: 10,
        filter: ['type||eq||Production']
      };
      const productionLocationsResponse = {
        data: [mockLocationResponse.data[0]], // Only production locations
        total: 1,
        page: 1,
        limit: 10
      };
      apiService.getData.and.returnValue(Promise.resolve(productionLocationsResponse));

      // Act
      const result = await service.getLocation(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(productionLocationsResponse);
      expect(result.data.length).toBe(1);
      expect(result.data[0].type).toBe('Production');
    });

    it('should fetch locations by plant', async () => {
      // Arrange
      const params = {
        page: 1,
        limit: 10,
        filter: ['plantId||eq||1']
      };
      apiService.getData.and.returnValue(Promise.resolve(mockLocationResponse));

      // Act
      const result = await service.getLocation(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(mockLocationResponse);
      expect(result.data.every((location: any) => location.plantId === 1)).toBe(true);
    });

    it('should search locations by name', async () => {
      // Arrange
      const params = {
        page: 1,
        limit: 10,
        filter: ['name||like||Production']
      };
      const searchResponse = {
        data: [mockLocationResponse.data[0]], // Only locations with 'Production' in name
        total: 1,
        page: 1,
        limit: 10
      };
      apiService.getData.and.returnValue(Promise.resolve(searchResponse));

      // Act
      const result = await service.getLocation(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(searchResponse);
      expect(result.data.length).toBe(1);
      expect(result.data[0].name).toContain('Production');
    });

    it('should fetch locations by access control', async () => {
      // Arrange
      const params = {
        page: 1,
        limit: 10,
        filter: ['accessControl||eq||Restricted']
      };
      const restrictedLocationsResponse = {
        data: [mockLocationResponse.data[0]], // Only restricted access locations
        total: 1,
        page: 1,
        limit: 10
      };
      apiService.getData.and.returnValue(Promise.resolve(restrictedLocationsResponse));

      // Act
      const result = await service.getLocation(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(restrictedLocationsResponse);
      expect(result.data.length).toBe(1);
      expect(result.data[0].accessControl).toBe('Restricted');
    });
  });

  describe('createLocation', () => {
    it('should create location successfully', async () => {
      // Arrange
      const newLocationData = {
        name: 'Quality Control Lab',
        code: 'QCL-003',
        type: 'Laboratory',
        description: 'Quality testing and analysis facility',
        plantId: 1,
        zoneId: 3,
        areaId: 3,
        enabled: true,
        businessUnitId: 0,
        coordinates: {
          latitude: 23.0245,
          longitude: 72.5734
        },
        address: {
          building: 'Building C',
          floor: 'First Floor',
          room: 'Lab 1',
          street: '125 Industrial Street',
          city: 'Ahmedabad',
          state: 'Gujarat',
          country: 'India',
          pincode: '380001'
        },
        capacity: {
          maxOccupancy: 20,
          area: '500 sqm',
          volume: '1500 cubic meters'
        },
        facilities: ['Clean Room', 'Fume Hoods', 'Emergency Shower', 'Eye Wash Station'],
        safetyFeatures: ['Chemical Storage', 'Ventilation System', 'Emergency Exits'],
        accessControl: 'Restricted',
        operatingHours: '8:00 AM - 6:00 PM',
        createdBy: 1
      };
      const mockCreateResponse = {
        responseCode: 200,
        message: 'Location created successfully',
        data: {
          id: 3,
          ...newLocationData,
          createdAt: '2024-01-15T00:00:00Z'
        }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockCreateResponse));

      // Act
      const result = await service.createLocation(newLocationData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), newLocationData);
      expect(result).toEqual(mockCreateResponse);
    });

    it('should handle validation errors when creating location', async () => {
      // Arrange
      const invalidLocationData = {
        name: '', // Empty name should cause validation error
        code: '',
        type: '',
        enabled: true
      };
      const validationError = {
        responseCode: 400,
        message: 'Validation failed',
        errors: ['Name is required', 'Code is required', 'Type is required']
      };
      apiService.postData.and.returnValue(Promise.reject(validationError));

      // Act & Assert
      await expectAsync(service.createLocation(invalidLocationData))
        .toBeRejectedWith(validationError);
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), invalidLocationData);
    });

    it('should handle duplicate location creation', async () => {
      // Arrange
      const duplicateLocationData = {
        name: 'Main Production Hall', // Assuming this already exists
        code: 'MPH-001',
        type: 'Production',
        enabled: true,
        businessUnitId: 0
      };
      const duplicateError = {
        responseCode: 409,
        message: 'Location with this code already exists'
      };
      apiService.postData.and.returnValue(Promise.reject(duplicateError));

      // Act & Assert
      await expectAsync(service.createLocation(duplicateLocationData))
        .toBeRejectedWith(duplicateError);
    });

    it('should create location with complete address and coordinates', async () => {
      // Arrange
      const completeLocationData = {
        name: 'Emergency Response Center',
        code: 'ERC-004',
        type: 'Emergency',
        description: 'Central emergency response and coordination facility',
        plantId: 2,
        zoneId: 4,
        areaId: 4,
        enabled: true,
        businessUnitId: 0,
        coordinates: {
          latitude: 23.0255,
          longitude: 72.5744,
          altitude: 100,
          accuracy: 5
        },
        address: {
          building: 'Emergency Building',
          floor: 'Ground Floor',
          room: 'Control Room',
          street: '126 Industrial Street',
          city: 'Ahmedabad',
          state: 'Gujarat',
          country: 'India',
          pincode: '380001',
          landmark: 'Near Main Gate'
        },
        capacity: {
          maxOccupancy: 30,
          area: '300 sqm',
          volume: '900 cubic meters',
          parkingSpaces: 10
        },
        facilities: [
          'Communication Center',
          'Medical Bay',
          'Equipment Storage',
          'Meeting Room',
          'Rest Area'
        ],
        safetyFeatures: [
          'Backup Power',
          'Emergency Communication',
          'First Aid Equipment',
          'Fire Suppression System',
          'Security System'
        ],
        accessControl: 'Emergency Personnel Only',
        operatingHours: '24/7',
        emergencyContact: {
          name: 'Emergency Coordinator',
          phone: '+91-**********',
          email: '<EMAIL>'
        },
        equipmentList: ['Radios', 'Medical Supplies', 'Safety Gear'],
        createdBy: 1
      };
      const mockResponse = {
        responseCode: 200,
        message: 'Emergency location created successfully',
        data: { id: 4, ...completeLocationData }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.createLocation(completeLocationData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), completeLocationData);
      expect(result).toEqual(mockResponse);
    });

    it('should handle network errors when creating location', async () => {
      // Arrange
      const locationData = {
        name: 'Network Test Location',
        code: 'NTL-005',
        type: 'Test',
        enabled: true
      };
      const networkError = new Error('Network connection failed');
      apiService.postData.and.returnValue(Promise.reject(networkError));

      // Act & Assert
      await expectAsync(service.createLocation(locationData)).toBeRejectedWith(networkError);
    });

    it('should create outdoor location', async () => {
      // Arrange
      const outdoorLocationData = {
        name: 'Outdoor Storage Yard',
        code: 'OSY-006',
        type: 'Outdoor',
        description: 'Open air storage for bulk materials',
        plantId: 1,
        zoneId: 5,
        areaId: 5,
        enabled: true,
        businessUnitId: 0,
        coordinates: {
          latitude: 23.0265,
          longitude: 72.5754
        },
        capacity: {
          maxOccupancy: 100,
          area: '10000 sqm',
          volume: 'Open air'
        },
        facilities: ['Drainage System', 'Lighting', 'Fencing', 'Access Roads'],
        safetyFeatures: ['Perimeter Security', 'Weather Monitoring', 'Emergency Access'],
        accessControl: 'Controlled',
        operatingHours: 'Daylight hours',
        weatherProtection: false,
        surfaceType: 'Concrete',
        drainageCapacity: '1000 L/min',
        createdBy: 1
      };
      const mockResponse = {
        responseCode: 200,
        message: 'Outdoor location created successfully',
        data: { id: 6, ...outdoorLocationData }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.createLocation(outdoorLocationData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), outdoorLocationData);
      expect(result).toEqual(mockResponse);
      expect(result.data.type).toBe('Outdoor');
    });

    it('should create disabled location', async () => {
      // Arrange
      const disabledLocationData = {
        name: 'Decommissioned Area',
        code: 'DA-007',
        type: 'Decommissioned',
        description: 'Area no longer in use',
        enabled: false,
        businessUnitId: 0,
        decommissionDate: '2024-01-15T00:00:00Z',
        decommissionReason: 'Facility upgrade',
        createdBy: 1
      };
      const mockResponse = {
        responseCode: 200,
        message: 'Disabled location created successfully',
        data: { id: 7, ...disabledLocationData }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.createLocation(disabledLocationData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), disabledLocationData);
      expect(result).toEqual(mockResponse);
      expect(result.data.enabled).toBe(false);
    });

    it('should handle null or undefined data', async () => {
      // Arrange
      const nullData = null;
      const mockResponse = {
        responseCode: 400,
        message: 'Invalid data provided'
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.createLocation(nullData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), nullData);
      expect(result).toEqual(mockResponse);
    });
  });
});
