import { TestBed } from '@angular/core/testing';
import { OpcoService } from './opco.service';
import { ApiService } from '../../api.service';

describe('OpcoService', () => {
  let service: OpcoService;
  let apiService: jasmine.SpyObj<ApiService>;

  beforeEach(() => {
    const apiSpy = jasmine.createSpyObj('ApiService', ['getData', 'postData']);

    TestBed.configureTestingModule({
      providers: [
        OpcoService,
        { provide: ApiService, useValue: apiSpy }
      ]
    });

    service = TestBed.inject(OpcoService);
    apiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
  });

  beforeEach(() => {
    spyOn(console, 'log');
    spyOn(console, 'error');
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getOpco', () => {
    it('should fetch opcos successfully', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const mockResponse = {
        responseCode: 200,
        data: [
          { id: 1, title: 'OPCO A', enabled: true },
          { id: 2, title: 'OPCO B', enabled: true }
        ],
        total: 2
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getOpco(mockParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), mockParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle API errors when fetching opcos', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const mockError = new Error('Network Error');
      apiService.getData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.getOpco(mockParams)).toBeRejectedWith(mockError);
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), mockParams);
    });

    it('should handle filtering parameters', async () => {
      // Arrange
      const filterParams = {
        page: 1,
        limit: 10,
        filter: ['enabled||eq||true', 'title||like||%opco%']
      };
      const mockResponse = {
        responseCode: 200,
        data: [{ id: 1, title: 'Main OPCO', enabled: true }],
        total: 1
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getOpco(filterParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), filterParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle empty response data', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const emptyResponse = {
        responseCode: 200,
        data: [],
        total: 0
      };
      apiService.getData.and.returnValue(Promise.resolve(emptyResponse));

      // Act
      const result = await service.getOpco(mockParams);

      // Assert
      expect(result.data.length).toBe(0);
      expect(result.total).toBe(0);
    });
  });

  describe('createOpco', () => {
    it('should create opco successfully', async () => {
      // Arrange
      const newOpcoData = {
        title: 'New OPCO',
        description: 'OPCO description',
        enabled: true
      };
      const mockResponse = {
        responseCode: 200,
        message: 'OPCO created successfully',
        data: { id: 3, ...newOpcoData }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.createOpco(newOpcoData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), newOpcoData);
      expect(result).toEqual(mockResponse);
    });

    it('should handle validation errors when creating opco', async () => {
      // Arrange
      const invalidOpcoData = { title: '' }; // Invalid data
      const mockError = new Error('Validation Error');
      apiService.postData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.createOpco(invalidOpcoData)).toBeRejectedWith(mockError);
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), invalidOpcoData);
    });
  });

  describe('getFacility', () => {
    it('should fetch facilities successfully', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10, opcoId: 1 };
      const mockResponse = {
        responseCode: 200,
        data: [
          { id: 1, name: 'Facility A', opcoId: 1, enabled: true },
          { id: 2, name: 'Facility B', opcoId: 1, enabled: true }
        ],
        total: 2
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getFacility(mockParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), mockParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle API errors when fetching facilities', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10, opcoId: 1 };
      const mockError = new Error('Network Error');
      apiService.getData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.getFacility(mockParams)).toBeRejectedWith(mockError);
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), mockParams);
    });

    it('should handle empty facilities response', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10, opcoId: 999 };
      const emptyResponse = {
        responseCode: 200,
        data: [],
        total: 0
      };
      apiService.getData.and.returnValue(Promise.resolve(emptyResponse));

      // Act
      const result = await service.getFacility(mockParams);

      // Assert
      expect(result.data.length).toBe(0);
      expect(result.total).toBe(0);
    });
  });
});
