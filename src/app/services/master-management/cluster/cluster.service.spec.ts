import { TestBed } from '@angular/core/testing';
import { ClusterService } from './cluster.service';
import { ApiService } from '../../api.service';

describe('ClusterService', () => {
  let service: ClusterService;
  let apiService: jasmine.SpyObj<ApiService>;

  const mockClusterResponse = {
    data: [
      {
        id: 1,
        name: 'North Cluster',
        description: 'Northern region cluster',
        enabled: true,
        businessUnitId: 0,
        createdAt: '2024-01-01T00:00:00Z',
        plants: [
          { id: 1, name: 'Plant Alpha' },
          { id: 2, name: 'Plant Beta' }
        ]
      },
      {
        id: 2,
        name: 'South Cluster',
        description: 'Southern region cluster',
        enabled: true,
        businessUnitId: 0,
        createdAt: '2024-01-02T00:00:00Z',
        plants: [
          { id: 3, name: 'Plant Gamma' }
        ]
      }
    ],
    total: 2,
    page: 1,
    limit: 10
  };

  beforeEach(() => {
    const apiSpy = jasmine.createSpyObj('ApiService', ['getData', 'postData']);

    TestBed.configureTestingModule({
      providers: [
        ClusterService,
        { provide: ApiService, useValue: apiSpy }
      ]
    });

    service = TestBed.inject(ClusterService);
    apiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
  });

  beforeEach(() => {
    spyOn(console, 'log');
    spyOn(console, 'error');
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getCluster', () => {
    it('should fetch clusters successfully', async () => {
      // Arrange
      const params = { page: 1, limit: 10, enabled: true };
      apiService.getData.and.returnValue(Promise.resolve(mockClusterResponse));

      // Act
      const result = await service.getCluster(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(mockClusterResponse);
    });

    it('should fetch clusters with filters', async () => {
      // Arrange
      const params = {
        page: 1,
        limit: 10,
        filter: ['enabled||eq||true', 'businessUnitId||eq||0'],
        sort: 'name,ASC',
        join: ['plants']
      };
      apiService.getData.and.returnValue(Promise.resolve(mockClusterResponse));

      // Act
      const result = await service.getCluster(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(mockClusterResponse);
    });

    it('should handle empty clusters response', async () => {
      // Arrange
      const params = { page: 1, limit: 10 };
      const emptyResponse = { data: [], total: 0, page: 1, limit: 10 };
      apiService.getData.and.returnValue(Promise.resolve(emptyResponse));

      // Act
      const result = await service.getCluster(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(emptyResponse);
    });

    it('should handle errors when fetching clusters', async () => {
      // Arrange
      const params = { page: 1, limit: 10 };
      const error = new Error('Network Error');
      apiService.getData.and.returnValue(Promise.reject(error));

      // Act & Assert
      await expectAsync(service.getCluster(params)).toBeRejectedWith(error);
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
    });

    it('should fetch enabled clusters only', async () => {
      // Arrange
      const params = {
        page: 1,
        limit: 10,
        filter: ['enabled||eq||true']
      };
      apiService.getData.and.returnValue(Promise.resolve(mockClusterResponse));

      // Act
      const result = await service.getCluster(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(mockClusterResponse);
      expect(result.data.every((cluster: any) => cluster.enabled === true)).toBe(true);
    });

    it('should fetch clusters with plant relationships', async () => {
      // Arrange
      const params = {
        page: 1,
        limit: 10,
        join: ['plants']
      };
      apiService.getData.and.returnValue(Promise.resolve(mockClusterResponse));

      // Act
      const result = await service.getCluster(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(mockClusterResponse);
      expect(result.data[0].plants).toBeDefined();
      expect(result.data[0].plants.length).toBe(2);
    });

    it('should handle search by cluster name', async () => {
      // Arrange
      const params = {
        page: 1,
        limit: 10,
        filter: ['name||like||North']
      };
      const northClusterResponse = {
        data: [mockClusterResponse.data[0]], // Only North cluster
        total: 1,
        page: 1,
        limit: 10
      };
      apiService.getData.and.returnValue(Promise.resolve(northClusterResponse));

      // Act
      const result = await service.getCluster(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(northClusterResponse);
      expect(result.data.length).toBe(1);
      expect(result.data[0].name).toContain('North');
    });
  });

  describe('createCluster', () => {
    it('should create cluster successfully', async () => {
      // Arrange
      const newClusterData = {
        name: 'East Cluster',
        description: 'Eastern region cluster',
        enabled: true,
        businessUnitId: 0,
        createdBy: 1
      };
      const mockCreateResponse = {
        responseCode: 200,
        message: 'Cluster created successfully',
        data: {
          id: 3,
          ...newClusterData,
          createdAt: '2024-01-15T00:00:00Z'
        }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockCreateResponse));

      // Act
      const result = await service.createCluster(newClusterData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), newClusterData);
      expect(result).toEqual(mockCreateResponse);
    });

    it('should handle validation errors when creating cluster', async () => {
      // Arrange
      const invalidClusterData = {
        name: '', // Empty name should cause validation error
        description: '',
        enabled: true
      };
      const validationError = {
        responseCode: 400,
        message: 'Validation failed',
        errors: ['Name is required', 'Description is required']
      };
      apiService.postData.and.returnValue(Promise.reject(validationError));

      // Act & Assert
      await expectAsync(service.createCluster(invalidClusterData))
        .toBeRejectedWith(validationError);
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), invalidClusterData);
    });

    it('should handle duplicate cluster creation', async () => {
      // Arrange
      const duplicateClusterData = {
        name: 'North Cluster', // Assuming this already exists
        description: 'Duplicate cluster',
        enabled: true,
        businessUnitId: 0
      };
      const duplicateError = {
        responseCode: 409,
        message: 'Cluster with this name already exists'
      };
      apiService.postData.and.returnValue(Promise.reject(duplicateError));

      // Act & Assert
      await expectAsync(service.createCluster(duplicateClusterData))
        .toBeRejectedWith(duplicateError);
    });

    it('should create cluster with all required fields', async () => {
      // Arrange
      const completeClusterData = {
        name: 'West Cluster',
        description: 'Western region cluster with complete data',
        enabled: true,
        businessUnitId: 0,
        region: 'West',
        managerId: 5,
        contactEmail: '<EMAIL>',
        contactPhone: '+91-9876543210',
        createdBy: 1
      };
      const mockResponse = {
        responseCode: 200,
        message: 'Cluster created successfully',
        data: { id: 4, ...completeClusterData }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.createCluster(completeClusterData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), completeClusterData);
      expect(result).toEqual(mockResponse);
    });

    it('should handle network errors when creating cluster', async () => {
      // Arrange
      const clusterData = {
        name: 'Network Test Cluster',
        description: 'Test cluster for network error',
        enabled: true
      };
      const networkError = new Error('Network connection failed');
      apiService.postData.and.returnValue(Promise.reject(networkError));

      // Act & Assert
      await expectAsync(service.createCluster(clusterData)).toBeRejectedWith(networkError);
    });

    it('should create disabled cluster', async () => {
      // Arrange
      const disabledClusterData = {
        name: 'Disabled Cluster',
        description: 'Cluster created in disabled state',
        enabled: false,
        businessUnitId: 0,
        createdBy: 1
      };
      const mockResponse = {
        responseCode: 200,
        message: 'Disabled cluster created successfully',
        data: { id: 5, ...disabledClusterData }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.createCluster(disabledClusterData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), disabledClusterData);
      expect(result).toEqual(mockResponse);
      expect(result.data.enabled).toBe(false);
    });

    it('should handle null or undefined data', async () => {
      // Arrange
      const nullData = null;
      const mockResponse = {
        responseCode: 400,
        message: 'Invalid data provided'
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.createCluster(nullData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), nullData);
      expect(result).toEqual(mockResponse);
    });

    it('should create cluster with custom properties', async () => {
      // Arrange
      const customClusterData = {
        name: 'Custom Cluster',
        description: 'Cluster with custom properties',
        enabled: true,
        businessUnitId: 0,
        customProperties: {
          timezone: 'Asia/Kolkata',
          operatingHours: '24/7',
          emergencyContact: '+91-1234567890'
        },
        tags: ['production', 'critical'],
        createdBy: 1
      };
      const mockResponse = {
        responseCode: 200,
        message: 'Custom cluster created successfully',
        data: { id: 6, ...customClusterData }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.createCluster(customClusterData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), customClusterData);
      expect(result).toEqual(mockResponse);
    });
  });
});
