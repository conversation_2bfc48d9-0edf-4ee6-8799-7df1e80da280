import { TestBed } from '@angular/core/testing';
import { SegmentService } from './segment.service';
import { ApiService } from '../../api.service';

describe('SegmentService', () => {
  let service: SegmentService;
  let apiService: jasmine.SpyObj<ApiService>;

  beforeEach(() => {
    const apiSpy = jasmine.createSpyObj('ApiService', ['getData', 'postData', 'putData', 'deleteData']);

    TestBed.configureTestingModule({
      providers: [
        SegmentService,
        { provide: ApiService, useValue: apiSpy }
      ]
    });

    service = TestBed.inject(SegmentService);
    apiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
  });

  beforeEach(() => {
    spyOn(console, 'log');
    spyOn(console, 'error');
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getSegment', () => {
    it('should fetch segments successfully', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const mockResponse = {
        responseCode: 200,
        data: [
          { id: 1, title: 'Production Segment', enabled: true, type: 'Manufacturing', description: 'Production and manufacturing operations' },
          { id: 2, title: 'Quality Segment', enabled: true, type: 'Quality Control', description: 'Quality assurance and control' }
        ],
        total: 2
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getSegment(mockParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), mockParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle API errors when fetching segments', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const mockError = new Error('Network Error');
      apiService.getData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.getSegment(mockParams)).toBeRejectedWith(mockError);
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), mockParams);
    });

    it('should handle filtering by type', async () => {
      // Arrange
      const filterParams = {
        page: 1,
        limit: 10,
        filter: ['type||eq||Manufacturing', 'enabled||eq||true']
      };
      const mockResponse = {
        responseCode: 200,
        data: [{ id: 1, title: 'Production Segment', enabled: true, type: 'Manufacturing', description: 'Production and manufacturing operations' }],
        total: 1
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getSegment(filterParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), filterParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle search by title', async () => {
      // Arrange
      const searchParams = {
        page: 1,
        limit: 10,
        filter: ['title||like||%quality%']
      };
      const mockResponse = {
        responseCode: 200,
        data: [{ id: 2, title: 'Quality Segment', enabled: true, type: 'Quality Control', description: 'Quality assurance and control' }],
        total: 1
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getSegment(searchParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), searchParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle sorting parameters', async () => {
      // Arrange
      const sortParams = {
        page: 1,
        limit: 10,
        sort: 'title,ASC'
      };
      const mockResponse = {
        responseCode: 200,
        data: [
          { id: 1, title: 'Production Segment', enabled: true, type: 'Manufacturing', description: 'Production and manufacturing operations' },
          { id: 2, title: 'Quality Segment', enabled: true, type: 'Quality Control', description: 'Quality assurance and control' }
        ],
        total: 2
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getSegment(sortParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), sortParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle empty response data', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const emptyResponse = {
        responseCode: 200,
        data: [],
        total: 0
      };
      apiService.getData.and.returnValue(Promise.resolve(emptyResponse));

      // Act
      const result = await service.getSegment(mockParams);

      // Assert
      expect(result.data.length).toBe(0);
      expect(result.total).toBe(0);
    });
  });

  describe('createSegment', () => {
    it('should create segment successfully', async () => {
      // Arrange
      const newSegmentData = {
        title: 'Safety Segment',
        description: 'Safety and security operations',
        type: 'Safety',
        enabled: true
      };
      const mockResponse = {
        responseCode: 200,
        message: 'Segment created successfully',
        data: { id: 3, ...newSegmentData }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.createSegment(newSegmentData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), newSegmentData);
      expect(result).toEqual(mockResponse);
    });

    it('should handle validation errors when creating segment', async () => {
      // Arrange
      const invalidSegmentData = { title: '', type: '' }; // Invalid data
      const mockError = new Error('Validation Error');
      apiService.postData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.createSegment(invalidSegmentData)).toBeRejectedWith(mockError);
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), invalidSegmentData);
    });

    it('should handle duplicate segment creation', async () => {
      // Arrange
      const duplicateData = {
        title: 'Production Segment', // Already exists
        type: 'Manufacturing',
        enabled: true
      };
      const mockError = new Error('Segment already exists');
      apiService.postData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.createSegment(duplicateData)).toBeRejectedWith(mockError);
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), duplicateData);
    });

    it('should create segment with different types', async () => {
      // Arrange
      const segmentTypes = ['Manufacturing', 'Quality Control', 'Safety', 'Environmental', 'Maintenance'];

      for (const type of segmentTypes) {
        const segmentData = {
          title: `${type} Segment`,
          description: `${type} operations and management`,
          type: type,
          enabled: true
        };
        const mockResponse = {
          responseCode: 200,
          message: 'Segment created successfully',
          data: { id: Math.random(), ...segmentData }
        };
        apiService.postData.and.returnValue(Promise.resolve(mockResponse));

        // Act
        const result = await service.createSegment(segmentData);

        // Assert
        expect(result.data.type).toBe(type);
      }
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle network timeout errors', async () => {
      // Arrange
      const timeoutError = new Error('Request timeout');
      timeoutError.name = 'TimeoutError';
      apiService.getData.and.returnValue(Promise.reject(timeoutError));

      // Act & Assert
      await expectAsync(service.getSegment({})).toBeRejectedWith(timeoutError);
    });

    it('should handle server errors (500)', async () => {
      // Arrange
      const serverError = new Error('Internal Server Error');
      serverError.name = 'ServerError';
      apiService.postData.and.returnValue(Promise.reject(serverError));

      // Act & Assert
      await expectAsync(service.createSegment({})).toBeRejectedWith(serverError);
    });

    it('should handle unauthorized access errors', async () => {
      // Arrange
      const unauthorizedError = new Error('Unauthorized');
      unauthorizedError.name = 'UnauthorizedError';
      apiService.getData.and.returnValue(Promise.reject(unauthorizedError));

      // Act & Assert
      await expectAsync(service.getSegment({})).toBeRejectedWith(unauthorizedError);
    });

    it('should handle malformed response data', async () => {
      // Arrange
      const malformedResponse = {
        responseCode: 200,
        data: null, // Malformed data
        total: 0
      };
      apiService.getData.and.returnValue(Promise.resolve(malformedResponse));

      // Act
      const result = await service.getSegment({});

      // Assert
      expect(result.data).toBeNull();
      expect(result.total).toBe(0);
    });

    it('should handle segment hierarchy edge cases', async () => {
      // Arrange
      const hierarchicalSegmentData = {
        title: 'Parent Segment',
        description: 'Top-level segment with children',
        type: 'Hierarchical',
        enabled: true,
        parentId: null,
        children: [
          { title: 'Child Segment 1', type: 'Manufacturing' },
          { title: 'Child Segment 2', type: 'Quality Control' }
        ]
      };
      const mockResponse = {
        responseCode: 200,
        message: 'Segment created successfully',
        data: { id: 99, ...hierarchicalSegmentData }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.createSegment(hierarchicalSegmentData);

      // Assert
      expect(result.data.type).toBe('Hierarchical');
      expect(result.data.children).toBeDefined();
      expect(result.data.children.length).toBe(2);
    });
  });
});
