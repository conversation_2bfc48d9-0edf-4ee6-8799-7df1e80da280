import { TestBed } from '@angular/core/testing';
import { AreaService } from './area.service';
import { ApiService } from '../../api.service';

describe('AreaService', () => {
  let service: AreaService;
  let apiService: jasmine.SpyObj<ApiService>;

  beforeEach(() => {
    const apiSpy = jasmine.createSpyObj('ApiService', ['getData', 'postData', 'putData', 'deleteData']);

    TestBed.configureTestingModule({
      providers: [
        AreaService,
        { provide: ApiService, useValue: apiSpy }
      ]
    });

    service = TestBed.inject(AreaService);
    apiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
  });

  beforeEach(() => {
    spyOn(console, 'log');
    spyOn(console, 'error');
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getArea', () => {
    it('should fetch areas successfully', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const mockResponse = {
        responseCode: 200,
        data: [
          { id: 1, title: 'Area 1', enabled: true },
          { id: 2, title: 'Area 2', enabled: true }
        ],
        total: 2
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getArea(mockParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), mockParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle API errors when fetching areas', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const mockError = new Error('Network Error');
      apiService.getData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.getArea(mockParams)).toBeRejectedWith(mockError);
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), mockParams);
    });

    it('should handle empty parameters', async () => {
      // Arrange
      const mockResponse = { responseCode: 200, data: [], total: 0 };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getArea({});

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), {});
      expect(result).toEqual(mockResponse);
    });

    it('should handle null parameters', async () => {
      // Arrange
      const mockResponse = { responseCode: 200, data: [], total: 0 };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getArea(null);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), null);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('getArea with different parameters', () => {
    it('should handle filtering parameters', async () => {
      // Arrange
      const filterParams = {
        page: 1,
        limit: 10,
        filter: ['enabled||eq||true', 'title||like||%area%']
      };
      const mockResponse = {
        responseCode: 200,
        data: [{ id: 1, title: 'Filtered Area', enabled: true }],
        total: 1
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getArea(filterParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), filterParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle sorting parameters', async () => {
      // Arrange
      const sortParams = {
        page: 1,
        limit: 10,
        sort: 'title,ASC'
      };
      const mockResponse = {
        responseCode: 200,
        data: [
          { id: 1, title: 'Area A', enabled: true },
          { id: 2, title: 'Area B', enabled: true }
        ],
        total: 2
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getArea(sortParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), sortParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle search parameters', async () => {
      // Arrange
      const searchParams = {
        page: 1,
        limit: 10,
        search: 'production'
      };
      const mockResponse = {
        responseCode: 200,
        data: [{ id: 1, title: 'Production Area', enabled: true }],
        total: 1
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getArea(searchParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), searchParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle combined filter and sort parameters', async () => {
      // Arrange
      const combinedParams = {
        page: 1,
        limit: 5,
        filter: ['enabled||eq||true'],
        sort: 'title,DESC',
        search: 'area'
      };
      const mockResponse = {
        responseCode: 200,
        data: [
          { id: 2, title: 'Zone Area', enabled: true },
          { id: 1, title: 'Main Area', enabled: true }
        ],
        total: 2
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getArea(combinedParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), combinedParams);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle network timeout errors', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const timeoutError = new Error('Request timeout');
      timeoutError.name = 'TimeoutError';
      apiService.getData.and.returnValue(Promise.reject(timeoutError));

      // Act & Assert
      await expectAsync(service.getArea(mockParams)).toBeRejectedWith(timeoutError);
    });

    it('should handle server errors (500)', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const serverError = new Error('Internal Server Error');
      serverError.name = 'ServerError';
      apiService.getData.and.returnValue(Promise.reject(serverError));

      // Act & Assert
      await expectAsync(service.getArea(mockParams)).toBeRejectedWith(serverError);
    });

    it('should handle large datasets', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 1000 };
      const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
        id: i + 1,
        title: `Area ${i + 1}`,
        enabled: true
      }));
      const mockResponse = {
        responseCode: 200,
        data: largeDataset,
        total: 1000
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getArea(mockParams);

      // Assert
      expect(result.data.length).toBe(1000);
      expect(result.total).toBe(1000);
    });
  });
});
