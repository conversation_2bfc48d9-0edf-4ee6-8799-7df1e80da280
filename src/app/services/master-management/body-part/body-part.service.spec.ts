import { TestBed } from '@angular/core/testing';
import { BodyPartService } from './body-part.service';
import { ApiService } from '../../api.service';

describe('BodyPartService', () => {
  let service: BodyPartService;
  let apiService: jasmine.SpyObj<ApiService>;

  beforeEach(() => {
    const apiSpy = jasmine.createSpyObj('ApiService', ['getData', 'postData', 'putData', 'deleteData']);

    TestBed.configureTestingModule({
      providers: [
        BodyPartService,
        { provide: ApiService, useValue: apiSpy }
      ]
    });

    service = TestBed.inject(BodyPartService);
    apiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
  });

  beforeEach(() => {
    spyOn(console, 'log');
    spyOn(console, 'error');
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getBodyPart', () => {
    it('should fetch body parts successfully', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const mockResponse = {
        responseCode: 200,
        data: [
          { id: 1, title: 'Head', enabled: true },
          { id: 2, title: 'Hand', enabled: true },
          { id: 3, title: 'Leg', enabled: true }
        ],
        total: 3
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getBodyPart(mockParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), mockParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle API errors when fetching body parts', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const mockError = new Error('Network Error');
      apiService.getData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.getBodyPart(mockParams)).toBeRejectedWith(mockError);
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), mockParams);
    });

    it('should handle filtering parameters', async () => {
      // Arrange
      const filterParams = {
        page: 1,
        limit: 10,
        filter: ['enabled||eq||true', 'title||like||%hand%']
      };
      const mockResponse = {
        responseCode: 200,
        data: [{ id: 2, title: 'Hand', enabled: true }],
        total: 1
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getBodyPart(filterParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), filterParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle sorting parameters', async () => {
      // Arrange
      const sortParams = {
        page: 1,
        limit: 10,
        sort: 'title,ASC'
      };
      const mockResponse = {
        responseCode: 200,
        data: [
          { id: 1, title: 'Hand', enabled: true },
          { id: 2, title: 'Head', enabled: true },
          { id: 3, title: 'Leg', enabled: true }
        ],
        total: 3
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getBodyPart(sortParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), sortParams);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('createBodyPart', () => {
    it('should create body part successfully', async () => {
      // Arrange
      const newBodyPartData = {
        title: 'Shoulder',
        description: 'Shoulder body part',
        enabled: true
      };
      const mockResponse = {
        responseCode: 200,
        message: 'Body part created successfully',
        data: { id: 4, ...newBodyPartData }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.createBodyPart(newBodyPartData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), newBodyPartData);
      expect(result).toEqual(mockResponse);
    });

    it('should handle duplicate body part creation', async () => {
      // Arrange
      const duplicateData = {
        title: 'Head', // Already exists
        enabled: true
      };
      const mockError = new Error('Body part already exists');
      apiService.postData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.createBodyPart(duplicateData)).toBeRejectedWith(mockError);
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), duplicateData);
    });

    it('should handle validation errors', async () => {
      // Arrange
      const invalidData = {
        title: '', // Empty title
        enabled: true
      };
      const mockError = new Error('Title is required');
      apiService.postData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.createBodyPart(invalidData)).toBeRejectedWith(mockError);
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), invalidData);
    });

    it('should handle special characters in body part names', async () => {
      // Arrange
      const specialCharData = {
        title: 'Left-Hand (Primary)',
        description: 'Body part with special chars & symbols',
        enabled: true
      };
      const mockResponse = {
        responseCode: 200,
        message: 'Body part created successfully',
        data: { id: 5, ...specialCharData }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.createBodyPart(specialCharData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), specialCharData);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle unauthorized access errors', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const unauthorizedError = new Error('Unauthorized');
      unauthorizedError.name = 'UnauthorizedError';
      apiService.getData.and.returnValue(Promise.reject(unauthorizedError));

      // Act & Assert
      await expectAsync(service.getBodyPart(mockParams)).toBeRejectedWith(unauthorizedError);
    });

    it('should handle empty response data', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const emptyResponse = {
        responseCode: 200,
        data: [],
        total: 0
      };
      apiService.getData.and.returnValue(Promise.resolve(emptyResponse));

      // Act
      const result = await service.getBodyPart(mockParams);

      // Assert
      expect(result.data.length).toBe(0);
      expect(result.total).toBe(0);
    });
  });
});
