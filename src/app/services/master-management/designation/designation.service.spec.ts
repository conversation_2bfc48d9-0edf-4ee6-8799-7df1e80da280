import { TestBed } from '@angular/core/testing';
import { DesignationService } from './designation.service';
import { ApiService } from '../../api.service';

describe('DesignationService', () => {
  let service: DesignationService;
  let apiService: jasmine.SpyObj<ApiService>;

  const mockDesignationResponse = {
    data: [
      {
        id: 1,
        title: 'Safety Manager',
        description: 'Responsible for safety operations',
        enabled: true,
        businessUnitId: 0,
        level: 'manager',
        department: 'Safety',
        createdAt: '2024-01-01T00:00:00Z'
      },
      {
        id: 2,
        title: 'Plant Supervisor',
        description: 'Supervises plant operations',
        enabled: true,
        businessUnitId: 0,
        level: 'supervisor',
        department: 'Operations',
        createdAt: '2024-01-02T00:00:00Z'
      },
      {
        id: 3,
        title: 'Quality Inspector',
        description: 'Inspects quality standards',
        enabled: true,
        businessUnitId: 0,
        level: 'inspector',
        department: 'Quality',
        createdAt: '2024-01-03T00:00:00Z'
      }
    ],
    total: 3,
    page: 1,
    limit: 10
  };

  beforeEach(() => {
    const apiSpy = jasmine.createSpyObj('ApiService', ['getData', 'postData']);

    TestBed.configureTestingModule({
      providers: [
        DesignationService,
        { provide: ApiService, useValue: apiSpy }
      ]
    });

    service = TestBed.inject(DesignationService);
    apiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
  });

  beforeEach(() => {
    spyOn(console, 'log');
    spyOn(console, 'error');
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getDesignation', () => {
    it('should fetch designations successfully', async () => {
      // Arrange
      const params = { page: 1, limit: 10, enabled: true };
      apiService.getData.and.returnValue(Promise.resolve(mockDesignationResponse));

      // Act
      const result = await service.getDesignation(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(mockDesignationResponse);
    });

    it('should fetch designations with filters', async () => {
      // Arrange
      const params = {
        page: 1,
        limit: 10,
        filter: ['enabled||eq||true', 'level||eq||manager'],
        sort: 'title,ASC'
      };
      apiService.getData.and.returnValue(Promise.resolve(mockDesignationResponse));

      // Act
      const result = await service.getDesignation(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(mockDesignationResponse);
    });

    it('should handle empty designations response', async () => {
      // Arrange
      const params = { page: 1, limit: 10 };
      const emptyResponse = { data: [], total: 0, page: 1, limit: 10 };
      apiService.getData.and.returnValue(Promise.resolve(emptyResponse));

      // Act
      const result = await service.getDesignation(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(emptyResponse);
    });

    it('should handle errors when fetching designations', async () => {
      // Arrange
      const params = { page: 1, limit: 10 };
      const error = new Error('Network Error');
      apiService.getData.and.returnValue(Promise.reject(error));

      // Act & Assert
      await expectAsync(service.getDesignation(params)).toBeRejectedWith(error);
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
    });

    it('should fetch designations by level', async () => {
      // Arrange
      const params = {
        page: 1,
        limit: 10,
        filter: ['level||eq||manager']
      };
      const managerDesignationsResponse = {
        data: [mockDesignationResponse.data[0]], // Only manager level
        total: 1,
        page: 1,
        limit: 10
      };
      apiService.getData.and.returnValue(Promise.resolve(managerDesignationsResponse));

      // Act
      const result = await service.getDesignation(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(managerDesignationsResponse);
      expect(result.data.length).toBe(1);
      expect(result.data[0].level).toBe('manager');
    });

    it('should fetch designations by department', async () => {
      // Arrange
      const params = {
        page: 1,
        limit: 10,
        filter: ['department||eq||Safety']
      };
      const safetyDesignationsResponse = {
        data: [mockDesignationResponse.data[0]], // Only Safety department
        total: 1,
        page: 1,
        limit: 10
      };
      apiService.getData.and.returnValue(Promise.resolve(safetyDesignationsResponse));

      // Act
      const result = await service.getDesignation(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(safetyDesignationsResponse);
      expect(result.data.length).toBe(1);
      expect(result.data[0].department).toBe('Safety');
    });

    it('should search designations by title', async () => {
      // Arrange
      const params = {
        page: 1,
        limit: 10,
        filter: ['title||like||Manager']
      };
      const managerTitleResponse = {
        data: [mockDesignationResponse.data[0]], // Only titles containing 'Manager'
        total: 1,
        page: 1,
        limit: 10
      };
      apiService.getData.and.returnValue(Promise.resolve(managerTitleResponse));

      // Act
      const result = await service.getDesignation(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(managerTitleResponse);
      expect(result.data.length).toBe(1);
      expect(result.data[0].title).toContain('Manager');
    });
  });

  describe('createDesignation', () => {
    it('should create designation successfully', async () => {
      // Arrange
      const newDesignationData = {
        title: 'Senior Engineer',
        description: 'Senior engineering position',
        enabled: true,
        businessUnitId: 0,
        level: 'senior',
        department: 'Engineering',
        createdBy: 1
      };
      const mockCreateResponse = {
        responseCode: 200,
        message: 'Designation created successfully',
        data: {
          id: 4,
          ...newDesignationData,
          createdAt: '2024-01-15T00:00:00Z'
        }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockCreateResponse));

      // Act
      const result = await service.createDesignation(newDesignationData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), newDesignationData);
      expect(result).toEqual(mockCreateResponse);
    });

    it('should handle validation errors when creating designation', async () => {
      // Arrange
      const invalidDesignationData = {
        title: '', // Empty title should cause validation error
        description: '',
        enabled: true
      };
      const validationError = {
        responseCode: 400,
        message: 'Validation failed',
        errors: ['Title is required', 'Description is required']
      };
      apiService.postData.and.returnValue(Promise.reject(validationError));

      // Act & Assert
      await expectAsync(service.createDesignation(invalidDesignationData))
        .toBeRejectedWith(validationError);
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), invalidDesignationData);
    });

    it('should handle duplicate designation creation', async () => {
      // Arrange
      const duplicateDesignationData = {
        title: 'Safety Manager', // Assuming this already exists
        description: 'Duplicate designation',
        enabled: true,
        businessUnitId: 0
      };
      const duplicateError = {
        responseCode: 409,
        message: 'Designation with this title already exists'
      };
      apiService.postData.and.returnValue(Promise.reject(duplicateError));

      // Act & Assert
      await expectAsync(service.createDesignation(duplicateDesignationData))
        .toBeRejectedWith(duplicateError);
    });

    it('should create designation with all required fields', async () => {
      // Arrange
      const completeDesignationData = {
        title: 'Technical Lead',
        description: 'Technical leadership position with complete data',
        enabled: true,
        businessUnitId: 0,
        level: 'lead',
        department: 'Technology',
        reportingTo: 'Engineering Manager',
        responsibilities: ['Team leadership', 'Technical guidance', 'Code review'],
        requiredSkills: ['Leadership', 'Technical expertise', 'Communication'],
        experienceRequired: '5+ years',
        createdBy: 1
      };
      const mockResponse = {
        responseCode: 200,
        message: 'Designation created successfully',
        data: { id: 5, ...completeDesignationData }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.createDesignation(completeDesignationData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), completeDesignationData);
      expect(result).toEqual(mockResponse);
    });

    it('should handle network errors when creating designation', async () => {
      // Arrange
      const designationData = {
        title: 'Network Test Designation',
        description: 'Test designation for network error',
        enabled: true
      };
      const networkError = new Error('Network connection failed');
      apiService.postData.and.returnValue(Promise.reject(networkError));

      // Act & Assert
      await expectAsync(service.createDesignation(designationData)).toBeRejectedWith(networkError);
    });

    it('should create designation with different levels', async () => {
      // Arrange
      const juniorDesignationData = {
        title: 'Junior Analyst',
        description: 'Entry level analyst position',
        enabled: true,
        businessUnitId: 0,
        level: 'junior',
        department: 'Analytics',
        createdBy: 1
      };
      const mockResponse = {
        responseCode: 200,
        message: 'Junior designation created successfully',
        data: { id: 6, ...juniorDesignationData }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.createDesignation(juniorDesignationData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), juniorDesignationData);
      expect(result).toEqual(mockResponse);
      expect(result.data.level).toBe('junior');
    });

    it('should create disabled designation', async () => {
      // Arrange
      const disabledDesignationData = {
        title: 'Deprecated Role',
        description: 'Role that is no longer active',
        enabled: false,
        businessUnitId: 0,
        level: 'deprecated',
        department: 'Legacy',
        createdBy: 1
      };
      const mockResponse = {
        responseCode: 200,
        message: 'Disabled designation created successfully',
        data: { id: 7, ...disabledDesignationData }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.createDesignation(disabledDesignationData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), disabledDesignationData);
      expect(result).toEqual(mockResponse);
      expect(result.data.enabled).toBe(false);
    });

    it('should handle null or undefined data', async () => {
      // Arrange
      const nullData = null;
      const mockResponse = {
        responseCode: 400,
        message: 'Invalid data provided'
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.createDesignation(nullData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), nullData);
      expect(result).toEqual(mockResponse);
    });

    it('should create designation with hierarchy information', async () => {
      // Arrange
      const hierarchyDesignationData = {
        title: 'Department Head',
        description: 'Head of department with reporting structure',
        enabled: true,
        businessUnitId: 0,
        level: 'head',
        department: 'Operations',
        hierarchy: {
          reportsTo: 'General Manager',
          manages: ['Team Lead', 'Senior Engineer', 'Engineer'],
          hierarchyLevel: 3
        },
        permissions: ['approve_budget', 'hire_staff', 'performance_review'],
        createdBy: 1
      };
      const mockResponse = {
        responseCode: 200,
        message: 'Hierarchy designation created successfully',
        data: { id: 8, ...hierarchyDesignationData }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.createDesignation(hierarchyDesignationData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), hierarchyDesignationData);
      expect(result).toEqual(mockResponse);
    });
  });
});
