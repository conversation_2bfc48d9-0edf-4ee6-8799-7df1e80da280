import { TestBed } from '@angular/core/testing';
import { EquipmentService } from './equipment.service';
import { ApiService } from '../../api.service';

describe('EquipmentService', () => {
  let service: EquipmentService;
  let apiService: jasmine.SpyObj<ApiService>;

  const mockEquipmentResponse = {
    data: [
      {
        id: 1,
        name: 'Conveyor Belt A1',
        code: 'CB-A1-001',
        type: 'Conveyor',
        category: 'Material Handling',
        manufacturer: 'ABC Industries',
        model: 'CB-2000',
        serialNumber: 'SN123456789',
        installationDate: '2023-01-15T00:00:00Z',
        status: 'Active',
        location: 'Plant A - Section 1',
        plantId: 1,
        enabled: true,
        businessUnitId: 0,
        specifications: {
          capacity: '1000 tons/hour',
          power: '50 kW',
          dimensions: '100m x 2m x 1.5m'
        },
        maintenanceSchedule: 'Monthly',
        lastMaintenanceDate: '2024-01-01T00:00:00Z',
        nextMaintenanceDate: '2024-02-01T00:00:00Z',
        createdAt: '2023-01-01T00:00:00Z'
      },
      {
        id: 2,
        name: 'Crusher Unit B2',
        code: 'CU-B2-002',
        type: 'Crusher',
        category: 'Processing',
        manufacturer: 'XYZ Corp',
        model: 'CU-5000',
        serialNumber: 'SN987654321',
        installationDate: '2023-02-20T00:00:00Z',
        status: 'Maintenance',
        location: 'Plant B - Section 2',
        plantId: 2,
        enabled: true,
        businessUnitId: 0,
        specifications: {
          capacity: '500 tons/hour',
          power: '200 kW',
          dimensions: '10m x 5m x 8m'
        },
        maintenanceSchedule: 'Weekly',
        lastMaintenanceDate: '2024-01-08T00:00:00Z',
        nextMaintenanceDate: '2024-01-15T00:00:00Z',
        createdAt: '2023-02-01T00:00:00Z'
      }
    ],
    total: 2,
    page: 1,
    limit: 10
  };

  beforeEach(() => {
    const apiSpy = jasmine.createSpyObj('ApiService', ['getData', 'postData']);

    TestBed.configureTestingModule({
      providers: [
        EquipmentService,
        { provide: ApiService, useValue: apiSpy }
      ]
    });

    service = TestBed.inject(EquipmentService);
    apiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
  });

  beforeEach(() => {
    spyOn(console, 'log');
    spyOn(console, 'error');
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getEquipment', () => {
    it('should fetch equipment successfully', async () => {
      // Arrange
      const params = { page: 1, limit: 10, enabled: true };
      apiService.getData.and.returnValue(Promise.resolve(mockEquipmentResponse));

      // Act
      const result = await service.getEquipment(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(mockEquipmentResponse);
    });

    it('should fetch equipment with filters', async () => {
      // Arrange
      const params = {
        page: 1,
        limit: 10,
        filter: ['enabled||eq||true', 'status||eq||Active', 'type||eq||Conveyor'],
        sort: 'name,ASC'
      };
      apiService.getData.and.returnValue(Promise.resolve(mockEquipmentResponse));

      // Act
      const result = await service.getEquipment(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(mockEquipmentResponse);
    });

    it('should handle empty equipment response', async () => {
      // Arrange
      const params = { page: 1, limit: 10 };
      const emptyResponse = { data: [], total: 0, page: 1, limit: 10 };
      apiService.getData.and.returnValue(Promise.resolve(emptyResponse));

      // Act
      const result = await service.getEquipment(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(emptyResponse);
    });

    it('should handle errors when fetching equipment', async () => {
      // Arrange
      const params = { page: 1, limit: 10 };
      const error = new Error('Network Error');
      apiService.getData.and.returnValue(Promise.reject(error));

      // Act & Assert
      await expectAsync(service.getEquipment(params)).toBeRejectedWith(error);
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
    });

    it('should fetch equipment by status', async () => {
      // Arrange
      const params = {
        page: 1,
        limit: 10,
        filter: ['status||eq||Active']
      };
      const activeEquipmentResponse = {
        data: [mockEquipmentResponse.data[0]], // Only active equipment
        total: 1,
        page: 1,
        limit: 10
      };
      apiService.getData.and.returnValue(Promise.resolve(activeEquipmentResponse));

      // Act
      const result = await service.getEquipment(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(activeEquipmentResponse);
      expect(result.data.length).toBe(1);
      expect(result.data[0].status).toBe('Active');
    });

    it('should fetch equipment by type', async () => {
      // Arrange
      const params = {
        page: 1,
        limit: 10,
        filter: ['type||eq||Conveyor']
      };
      const conveyorEquipmentResponse = {
        data: [mockEquipmentResponse.data[0]], // Only conveyor equipment
        total: 1,
        page: 1,
        limit: 10
      };
      apiService.getData.and.returnValue(Promise.resolve(conveyorEquipmentResponse));

      // Act
      const result = await service.getEquipment(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(conveyorEquipmentResponse);
      expect(result.data.length).toBe(1);
      expect(result.data[0].type).toBe('Conveyor');
    });

    it('should fetch equipment by plant', async () => {
      // Arrange
      const params = {
        page: 1,
        limit: 10,
        filter: ['plantId||eq||1']
      };
      const plantEquipmentResponse = {
        data: [mockEquipmentResponse.data[0]], // Only plant 1 equipment
        total: 1,
        page: 1,
        limit: 10
      };
      apiService.getData.and.returnValue(Promise.resolve(plantEquipmentResponse));

      // Act
      const result = await service.getEquipment(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(plantEquipmentResponse);
      expect(result.data.length).toBe(1);
      expect(result.data[0].plantId).toBe(1);
    });

    it('should search equipment by name', async () => {
      // Arrange
      const params = {
        page: 1,
        limit: 10,
        filter: ['name||like||Conveyor']
      };
      const searchResponse = {
        data: [mockEquipmentResponse.data[0]], // Only equipment with 'Conveyor' in name
        total: 1,
        page: 1,
        limit: 10
      };
      apiService.getData.and.returnValue(Promise.resolve(searchResponse));

      // Act
      const result = await service.getEquipment(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(searchResponse);
      expect(result.data.length).toBe(1);
      expect(result.data[0].name).toContain('Conveyor');
    });
  });

  describe('createEquipment', () => {
    it('should create equipment successfully', async () => {
      // Arrange
      const newEquipmentData = {
        name: 'New Pump Unit',
        code: 'PU-C3-003',
        type: 'Pump',
        category: 'Fluid Handling',
        manufacturer: 'PumpTech Ltd',
        model: 'PT-3000',
        serialNumber: 'SN456789123',
        installationDate: '2024-01-15T00:00:00Z',
        status: 'Active',
        location: 'Plant C - Section 3',
        plantId: 3,
        enabled: true,
        businessUnitId: 0,
        specifications: {
          capacity: '2000 L/min',
          power: '75 kW',
          pressure: '10 bar'
        },
        maintenanceSchedule: 'Quarterly',
        createdBy: 1
      };
      const mockCreateResponse = {
        responseCode: 200,
        message: 'Equipment created successfully',
        data: {
          id: 3,
          ...newEquipmentData,
          createdAt: '2024-01-15T00:00:00Z'
        }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockCreateResponse));

      // Act
      const result = await service.createEquipment(newEquipmentData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), newEquipmentData);
      expect(result).toEqual(mockCreateResponse);
    });

    it('should handle validation errors when creating equipment', async () => {
      // Arrange
      const invalidEquipmentData = {
        name: '', // Empty name should cause validation error
        code: '',
        type: '',
        enabled: true
      };
      const validationError = {
        responseCode: 400,
        message: 'Validation failed',
        errors: ['Name is required', 'Code is required', 'Type is required']
      };
      apiService.postData.and.returnValue(Promise.reject(validationError));

      // Act & Assert
      await expectAsync(service.createEquipment(invalidEquipmentData))
        .toBeRejectedWith(validationError);
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), invalidEquipmentData);
    });

    it('should handle duplicate equipment creation', async () => {
      // Arrange
      const duplicateEquipmentData = {
        name: 'Conveyor Belt A1', // Assuming this already exists
        code: 'CB-A1-001',
        type: 'Conveyor',
        enabled: true,
        businessUnitId: 0
      };
      const duplicateError = {
        responseCode: 409,
        message: 'Equipment with this code already exists'
      };
      apiService.postData.and.returnValue(Promise.reject(duplicateError));

      // Act & Assert
      await expectAsync(service.createEquipment(duplicateEquipmentData))
        .toBeRejectedWith(duplicateError);
    });

    it('should create equipment with complete specifications', async () => {
      // Arrange
      const completeEquipmentData = {
        name: 'Advanced Turbine Generator',
        code: 'TG-D4-004',
        type: 'Generator',
        category: 'Power Generation',
        manufacturer: 'PowerGen Industries',
        model: 'PG-5000',
        serialNumber: 'SN789123456',
        installationDate: '2024-02-01T00:00:00Z',
        status: 'Active',
        location: 'Plant D - Power House',
        plantId: 4,
        enabled: true,
        businessUnitId: 0,
        specifications: {
          capacity: '5000 kW',
          voltage: '11 kV',
          frequency: '50 Hz',
          efficiency: '95%',
          fuelType: 'Natural Gas'
        },
        maintenanceSchedule: 'Monthly',
        warrantyExpiry: '2027-02-01T00:00:00Z',
        purchaseDate: '2024-01-01T00:00:00Z',
        purchaseCost: 5000000,
        supplier: 'PowerGen Industries',
        operatingParameters: {
          maxTemperature: '150°C',
          maxPressure: '20 bar',
          operatingHours: '8760 hours/year'
        },
        safetyFeatures: ['Emergency Stop', 'Overheat Protection', 'Vibration Monitoring'],
        createdBy: 1
      };
      const mockResponse = {
        responseCode: 200,
        message: 'Advanced equipment created successfully',
        data: { id: 4, ...completeEquipmentData }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.createEquipment(completeEquipmentData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), completeEquipmentData);
      expect(result).toEqual(mockResponse);
    });

    it('should handle network errors when creating equipment', async () => {
      // Arrange
      const equipmentData = {
        name: 'Network Test Equipment',
        code: 'NTE-001',
        type: 'Test',
        enabled: true
      };
      const networkError = new Error('Network connection failed');
      apiService.postData.and.returnValue(Promise.reject(networkError));

      // Act & Assert
      await expectAsync(service.createEquipment(equipmentData)).toBeRejectedWith(networkError);
    });

    it('should create equipment with maintenance schedule', async () => {
      // Arrange
      const maintenanceEquipmentData = {
        name: 'Scheduled Maintenance Equipment',
        code: 'SME-005',
        type: 'Maintenance',
        category: 'Support',
        manufacturer: 'MaintenanceCorp',
        model: 'MC-1000',
        serialNumber: 'SN321654987',
        installationDate: '2024-01-20T00:00:00Z',
        status: 'Active',
        location: 'Maintenance Bay',
        plantId: 1,
        enabled: true,
        businessUnitId: 0,
        maintenanceSchedule: 'Daily',
        maintenanceType: 'Preventive',
        maintenanceDuration: '2 hours',
        maintenanceTeam: 'Team A',
        lastMaintenanceDate: '2024-01-19T00:00:00Z',
        nextMaintenanceDate: '2024-01-20T00:00:00Z',
        maintenanceHistory: [],
        createdBy: 1
      };
      const mockResponse = {
        responseCode: 200,
        message: 'Equipment with maintenance schedule created successfully',
        data: { id: 5, ...maintenanceEquipmentData }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.createEquipment(maintenanceEquipmentData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), maintenanceEquipmentData);
      expect(result).toEqual(mockResponse);
      expect(result.data.maintenanceSchedule).toBe('Daily');
    });

    it('should create disabled equipment', async () => {
      // Arrange
      const disabledEquipmentData = {
        name: 'Decommissioned Equipment',
        code: 'DE-006',
        type: 'Decommissioned',
        category: 'Legacy',
        status: 'Inactive',
        enabled: false,
        businessUnitId: 0,
        decommissionDate: '2024-01-15T00:00:00Z',
        decommissionReason: 'End of life',
        createdBy: 1
      };
      const mockResponse = {
        responseCode: 200,
        message: 'Disabled equipment created successfully',
        data: { id: 6, ...disabledEquipmentData }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.createEquipment(disabledEquipmentData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), disabledEquipmentData);
      expect(result).toEqual(mockResponse);
      expect(result.data.enabled).toBe(false);
    });

    it('should handle null or undefined data', async () => {
      // Arrange
      const nullData = null;
      const mockResponse = {
        responseCode: 400,
        message: 'Invalid data provided'
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.createEquipment(nullData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), nullData);
      expect(result).toEqual(mockResponse);
    });
  });
});
