import { TestBed } from '@angular/core/testing';
import { QrTypeService } from './qr-type.service';
import { ApiService } from '../../api.service';

describe('QrTypeService', () => {
  let service: QrTypeService;
  let apiService: jasmine.SpyObj<ApiService>;

  beforeEach(() => {
    const apiSpy = jasmine.createSpyObj('ApiService', ['getData', 'postData', 'putData', 'deleteData']);

    TestBed.configureTestingModule({
      providers: [
        QrTypeService,
        { provide: ApiService, useValue: apiSpy }
      ]
    });

    service = TestBed.inject(QrTypeService);
    apiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
  });

  beforeEach(() => {
    spyOn(console, 'log');
    spyOn(console, 'error');
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getQrType', () => {
    it('should fetch QR types successfully', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const mockResponse = {
        responseCode: 200,
        data: [
          { id: 1, title: 'Permanent QR', enabled: true, description: 'Permanent QR code for fixed locations' },
          { id: 2, title: 'Temporary QR', enabled: true, description: 'Temporary QR code for mobile inspections' }
        ],
        total: 2
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getQrType(mockParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), mockParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle API errors when fetching QR types', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const mockError = new Error('Network Error');
      apiService.getData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.getQrType(mockParams)).toBeRejectedWith(mockError);
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), mockParams);
    });

    it('should handle filtering by enabled status', async () => {
      // Arrange
      const filterParams = {
        page: 1,
        limit: 10,
        filter: ['enabled||eq||true']
      };
      const mockResponse = {
        responseCode: 200,
        data: [{ id: 1, title: 'Permanent QR', enabled: true, description: 'Permanent QR code for fixed locations' }],
        total: 1
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getQrType(filterParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), filterParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle search by title', async () => {
      // Arrange
      const searchParams = {
        page: 1,
        limit: 10,
        filter: ['title||like||%temporary%']
      };
      const mockResponse = {
        responseCode: 200,
        data: [{ id: 2, title: 'Temporary QR', enabled: true, description: 'Temporary QR code for mobile inspections' }],
        total: 1
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getQrType(searchParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), searchParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle sorting parameters', async () => {
      // Arrange
      const sortParams = {
        page: 1,
        limit: 10,
        sort: 'title,ASC'
      };
      const mockResponse = {
        responseCode: 200,
        data: [
          { id: 1, title: 'Permanent QR', enabled: true, description: 'Permanent QR code for fixed locations' },
          { id: 2, title: 'Temporary QR', enabled: true, description: 'Temporary QR code for mobile inspections' }
        ],
        total: 2
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getQrType(sortParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), sortParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle empty response data', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const emptyResponse = {
        responseCode: 200,
        data: [],
        total: 0
      };
      apiService.getData.and.returnValue(Promise.resolve(emptyResponse));

      // Act
      const result = await service.getQrType(mockParams);

      // Assert
      expect(result.data.length).toBe(0);
      expect(result.total).toBe(0);
    });
  });

  describe('createQrType', () => {
    it('should create QR type successfully', async () => {
      // Arrange
      const newQrTypeData = {
        title: 'Emergency QR',
        description: 'Emergency response QR code',
        enabled: true
      };
      const mockResponse = {
        responseCode: 200,
        message: 'QR type created successfully',
        data: { id: 3, ...newQrTypeData }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.createQrType(newQrTypeData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), newQrTypeData);
      expect(result).toEqual(mockResponse);
    });

    it('should handle validation errors when creating QR type', async () => {
      // Arrange
      const invalidQrTypeData = { title: '' }; // Invalid data
      const mockError = new Error('Validation Error');
      apiService.postData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.createQrType(invalidQrTypeData)).toBeRejectedWith(mockError);
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), invalidQrTypeData);
    });

    it('should handle duplicate QR type creation', async () => {
      // Arrange
      const duplicateData = {
        title: 'Permanent QR', // Already exists
        enabled: true
      };
      const mockError = new Error('QR type already exists');
      apiService.postData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.createQrType(duplicateData)).toBeRejectedWith(mockError);
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), duplicateData);
    });

    it('should create QR type with different types', async () => {
      // Arrange
      const qrTypes = ['Permanent QR', 'Temporary QR', 'Emergency QR', 'Maintenance QR', 'Inspection QR'];

      for (const type of qrTypes) {
        const qrTypeData = {
          title: type,
          description: `${type} code type`,
          enabled: true
        };
        const mockResponse = {
          responseCode: 200,
          message: 'QR type created successfully',
          data: { id: Math.random(), ...qrTypeData }
        };
        apiService.postData.and.returnValue(Promise.resolve(mockResponse));

        // Act
        const result = await service.createQrType(qrTypeData);

        // Assert
        expect(result.data.title).toBe(type);
      }
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle network timeout errors', async () => {
      // Arrange
      const timeoutError = new Error('Request timeout');
      timeoutError.name = 'TimeoutError';
      apiService.getData.and.returnValue(Promise.reject(timeoutError));

      // Act & Assert
      await expectAsync(service.getQrType({})).toBeRejectedWith(timeoutError);
    });

    it('should handle server errors (500)', async () => {
      // Arrange
      const serverError = new Error('Internal Server Error');
      serverError.name = 'ServerError';
      apiService.postData.and.returnValue(Promise.reject(serverError));

      // Act & Assert
      await expectAsync(service.createQrType({})).toBeRejectedWith(serverError);
    });

    it('should handle unauthorized access errors', async () => {
      // Arrange
      const unauthorizedError = new Error('Unauthorized');
      unauthorizedError.name = 'UnauthorizedError';
      apiService.getData.and.returnValue(Promise.reject(unauthorizedError));

      // Act & Assert
      await expectAsync(service.getQrType({})).toBeRejectedWith(unauthorizedError);
    });

    it('should handle malformed response data', async () => {
      // Arrange
      const malformedResponse = {
        responseCode: 200,
        data: null, // Malformed data
        total: 0
      };
      apiService.getData.and.returnValue(Promise.resolve(malformedResponse));

      // Act
      const result = await service.getQrType({});

      // Assert
      expect(result.data).toBeNull();
      expect(result.total).toBe(0);
    });

    it('should handle QR code generation edge cases', async () => {
      // Arrange
      const specialQrTypeData = {
        title: 'Special-QR_Type@123',
        description: 'QR type with special characters',
        enabled: true
      };
      const mockResponse = {
        responseCode: 200,
        message: 'QR type created successfully',
        data: { id: 99, ...specialQrTypeData }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.createQrType(specialQrTypeData);

      // Assert
      expect(result.data.title).toBe('Special-QR_Type@123');
    });
  });
});
