import { TestBed } from '@angular/core/testing';
import { RootCauseService } from './root-cause.service';
import { ApiService } from '../../api.service';

describe('RootCauseService', () => {
  let service: RootCauseService;
  let apiService: jasmine.SpyObj<ApiService>;

  beforeEach(() => {
    const apiSpy = jasmine.createSpyObj('ApiService', ['getData', 'postData']);

    TestBed.configureTestingModule({
      providers: [
        RootCauseService,
        { provide: ApiService, useValue: apiSpy }
      ]
    });

    service = TestBed.inject(RootCauseService);
    apiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
  });

  beforeEach(() => {
    spyOn(console, 'log');
    spyOn(console, 'error');
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getRootCause', () => {
    it('should fetch root causes successfully', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const mockResponse = {
        responseCode: 200,
        data: [
          { id: 1, title: 'Human Error', enabled: true, category: 'Personnel' },
          { id: 2, title: 'Equipment Failure', enabled: true, category: 'Technical' }
        ],
        total: 2
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getRootCause(mockParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), mockParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle API errors when fetching root causes', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const mockError = new Error('Network Error');
      apiService.getData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.getRootCause(mockParams)).toBeRejectedWith(mockError);
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), mockParams);
    });

    it('should handle filtering by category', async () => {
      // Arrange
      const filterParams = {
        page: 1,
        limit: 10,
        filter: ['category||eq||Technical', 'enabled||eq||true']
      };
      const mockResponse = {
        responseCode: 200,
        data: [{ id: 2, title: 'Equipment Failure', enabled: true, category: 'Technical' }],
        total: 1
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getRootCause(filterParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), filterParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle search by title', async () => {
      // Arrange
      const searchParams = {
        page: 1,
        limit: 10,
        filter: ['title||like||%error%']
      };
      const mockResponse = {
        responseCode: 200,
        data: [{ id: 1, title: 'Human Error', enabled: true, category: 'Personnel' }],
        total: 1
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getRootCause(searchParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), searchParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle sorting parameters', async () => {
      // Arrange
      const sortParams = {
        page: 1,
        limit: 10,
        sort: 'title,ASC'
      };
      const mockResponse = {
        responseCode: 200,
        data: [
          { id: 2, title: 'Equipment Failure', enabled: true, category: 'Technical' },
          { id: 1, title: 'Human Error', enabled: true, category: 'Personnel' }
        ],
        total: 2
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getRootCause(sortParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), sortParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle empty response data', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const emptyResponse = {
        responseCode: 200,
        data: [],
        total: 0
      };
      apiService.getData.and.returnValue(Promise.resolve(emptyResponse));

      // Act
      const result = await service.getRootCause(mockParams);

      // Assert
      expect(result.data.length).toBe(0);
      expect(result.total).toBe(0);
    });
  });

  describe('createRootCause', () => {
    it('should create root cause successfully', async () => {
      // Arrange
      const newRootCauseData = {
        title: 'Process Deviation',
        description: 'Deviation from standard operating procedure',
        category: 'Process',
        enabled: true
      };
      const mockResponse = {
        responseCode: 200,
        message: 'Root cause created successfully',
        data: { id: 3, ...newRootCauseData }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.createRootCause(newRootCauseData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), newRootCauseData);
      expect(result).toEqual(mockResponse);
    });

    it('should handle validation errors when creating root cause', async () => {
      // Arrange
      const invalidRootCauseData = { title: '', category: '' }; // Invalid data
      const mockError = new Error('Validation Error');
      apiService.postData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.createRootCause(invalidRootCauseData)).toBeRejectedWith(mockError);
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), invalidRootCauseData);
    });

    it('should create root cause with different categories', async () => {
      // Arrange
      const categories = ['Personnel', 'Technical', 'Process', 'Environmental'];

      for (const category of categories) {
        const rootCauseData = {
          title: `${category} Issue`,
          description: `${category} related root cause`,
          category: category,
          enabled: true
        };
        const mockResponse = {
          responseCode: 200,
          message: 'Root cause created successfully',
          data: { id: Math.random(), ...rootCauseData }
        };
        apiService.postData.and.returnValue(Promise.resolve(mockResponse));

        // Act
        const result = await service.createRootCause(rootCauseData);

        // Assert
        expect(result.data.category).toBe(category);
      }
    });

    it('should handle duplicate root cause creation', async () => {
      // Arrange
      const duplicateData = {
        title: 'Human Error', // Already exists
        category: 'Personnel',
        enabled: true
      };
      const mockError = new Error('Root cause already exists');
      apiService.postData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.createRootCause(duplicateData)).toBeRejectedWith(mockError);
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), duplicateData);
    });
  });
});
