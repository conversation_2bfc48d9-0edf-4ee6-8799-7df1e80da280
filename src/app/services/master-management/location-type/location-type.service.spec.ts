import { TestBed } from '@angular/core/testing';
import { LocationTypeService } from './location-type.service';
import { ApiService } from '../../api.service';

describe('LocationTypeService', () => {
  let service: LocationTypeService;
  let apiService: jasmine.SpyObj<ApiService>;

  beforeEach(() => {
    const apiSpy = jasmine.createSpyObj('ApiService', ['getData', 'postData', 'putData', 'deleteData']);

    TestBed.configureTestingModule({
      providers: [
        LocationTypeService,
        { provide: ApiService, useValue: apiSpy }
      ]
    });

    service = TestBed.inject(LocationTypeService);
    apiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
  });

  beforeEach(() => {
    spyOn(console, 'log');
    spyOn(console, 'error');
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getLocationType', () => {
    it('should fetch location types successfully', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const mockResponse = {
        responseCode: 200,
        data: [
          { id: 1, title: 'Office', enabled: true, description: 'Office location' },
          { id: 2, title: 'Warehouse', enabled: true, description: 'Storage facility' }
        ],
        total: 2
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getLocationType(mockParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), mockParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle API errors when fetching location types', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const mockError = new Error('Network Error');
      apiService.getData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.getLocationType(mockParams)).toBeRejectedWith(mockError);
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), mockParams);
    });

    it('should handle filtering by enabled status', async () => {
      // Arrange
      const filterParams = {
        page: 1,
        limit: 10,
        filter: ['enabled||eq||true']
      };
      const mockResponse = {
        responseCode: 200,
        data: [{ id: 1, title: 'Office', enabled: true, description: 'Office location' }],
        total: 1
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getLocationType(filterParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), filterParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle empty response data', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const emptyResponse = {
        responseCode: 200,
        data: [],
        total: 0
      };
      apiService.getData.and.returnValue(Promise.resolve(emptyResponse));

      // Act
      const result = await service.getLocationType(mockParams);

      // Assert
      expect(result.data.length).toBe(0);
      expect(result.total).toBe(0);
    });
  });

  describe('createLocationType', () => {
    it('should create location type successfully', async () => {
      // Arrange
      const newLocationTypeData = {
        title: 'Factory',
        description: 'Manufacturing facility',
        enabled: true
      };
      const mockResponse = {
        responseCode: 200,
        message: 'Location type created successfully',
        data: { id: 3, ...newLocationTypeData }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.createLocationType(newLocationTypeData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), newLocationTypeData);
      expect(result).toEqual(mockResponse);
    });

    it('should handle validation errors when creating location type', async () => {
      // Arrange
      const invalidLocationTypeData = { title: '' }; // Invalid data
      const mockError = new Error('Validation Error');
      apiService.postData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.createLocationType(invalidLocationTypeData)).toBeRejectedWith(mockError);
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), invalidLocationTypeData);
    });
  });

  describe('Error Handling', () => {
    it('should handle network timeout errors', async () => {
      // Arrange
      const timeoutError = new Error('Request timeout');
      timeoutError.name = 'TimeoutError';
      apiService.getData.and.returnValue(Promise.reject(timeoutError));

      // Act & Assert
      await expectAsync(service.getLocationType({})).toBeRejectedWith(timeoutError);
    });

    it('should handle unauthorized access errors', async () => {
      // Arrange
      const unauthorizedError = new Error('Unauthorized');
      unauthorizedError.name = 'UnauthorizedError';
      apiService.getData.and.returnValue(Promise.reject(unauthorizedError));

      // Act & Assert
      await expectAsync(service.getLocationType({})).toBeRejectedWith(unauthorizedError);
    });
  });
});
