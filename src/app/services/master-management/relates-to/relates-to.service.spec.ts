import { TestBed } from '@angular/core/testing';
import { RelatesToService } from './relates-to.service';
import { ApiService } from '../../api.service';

describe('RelatesToService', () => {
  let service: RelatesToService;
  let apiService: jasmine.SpyObj<ApiService>;

  beforeEach(() => {
    const apiSpy = jasmine.createSpyObj('ApiService', ['getData', 'postData', 'putData', 'deleteData']);

    TestBed.configureTestingModule({
      providers: [
        RelatesToService,
        { provide: ApiService, useValue: apiSpy }
      ]
    });

    service = TestBed.inject(RelatesToService);
    apiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
  });

  beforeEach(() => {
    spyOn(console, 'log');
    spyOn(console, 'error');
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getRelatesTo', () => {
    it('should fetch relates to items successfully', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const mockResponse = {
        responseCode: 200,
        data: [
          { id: 1, title: 'Equipment', enabled: true, category: 'Asset', description: 'Related to equipment and machinery' },
          { id: 2, title: 'Personnel', enabled: true, category: 'Human', description: 'Related to personnel and staff' }
        ],
        total: 2
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getRelatesTo(mockParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), mockParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle API errors when fetching relates to items', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const mockError = new Error('Network Error');
      apiService.getData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.getRelatesTo(mockParams)).toBeRejectedWith(mockError);
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), mockParams);
    });

    it('should handle filtering by category', async () => {
      // Arrange
      const filterParams = {
        page: 1,
        limit: 10,
        filter: ['category||eq||Asset', 'enabled||eq||true']
      };
      const mockResponse = {
        responseCode: 200,
        data: [{ id: 1, title: 'Equipment', enabled: true, category: 'Asset', description: 'Related to equipment and machinery' }],
        total: 1
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getRelatesTo(filterParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), filterParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle search by title', async () => {
      // Arrange
      const searchParams = {
        page: 1,
        limit: 10,
        filter: ['title||like||%personnel%']
      };
      const mockResponse = {
        responseCode: 200,
        data: [{ id: 2, title: 'Personnel', enabled: true, category: 'Human', description: 'Related to personnel and staff' }],
        total: 1
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getRelatesTo(searchParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), searchParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle sorting parameters', async () => {
      // Arrange
      const sortParams = {
        page: 1,
        limit: 10,
        sort: 'title,ASC'
      };
      const mockResponse = {
        responseCode: 200,
        data: [
          { id: 1, title: 'Equipment', enabled: true, category: 'Asset', description: 'Related to equipment and machinery' },
          { id: 2, title: 'Personnel', enabled: true, category: 'Human', description: 'Related to personnel and staff' }
        ],
        total: 2
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getRelatesTo(sortParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), sortParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle empty response data', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const emptyResponse = {
        responseCode: 200,
        data: [],
        total: 0
      };
      apiService.getData.and.returnValue(Promise.resolve(emptyResponse));

      // Act
      const result = await service.getRelatesTo(mockParams);

      // Assert
      expect(result.data.length).toBe(0);
      expect(result.total).toBe(0);
    });
  });

  describe('createRelatesTo', () => {
    it('should create relates to item successfully', async () => {
      // Arrange
      const newRelatesToData = {
        title: 'Environment',
        description: 'Related to environmental factors',
        category: 'Environmental',
        enabled: true
      };
      const mockResponse = {
        responseCode: 200,
        message: 'Relates to item created successfully',
        data: { id: 3, ...newRelatesToData }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.createRelatesTo(newRelatesToData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), newRelatesToData);
      expect(result).toEqual(mockResponse);
    });

    it('should handle validation errors when creating relates to item', async () => {
      // Arrange
      const invalidRelatesToData = { title: '', category: '' }; // Invalid data
      const mockError = new Error('Validation Error');
      apiService.postData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.createRelatesTo(invalidRelatesToData)).toBeRejectedWith(mockError);
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), invalidRelatesToData);
    });

    it('should handle duplicate relates to item creation', async () => {
      // Arrange
      const duplicateData = {
        title: 'Equipment', // Already exists
        category: 'Asset',
        enabled: true
      };
      const mockError = new Error('Relates to item already exists');
      apiService.postData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.createRelatesTo(duplicateData)).toBeRejectedWith(mockError);
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), duplicateData);
    });

    it('should create relates to item with different categories', async () => {
      // Arrange
      const categories = ['Asset', 'Human', 'Environmental', 'Process', 'System'];

      for (const category of categories) {
        const relatesToData = {
          title: `${category} Related`,
          description: `Related to ${category.toLowerCase()} factors`,
          category: category,
          enabled: true
        };
        const mockResponse = {
          responseCode: 200,
          message: 'Relates to item created successfully',
          data: { id: Math.random(), ...relatesToData }
        };
        apiService.postData.and.returnValue(Promise.resolve(mockResponse));

        // Act
        const result = await service.createRelatesTo(relatesToData);

        // Assert
        expect(result.data.category).toBe(category);
      }
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle network timeout errors', async () => {
      // Arrange
      const timeoutError = new Error('Request timeout');
      timeoutError.name = 'TimeoutError';
      apiService.getData.and.returnValue(Promise.reject(timeoutError));

      // Act & Assert
      await expectAsync(service.getRelatesTo({})).toBeRejectedWith(timeoutError);
    });

    it('should handle server errors (500)', async () => {
      // Arrange
      const serverError = new Error('Internal Server Error');
      serverError.name = 'ServerError';
      apiService.postData.and.returnValue(Promise.reject(serverError));

      // Act & Assert
      await expectAsync(service.createRelatesTo({})).toBeRejectedWith(serverError);
    });

    it('should handle unauthorized access errors', async () => {
      // Arrange
      const unauthorizedError = new Error('Unauthorized');
      unauthorizedError.name = 'UnauthorizedError';
      apiService.getData.and.returnValue(Promise.reject(unauthorizedError));

      // Act & Assert
      await expectAsync(service.getRelatesTo({})).toBeRejectedWith(unauthorizedError);
    });

    it('should handle malformed response data', async () => {
      // Arrange
      const malformedResponse = {
        responseCode: 200,
        data: null, // Malformed data
        total: 0
      };
      apiService.getData.and.returnValue(Promise.resolve(malformedResponse));

      // Act
      const result = await service.getRelatesTo({});

      // Assert
      expect(result.data).toBeNull();
      expect(result.total).toBe(0);
    });

    it('should handle relationship mapping edge cases', async () => {
      // Arrange
      const complexRelatesToData = {
        title: 'Multi-Category Relationship',
        description: 'Complex relationship spanning multiple categories',
        category: 'Hybrid',
        enabled: true,
        metadata: {
          primaryCategory: 'Asset',
          secondaryCategories: ['Human', 'Environmental']
        }
      };
      const mockResponse = {
        responseCode: 200,
        message: 'Relates to item created successfully',
        data: { id: 99, ...complexRelatesToData }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.createRelatesTo(complexRelatesToData);

      // Assert
      expect(result.data.category).toBe('Hybrid');
      expect(result.data.metadata).toBeDefined();
    });
  });
});
