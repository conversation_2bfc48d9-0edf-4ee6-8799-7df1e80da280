import { TestBed } from '@angular/core/testing';
import { PlantTypeService } from './plant-type.service';
import { ApiService } from '../../api.service';

describe('PlantTypeService', () => {
  let service: PlantTypeService;
  let apiService: jasmine.SpyObj<ApiService>;

  beforeEach(() => {
    const apiSpy = jasmine.createSpyObj('ApiService', ['getData', 'postData', 'putData', 'deleteData']);

    TestBed.configureTestingModule({
      providers: [
        PlantTypeService,
        { provide: ApiService, useValue: apiSpy }
      ]
    });

    service = TestBed.inject(PlantTypeService);
    apiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
  });

  beforeEach(() => {
    spyOn(console, 'log');
    spyOn(console, 'error');
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getPlantType', () => {
    it('should fetch plant types successfully', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const mockResponse = {
        responseCode: 200,
        data: [
          { id: 1, title: 'Power Plant', enabled: true, description: 'Electricity generation facility' },
          { id: 2, title: 'Manufacturing Plant', enabled: true, description: 'Manufacturing and production facility' }
        ],
        total: 2
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getPlantType(mockParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), mockParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle API errors when fetching plant types', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const mockError = new Error('Network Error');
      apiService.getData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.getPlantType(mockParams)).toBeRejectedWith(mockError);
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), mockParams);
    });

    it('should handle filtering by enabled status', async () => {
      // Arrange
      const filterParams = {
        page: 1,
        limit: 10,
        filter: ['enabled||eq||true']
      };
      const mockResponse = {
        responseCode: 200,
        data: [{ id: 1, title: 'Power Plant', enabled: true, description: 'Electricity generation facility' }],
        total: 1
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getPlantType(filterParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), filterParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle search by title', async () => {
      // Arrange
      const searchParams = {
        page: 1,
        limit: 10,
        filter: ['title||like||%manufacturing%']
      };
      const mockResponse = {
        responseCode: 200,
        data: [{ id: 2, title: 'Manufacturing Plant', enabled: true, description: 'Manufacturing and production facility' }],
        total: 1
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getPlantType(searchParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), searchParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle sorting parameters', async () => {
      // Arrange
      const sortParams = {
        page: 1,
        limit: 10,
        sort: 'title,ASC'
      };
      const mockResponse = {
        responseCode: 200,
        data: [
          { id: 2, title: 'Manufacturing Plant', enabled: true, description: 'Manufacturing and production facility' },
          { id: 1, title: 'Power Plant', enabled: true, description: 'Electricity generation facility' }
        ],
        total: 2
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getPlantType(sortParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), sortParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle empty response data', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const emptyResponse = {
        responseCode: 200,
        data: [],
        total: 0
      };
      apiService.getData.and.returnValue(Promise.resolve(emptyResponse));

      // Act
      const result = await service.getPlantType(mockParams);

      // Assert
      expect(result.data.length).toBe(0);
      expect(result.total).toBe(0);
    });
  });

  describe('createPlantType', () => {
    it('should create plant type successfully', async () => {
      // Arrange
      const newPlantTypeData = {
        title: 'Chemical Plant',
        description: 'Chemical processing facility',
        enabled: true
      };
      const mockResponse = {
        responseCode: 200,
        message: 'Plant type created successfully',
        data: { id: 3, ...newPlantTypeData }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.createPlantType(newPlantTypeData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), newPlantTypeData);
      expect(result).toEqual(mockResponse);
    });

    it('should handle validation errors when creating plant type', async () => {
      // Arrange
      const invalidPlantTypeData = { title: '' }; // Invalid data
      const mockError = new Error('Validation Error');
      apiService.postData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.createPlantType(invalidPlantTypeData)).toBeRejectedWith(mockError);
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), invalidPlantTypeData);
    });

    it('should handle duplicate plant type creation', async () => {
      // Arrange
      const duplicateData = {
        title: 'Power Plant', // Already exists
        enabled: true
      };
      const mockError = new Error('Plant type already exists');
      apiService.postData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.createPlantType(duplicateData)).toBeRejectedWith(mockError);
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), duplicateData);
    });

    it('should create plant type with different types', async () => {
      // Arrange
      const plantTypes = ['Power Plant', 'Manufacturing Plant', 'Chemical Plant', 'Oil Refinery', 'Steel Mill'];

      for (const type of plantTypes) {
        const plantTypeData = {
          title: type,
          description: `${type} facility`,
          enabled: true
        };
        const mockResponse = {
          responseCode: 200,
          message: 'Plant type created successfully',
          data: { id: Math.random(), ...plantTypeData }
        };
        apiService.postData.and.returnValue(Promise.resolve(mockResponse));

        // Act
        const result = await service.createPlantType(plantTypeData);

        // Assert
        expect(result.data.title).toBe(type);
      }
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle network timeout errors', async () => {
      // Arrange
      const timeoutError = new Error('Request timeout');
      timeoutError.name = 'TimeoutError';
      apiService.getData.and.returnValue(Promise.reject(timeoutError));

      // Act & Assert
      await expectAsync(service.getPlantType({})).toBeRejectedWith(timeoutError);
    });

    it('should handle server errors (500)', async () => {
      // Arrange
      const serverError = new Error('Internal Server Error');
      serverError.name = 'ServerError';
      apiService.postData.and.returnValue(Promise.reject(serverError));

      // Act & Assert
      await expectAsync(service.createPlantType({})).toBeRejectedWith(serverError);
    });

    it('should handle unauthorized access errors', async () => {
      // Arrange
      const unauthorizedError = new Error('Unauthorized');
      unauthorizedError.name = 'UnauthorizedError';
      apiService.getData.and.returnValue(Promise.reject(unauthorizedError));

      // Act & Assert
      await expectAsync(service.getPlantType({})).toBeRejectedWith(unauthorizedError);
    });

    it('should handle malformed response data', async () => {
      // Arrange
      const malformedResponse = {
        responseCode: 200,
        data: null, // Malformed data
        total: 0
      };
      apiService.getData.and.returnValue(Promise.resolve(malformedResponse));

      // Act
      const result = await service.getPlantType({});

      // Assert
      expect(result.data).toBeNull();
      expect(result.total).toBe(0);
    });
  });
});
