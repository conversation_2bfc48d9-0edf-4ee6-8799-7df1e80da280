import { TestBed } from '@angular/core/testing';
import { DepartmentService } from './department.service';
import { ApiService } from '../../api.service';

describe('DepartmentService', () => {
  let service: DepartmentService;
  let apiService: jasmine.SpyObj<ApiService>;

  const mockDepartmentResponse = {
    data: [
      {
        id: 1,
        title: 'Engineering',
        enabled: true,
        businessUnitId: 0,
        createdAt: '2024-01-01T00:00:00Z'
      },
      {
        id: 2,
        title: 'Safety',
        enabled: true,
        businessUnitId: 0,
        createdAt: '2024-01-01T00:00:00Z'
      }
    ],
    total: 2,
    page: 1,
    limit: 10
  };

  beforeEach(() => {
    const apiSpy = jasmine.createSpyObj('ApiService', ['getData', 'postData']);

    TestBed.configureTestingModule({
      providers: [
        DepartmentService,
        { provide: ApiService, useValue: apiSpy }
      ]
    });

    service = TestBed.inject(DepartmentService);
    apiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
  });

  beforeEach(() => {
    spyOn(console, 'log');
    spyOn(console, 'error');
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getDepartments', () => {
    it('should fetch departments successfully', async () => {
      // Arrange
      const params = { page: 1, limit: 10, enabled: true };
      apiService.getData.and.returnValue(Promise.resolve(mockDepartmentResponse));

      // Act
      const result = await service.getDepartments(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(mockDepartmentResponse);
    });

    it('should handle errors when fetching departments', async () => {
      // Arrange
      const params = { page: 1, limit: 10 };
      const error = new Error('Network Error');
      apiService.getData.and.returnValue(Promise.reject(error));

      // Act & Assert
      await expectAsync(service.getDepartments(params)).toBeRejectedWith(error);
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
    });

    it('should fetch departments with filters', async () => {
      // Arrange
      const params = {
        page: 1,
        limit: 10,
        filter: ['enabled||eq||true', 'businessUnitId||eq||0'],
        sort: 'title,ASC'
      };
      apiService.getData.and.returnValue(Promise.resolve(mockDepartmentResponse));

      // Act
      const result = await service.getDepartments(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(mockDepartmentResponse);
    });

    it('should handle empty response', async () => {
      // Arrange
      const params = { page: 1, limit: 10 };
      const emptyResponse = { data: [], total: 0, page: 1, limit: 10 };
      apiService.getData.and.returnValue(Promise.resolve(emptyResponse));

      // Act
      const result = await service.getDepartments(params);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), params);
      expect(result).toEqual(emptyResponse);
    });
  });

  describe('createDepartment', () => {
    it('should create department successfully', async () => {
      // Arrange
      const newDepartmentData = {
        title: 'Human Resources',
        enabled: true,
        businessUnitId: 0
      };

      const mockCreateResponse = {
        responseCode: 200,
        message: 'Department created successfully',
        data: {
          id: 3,
          ...newDepartmentData,
          createdAt: '2024-01-01T00:00:00Z'
        }
      };

      apiService.postData.and.returnValue(Promise.resolve(mockCreateResponse));

      // Act
      const result = await service.createDepartment(newDepartmentData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), newDepartmentData);
      expect(result).toEqual(mockCreateResponse);
    });

    it('should handle validation errors when creating department', async () => {
      // Arrange
      const invalidDepartmentData = {
        title: '', // Empty title should cause validation error
        enabled: true
      };

      const validationError = {
        responseCode: 400,
        message: 'Title is required',
        errors: ['Title cannot be empty']
      };

      apiService.postData.and.returnValue(Promise.reject(validationError));

      // Act & Assert
      await expectAsync(service.createDepartment(invalidDepartmentData))
        .toBeRejectedWith(validationError);
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), invalidDepartmentData);
    });

    it('should handle duplicate department creation', async () => {
      // Arrange
      const duplicateDepartmentData = {
        title: 'Engineering', // Assuming this already exists
        enabled: true,
        businessUnitId: 0
      };

      const duplicateError = {
        responseCode: 409,
        message: 'Department with this title already exists'
      };

      apiService.postData.and.returnValue(Promise.reject(duplicateError));

      // Act & Assert
      await expectAsync(service.createDepartment(duplicateDepartmentData))
        .toBeRejectedWith(duplicateError);
    });

    it('should create department with all required fields', async () => {
      // Arrange
      const completeDepartmentData = {
        title: 'Quality Assurance',
        description: 'Quality control and assurance department',
        enabled: true,
        businessUnitId: 0,
        createdBy: 1
      };

      const mockResponse = {
        responseCode: 200,
        message: 'Department created successfully',
        data: { id: 4, ...completeDepartmentData }
      };

      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.createDepartment(completeDepartmentData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), completeDepartmentData);
      expect(result).toEqual(mockResponse);
    });
  });
});
