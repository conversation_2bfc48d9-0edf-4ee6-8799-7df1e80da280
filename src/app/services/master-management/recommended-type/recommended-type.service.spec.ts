import { TestBed } from '@angular/core/testing';
import { RecommendedTypeService } from './recommended-type.service';
import { ApiService } from '../../api.service';

describe('RecommendedTypeService', () => {
  let service: RecommendedTypeService;
  let apiService: jasmine.SpyObj<ApiService>;

  beforeEach(() => {
    const apiSpy = jasmine.createSpyObj('ApiService', ['getData', 'postData', 'putData', 'deleteData']);

    TestBed.configureTestingModule({
      providers: [
        RecommendedTypeService,
        { provide: ApiService, useValue: apiSpy }
      ]
    });

    service = TestBed.inject(RecommendedTypeService);
    apiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
  });

  beforeEach(() => {
    spyOn(console, 'log');
    spyOn(console, 'error');
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getRecommendedType', () => {
    it('should fetch recommended types successfully', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const mockResponse = {
        responseCode: 200,
        data: [
          { id: 1, title: 'Immediate Action', enabled: true, priority: 'High', description: 'Requires immediate attention' },
          { id: 2, title: 'Scheduled Maintenance', enabled: true, priority: 'Medium', description: 'Schedule for next maintenance cycle' }
        ],
        total: 2
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getRecommendedType(mockParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), mockParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle API errors when fetching recommended types', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const mockError = new Error('Network Error');
      apiService.getData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.getRecommendedType(mockParams)).toBeRejectedWith(mockError);
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), mockParams);
    });

    it('should handle filtering by priority', async () => {
      // Arrange
      const filterParams = {
        page: 1,
        limit: 10,
        filter: ['priority||eq||High', 'enabled||eq||true']
      };
      const mockResponse = {
        responseCode: 200,
        data: [{ id: 1, title: 'Immediate Action', enabled: true, priority: 'High', description: 'Requires immediate attention' }],
        total: 1
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getRecommendedType(filterParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), filterParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle search by title', async () => {
      // Arrange
      const searchParams = {
        page: 1,
        limit: 10,
        filter: ['title||like||%maintenance%']
      };
      const mockResponse = {
        responseCode: 200,
        data: [{ id: 2, title: 'Scheduled Maintenance', enabled: true, priority: 'Medium', description: 'Schedule for next maintenance cycle' }],
        total: 1
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getRecommendedType(searchParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), searchParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle sorting parameters', async () => {
      // Arrange
      const sortParams = {
        page: 1,
        limit: 10,
        sort: 'priority,DESC'
      };
      const mockResponse = {
        responseCode: 200,
        data: [
          { id: 1, title: 'Immediate Action', enabled: true, priority: 'High', description: 'Requires immediate attention' },
          { id: 2, title: 'Scheduled Maintenance', enabled: true, priority: 'Medium', description: 'Schedule for next maintenance cycle' }
        ],
        total: 2
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getRecommendedType(sortParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), sortParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle empty response data', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const emptyResponse = {
        responseCode: 200,
        data: [],
        total: 0
      };
      apiService.getData.and.returnValue(Promise.resolve(emptyResponse));

      // Act
      const result = await service.getRecommendedType(mockParams);

      // Assert
      expect(result.data.length).toBe(0);
      expect(result.total).toBe(0);
    });
  });

  describe('createRecommendedType', () => {
    it('should create recommended type successfully', async () => {
      // Arrange
      const newRecommendedTypeData = {
        title: 'Future Planning',
        description: 'Plan for future implementation',
        priority: 'Low',
        enabled: true
      };
      const mockResponse = {
        responseCode: 200,
        message: 'Recommended type created successfully',
        data: { id: 3, ...newRecommendedTypeData }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.createRecommendedType(newRecommendedTypeData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), newRecommendedTypeData);
      expect(result).toEqual(mockResponse);
    });

    it('should handle validation errors when creating recommended type', async () => {
      // Arrange
      const invalidRecommendedTypeData = { title: '', priority: '' }; // Invalid data
      const mockError = new Error('Validation Error');
      apiService.postData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.createRecommendedType(invalidRecommendedTypeData)).toBeRejectedWith(mockError);
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), invalidRecommendedTypeData);
    });

    it('should handle duplicate recommended type creation', async () => {
      // Arrange
      const duplicateData = {
        title: 'Immediate Action', // Already exists
        priority: 'High',
        enabled: true
      };
      const mockError = new Error('Recommended type already exists');
      apiService.postData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.createRecommendedType(duplicateData)).toBeRejectedWith(mockError);
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), duplicateData);
    });

    it('should create recommended type with different priorities', async () => {
      // Arrange
      const priorities = ['High', 'Medium', 'Low', 'Critical'];

      for (const priority of priorities) {
        const recommendedTypeData = {
          title: `${priority} Priority Action`,
          description: `${priority} priority recommendation`,
          priority: priority,
          enabled: true
        };
        const mockResponse = {
          responseCode: 200,
          message: 'Recommended type created successfully',
          data: { id: Math.random(), ...recommendedTypeData }
        };
        apiService.postData.and.returnValue(Promise.resolve(mockResponse));

        // Act
        const result = await service.createRecommendedType(recommendedTypeData);

        // Assert
        expect(result.data.priority).toBe(priority);
      }
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle network timeout errors', async () => {
      // Arrange
      const timeoutError = new Error('Request timeout');
      timeoutError.name = 'TimeoutError';
      apiService.getData.and.returnValue(Promise.reject(timeoutError));

      // Act & Assert
      await expectAsync(service.getRecommendedType({})).toBeRejectedWith(timeoutError);
    });

    it('should handle server errors (500)', async () => {
      // Arrange
      const serverError = new Error('Internal Server Error');
      serverError.name = 'ServerError';
      apiService.postData.and.returnValue(Promise.reject(serverError));

      // Act & Assert
      await expectAsync(service.createRecommendedType({})).toBeRejectedWith(serverError);
    });

    it('should handle unauthorized access errors', async () => {
      // Arrange
      const unauthorizedError = new Error('Unauthorized');
      unauthorizedError.name = 'UnauthorizedError';
      apiService.getData.and.returnValue(Promise.reject(unauthorizedError));

      // Act & Assert
      await expectAsync(service.getRecommendedType({})).toBeRejectedWith(unauthorizedError);
    });

    it('should handle malformed response data', async () => {
      // Arrange
      const malformedResponse = {
        responseCode: 200,
        data: null, // Malformed data
        total: 0
      };
      apiService.getData.and.returnValue(Promise.resolve(malformedResponse));

      // Act
      const result = await service.getRecommendedType({});

      // Assert
      expect(result.data).toBeNull();
      expect(result.total).toBe(0);
    });

    it('should handle priority validation edge cases', async () => {
      // Arrange
      const edgeCasePriorities = ['URGENT', 'emergency', 'Normal', ''];

      for (const priority of edgeCasePriorities) {
        const recommendedTypeData = {
          title: `Test Priority ${priority}`,
          description: 'Test description',
          priority: priority,
          enabled: true
        };

        if (priority === '') {
          // Empty priority should fail
          const mockError = new Error('Priority is required');
          apiService.postData.and.returnValue(Promise.reject(mockError));

          await expectAsync(service.createRecommendedType(recommendedTypeData)).toBeRejectedWith(mockError);
        } else {
          // Valid priorities should succeed
          const mockResponse = {
            responseCode: 200,
            message: 'Recommended type created successfully',
            data: { id: Math.random(), ...recommendedTypeData }
          };
          apiService.postData.and.returnValue(Promise.resolve(mockResponse));

          const result = await service.createRecommendedType(recommendedTypeData);
          expect(result.data.priority).toBe(priority);
        }
      }
    });
  });
});
