import { TestBed } from '@angular/core/testing';
import { InspectionToolService } from './inspection-tool.service';
import { ApiService } from '../../api.service';

describe('InspectionToolService', () => {
  let service: InspectionToolService;
  let apiService: jasmine.SpyObj<ApiService>;

  beforeEach(() => {
    const apiSpy = jasmine.createSpyObj('ApiService', ['getData', 'postData', 'putData', 'deleteData']);

    TestBed.configureTestingModule({
      providers: [
        InspectionToolService,
        { provide: ApiService, useValue: apiSpy }
      ]
    });

    service = TestBed.inject(InspectionToolService);
    apiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
  });

  beforeEach(() => {
    spyOn(console, 'log');
    spyOn(console, 'error');
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getInspectionTool', () => {
    it('should fetch inspection tools successfully', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const mockResponse = {
        responseCode: 200,
        data: [
          { id: 1, title: 'Safety Checklist', enabled: true, category: 'Safety', description: 'Daily safety inspection checklist' },
          { id: 2, title: 'Quality Audit', enabled: true, category: 'Quality', description: 'Quality control audit tool' }
        ],
        total: 2
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getInspectionTool(mockParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), mockParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle API errors when fetching inspection tools', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const mockError = new Error('Network Error');
      apiService.getData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.getInspectionTool(mockParams)).toBeRejectedWith(mockError);
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), mockParams);
    });

    it('should handle filtering by category', async () => {
      // Arrange
      const filterParams = {
        page: 1,
        limit: 10,
        filter: ['category||eq||Safety', 'enabled||eq||true']
      };
      const mockResponse = {
        responseCode: 200,
        data: [{ id: 1, title: 'Safety Checklist', enabled: true, category: 'Safety', description: 'Daily safety inspection checklist' }],
        total: 1
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getInspectionTool(filterParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), filterParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle search by title', async () => {
      // Arrange
      const searchParams = {
        page: 1,
        limit: 10,
        filter: ['title||like||%audit%']
      };
      const mockResponse = {
        responseCode: 200,
        data: [{ id: 2, title: 'Quality Audit', enabled: true, category: 'Quality', description: 'Quality control audit tool' }],
        total: 1
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getInspectionTool(searchParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), searchParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle sorting parameters', async () => {
      // Arrange
      const sortParams = {
        page: 1,
        limit: 10,
        sort: 'title,ASC'
      };
      const mockResponse = {
        responseCode: 200,
        data: [
          { id: 2, title: 'Quality Audit', enabled: true, category: 'Quality', description: 'Quality control audit tool' },
          { id: 1, title: 'Safety Checklist', enabled: true, category: 'Safety', description: 'Daily safety inspection checklist' }
        ],
        total: 2
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getInspectionTool(sortParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), sortParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle empty response data', async () => {
      // Arrange
      const mockParams = { page: 1, limit: 10 };
      const emptyResponse = {
        responseCode: 200,
        data: [],
        total: 0
      };
      apiService.getData.and.returnValue(Promise.resolve(emptyResponse));

      // Act
      const result = await service.getInspectionTool(mockParams);

      // Assert
      expect(result.data.length).toBe(0);
      expect(result.total).toBe(0);
    });
  });

  describe('createInspectionTool', () => {
    it('should create inspection tool successfully', async () => {
      // Arrange
      const newInspectionToolData = {
        title: 'Environmental Audit',
        description: 'Environmental compliance audit tool',
        category: 'Environmental',
        enabled: true
      };
      const mockResponse = {
        responseCode: 200,
        message: 'Inspection tool created successfully',
        data: { id: 3, ...newInspectionToolData }
      };
      apiService.postData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.createInspectionTool(newInspectionToolData);

      // Assert
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), newInspectionToolData);
      expect(result).toEqual(mockResponse);
    });

    it('should handle validation errors when creating inspection tool', async () => {
      // Arrange
      const invalidInspectionToolData = { title: '', category: '' }; // Invalid data
      const mockError = new Error('Validation Error');
      apiService.postData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.createInspectionTool(invalidInspectionToolData)).toBeRejectedWith(mockError);
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), invalidInspectionToolData);
    });

    it('should handle duplicate inspection tool creation', async () => {
      // Arrange
      const duplicateData = {
        title: 'Safety Checklist', // Already exists
        category: 'Safety',
        enabled: true
      };
      const mockError = new Error('Inspection tool already exists');
      apiService.postData.and.returnValue(Promise.reject(mockError));

      // Act & Assert
      await expectAsync(service.createInspectionTool(duplicateData)).toBeRejectedWith(mockError);
      expect(apiService.postData).toHaveBeenCalledWith(jasmine.any(String), duplicateData);
    });

    it('should create inspection tool with different categories', async () => {
      // Arrange
      const categories = ['Safety', 'Quality', 'Environmental', 'Security'];

      for (const category of categories) {
        const inspectionToolData = {
          title: `${category} Inspection`,
          description: `${category} inspection tool`,
          category: category,
          enabled: true
        };
        const mockResponse = {
          responseCode: 200,
          message: 'Inspection tool created successfully',
          data: { id: Math.random(), ...inspectionToolData }
        };
        apiService.postData.and.returnValue(Promise.resolve(mockResponse));

        // Act
        const result = await service.createInspectionTool(inspectionToolData);

        // Assert
        expect(result.data.category).toBe(category);
      }
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle network timeout errors', async () => {
      // Arrange
      const timeoutError = new Error('Request timeout');
      timeoutError.name = 'TimeoutError';
      apiService.getData.and.returnValue(Promise.reject(timeoutError));

      // Act & Assert
      await expectAsync(service.getInspectionTool({})).toBeRejectedWith(timeoutError);
    });

    it('should handle server errors (500)', async () => {
      // Arrange
      const serverError = new Error('Internal Server Error');
      serverError.name = 'ServerError';
      apiService.postData.and.returnValue(Promise.reject(serverError));

      // Act & Assert
      await expectAsync(service.createInspectionTool({})).toBeRejectedWith(serverError);
    });

    it('should handle unauthorized access errors', async () => {
      // Arrange
      const unauthorizedError = new Error('Unauthorized');
      unauthorizedError.name = 'UnauthorizedError';
      apiService.getData.and.returnValue(Promise.reject(unauthorizedError));

      // Act & Assert
      await expectAsync(service.getInspectionTool({})).toBeRejectedWith(unauthorizedError);
    });

    it('should handle malformed response data', async () => {
      // Arrange
      const malformedResponse = {
        responseCode: 200,
        data: null, // Malformed data
        total: 0
      };
      apiService.getData.and.returnValue(Promise.resolve(malformedResponse));

      // Act
      const result = await service.getInspectionTool({});

      // Assert
      expect(result.data).toBeNull();
      expect(result.total).toBe(0);
    });

    it('should handle pagination edge cases', async () => {
      // Arrange
      const edgeCaseParams = {
        page: 0, // Invalid page
        limit: -1 // Invalid limit
      };
      const mockResponse = {
        responseCode: 200,
        data: [],
        total: 0
      };
      apiService.getData.and.returnValue(Promise.resolve(mockResponse));

      // Act
      const result = await service.getInspectionTool(edgeCaseParams);

      // Assert
      expect(apiService.getData).toHaveBeenCalledWith(jasmine.any(String), edgeCaseParams);
      expect(result.data.length).toBe(0);
    });
  });
});
