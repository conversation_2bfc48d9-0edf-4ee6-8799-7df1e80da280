export interface AppSettingsResponse {
  id: number;
  enabled: boolean;
  isDeleted: boolean;
  meter: number;
  whatappNumber: string;
  shareAppMessage: string;
  contactNumber: string;
  playstoreLink: string;
  appstoreLink: string;
  aboutUsLink: string;
  contactUsEmail: string;
  termsAndConditionsLink: string;
  privacyPolicyLink: string;
  sosRadius: number;
  referralAmount: number;
  rewardAmount: number;
  observationRewardAmount: number;
  safeActAmount: number;
  unSafeActAmount: number;
  unSafeActAddWithFixitAmount: number;
  unSafeActfiFiEditAmount: number;
  unSafeActFixitAmount: number;
  crisisEmails: string[];
  crisisCCEmails: string[];
  inEvent: boolean;
  isReward: boolean;
  priority: null;
  validVersions: any[];
}