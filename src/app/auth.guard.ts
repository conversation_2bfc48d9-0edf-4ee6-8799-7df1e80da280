import { CanActivateFn } from '@angular/router';
import { inject } from '@angular/core';
import { Router } from '@angular/router';

export const authGuard: CanActivateFn = (route, state) => {
  const router = inject(Router);
  
  const token = localStorage.getItem('token');

  if (!token) {
    router.navigate(['/']);  // Redirect to login page
    return false;  // Prevent navigation to the requested route
  }

  // Token exists, allow access to the requested route
  return true;
};
