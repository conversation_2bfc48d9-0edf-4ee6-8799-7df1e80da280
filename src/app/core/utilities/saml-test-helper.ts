/**
 * SAML Test Helper Utilities
 * Utility functions to help test SAML functionality
 */

/**
 * Mock SAML token for testing purposes
 * DO NOT use in production
 */
export const generateMockSamlToken = (): string => {
  return 'mock-saml-token-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
};

/**
 * Simulate SAML callback URL for testing
 */
export const createMockSamlCallbackUrl = (token: string): string => {
  const baseUrl = window.location.origin + window.location.pathname;
  return `${baseUrl}?redirect=${encodeURIComponent(`callback?token=${token}`)}`;
};

/**
 * Test helper to validate email format for SAML
 */
export const isValidAdaniEmail = (email: string): boolean => {
  if (!email || typeof email !== 'string') {
    return false;
  }
  // Check if it ends with @adani.com and has content before the @
  const atIndex = email.indexOf('@');
  return atIndex > 0 && email.endsWith('@adani.com');
};