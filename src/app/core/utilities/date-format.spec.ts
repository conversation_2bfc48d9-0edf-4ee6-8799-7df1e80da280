import { convertDate } from './date-format';

describe('convertDate', () => {
  it('should convert date to last day of month with 18:30 time', () => {
    // Arrange
    const inputDate = '2024-01-15';

    // Act
    const result = convertDate(inputDate);

    // Assert
    const expectedDate = new Date(2024, 0, 31, 18, 30, 0, 0);
    expect(result).toBe(expectedDate.toISOString());
  });

  it('should handle February in non-leap year', () => {
    // Arrange
    const inputDate = '2023-02-10';

    // Act
    const result = convertDate(inputDate);

    // Assert
    const expectedDate = new Date(2023, 1, 28, 18, 30, 0, 0);
    expect(result).toBe(expectedDate.toISOString());
  });

  it('should handle February in leap year', () => {
    // Arrange
    const inputDate = '2024-02-10';

    // Act
    const result = convertDate(inputDate);

    // Assert
    const expectedDate = new Date(2024, 1, 29, 18, 30, 0, 0);
    expect(result).toBe(expectedDate.toISOString());
  });

  it('should handle months with 30 days', () => {
    // Arrange
    const inputDate = '2024-04-15'; // April has 30 days

    // Act
    const result = convertDate(inputDate);

    // Assert
    const expectedDate = new Date(2024, 3, 30, 18, 30, 0, 0);
    expect(result).toBe(expectedDate.toISOString());
  });

  it('should handle months with 31 days', () => {
    // Arrange
    const inputDate = '2024-03-15'; // March has 31 days

    // Act
    const result = convertDate(inputDate);

    // Assert
    const expectedDate = new Date(2024, 2, 31, 18, 30, 0, 0);
    expect(result).toBe(expectedDate.toISOString());
  });

  it('should handle December (year boundary)', () => {
    // Arrange
    const inputDate = '2024-12-01';

    // Act
    const result = convertDate(inputDate);

    // Assert
    const expectedDate = new Date(2024, 11, 31, 18, 30, 0, 0);
    expect(result).toBe(expectedDate.toISOString());
  });

  it('should handle January (year boundary)', () => {
    // Arrange
    const inputDate = '2024-01-01';

    // Act
    const result = convertDate(inputDate);

    // Assert
    const expectedDate = new Date(2024, 0, 31, 18, 30, 0, 0);
    expect(result).toBe(expectedDate.toISOString());
  });

  it('should handle date already at end of month', () => {
    // Arrange
    const inputDate = '2024-01-31';

    // Act
    const result = convertDate(inputDate);

    // Assert
    const expectedDate = new Date(2024, 0, 31, 18, 30, 0, 0);
    expect(result).toBe(expectedDate.toISOString());
  });

  it('should handle different date formats', () => {
    // Arrange
    const dateFormats = [
      '2024-01-15',
      '2024/01/15',
      'January 15, 2024',
      '01/15/2024',
      '2024-01-15T10:30:00Z'
    ];

    // Act & Assert
    dateFormats.forEach(dateFormat => {
      const result = convertDate(dateFormat);
      const expectedDate = new Date(2024, 0, 31, 18, 30, 0, 0);
      expect(result).toBe(expectedDate.toISOString());
    });
  });

  it('should return "Invalid date" for invalid input', () => {
    // Arrange
    const invalidDates = [
      'invalid-date',
      '',
      '2024-13-01', // Invalid month
      'not-a-date',
      '2024/13/45'
    ];

    // Act & Assert
    invalidDates.forEach(invalidDate => {
      const result = convertDate(invalidDate);
      expect(result).toBe('Invalid date');
    });
  });

  it('should handle date overflow correctly', () => {
    // Arrange - JavaScript Date constructor adjusts invalid dates
    const overflowDate = '2024-02-30'; // February 30th becomes March 1st

    // Act
    const result = convertDate(overflowDate);

    // Assert - Should return the last day of March (since Feb 30 -> Mar 1)
    const expectedDate = new Date(2024, 2, 31, 18, 30, 0, 0); // March 31st
    expect(result).toBe(expectedDate.toISOString());
  });

  it('should handle edge case dates', () => {
    // Arrange & Act & Assert
    const edgeCases = [
      { input: '2000-02-01', expectedMonth: 1, expectedDay: 29 }, // Leap year 2000
      { input: '1900-02-01', expectedMonth: 1, expectedDay: 28 }, // Non-leap year 1900
      { input: '2024-06-15', expectedMonth: 5, expectedDay: 30 }, // June has 30 days
      { input: '2024-07-15', expectedMonth: 6, expectedDay: 31 }, // July has 31 days
    ];

    edgeCases.forEach(testCase => {
      const result = convertDate(testCase.input);
      const inputDate = new Date(testCase.input);
      const expectedDate = new Date(inputDate.getFullYear(), testCase.expectedMonth, testCase.expectedDay, 18, 30, 0, 0);
      expect(result).toBe(expectedDate.toISOString());
    });
  });

  it('should preserve year correctly across different years', () => {
    // Arrange
    const years = [2020, 2021, 2022, 2023, 2024, 2025];

    // Act & Assert
    years.forEach(year => {
      const inputDate = `${year}-06-15`;
      const result = convertDate(inputDate);
      const expectedDate = new Date(year, 5, 30, 18, 30, 0, 0);
      expect(result).toBe(expectedDate.toISOString());
    });
  });

  it('should handle time components in input (should be ignored)', () => {
    // Arrange
    const inputDate = '2024-01-15T08:45:30.123Z';

    // Act
    const result = convertDate(inputDate);

    // Assert
    const expectedDate = new Date(2024, 0, 31, 18, 30, 0, 0);
    expect(result).toBe(expectedDate.toISOString());
  });

  it('should handle timezone in input (should be normalized)', () => {
    // Arrange
    const inputDate = '2024-01-15T08:45:30+05:30';

    // Act
    const result = convertDate(inputDate);

    // Assert
    const expectedDate = new Date(2024, 0, 31, 18, 30, 0, 0);
    expect(result).toBe(expectedDate.toISOString());
  });
});
