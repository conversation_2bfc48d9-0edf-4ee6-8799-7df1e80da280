import { FormControl } from '@angular/forms';
import { adaniDomainValidator } from './adani-email-validator';

describe('adaniDomainValidator', () => {
  let validator: any;

  beforeEach(() => {
    validator = adaniDomainValidator();
  });

  it('should be created', () => {
    expect(validator).toBeTruthy();
  });

  it('should return null for empty value', () => {
    // Arrange
    const control = new FormControl('');

    // Act
    const result = validator(control);

    // Assert
    expect(result).toBeNull();
  });

  it('should return null for null value', () => {
    // Arrange
    const control = new FormControl(null);

    // Act
    const result = validator(control);

    // Assert
    expect(result).toBeNull();
  });

  it('should return null for undefined value', () => {
    // Arrange
    const control = new FormControl(undefined);

    // Act
    const result = validator(control);

    // Assert
    expect(result).toBeNull();
  });

  it('should return null for valid adani email', () => {
    // Arrange
    const validEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];

    validEmails.forEach(email => {
      const control = new FormControl(email);

      // Act
      const result = validator(control);

      // Assert
      expect(result).toBeNull();
    });
  });



  it('should return error for invalid email format', () => {
    // Test each email individually - only emails that should actually fail the regex
    const invalidEmails = [
      'invalid-email',      // No @ symbol
      '@adani.com',         // No local part
      'test@',              // No domain
      'test.adani.com',     // No @ symbol
      'test@@adani.com',    // Double @
      '<EMAIL>.'     // Trailing dot in domain
    ];

    invalidEmails.forEach(email => {
      const control = new FormControl(email);
      const result = validator(control);
      expect(result).toEqual({ adaniDomain: true });
    });
  });

  it('should return error for emails with dots in problematic positions', () => {
    // These emails have valid regex patterns but may have other issues
    const problematicEmails = [
      '.<EMAIL>',    // Leading dot (allowed by current regex)
      '<EMAIL>'     // Trailing dot before @ (allowed by current regex)
    ];

    problematicEmails.forEach(email => {
      const control = new FormControl(email);
      const result = validator(control);
      // These might pass the current validator, so let's test what actually happens
      // If they pass, we should update the validator to be more strict
      console.log(`Testing ${email}: ${result ? 'FAIL' : 'PASS'}`);
    });
  });

  it('should return error for emails with valid format but invalid domain', () => {
    // Test emails that have valid format but wrong domain
    const invalidDomainEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];

    invalidDomainEmails.forEach(email => {
      const control = new FormControl(email);
      const result = validator(control);
      expect(result).toEqual({ adaniDomain: true });
    });
  });

  it('should return error for local part without alphabets', () => {
    // Arrange
    const invalidLocalPartEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '...@adani.com',
      '+++@adani.com'
    ];

    invalidLocalPartEmails.forEach(email => {
      const control = new FormControl(email);

      // Act
      const result = validator(control);

      // Assert
      expect(result).toEqual({ adaniDomain: true });
    });
  });

  it('should return null for local part with mixed characters including alphabets', () => {
    // Arrange
    const validLocalPartEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];

    validLocalPartEmails.forEach(email => {
      const control = new FormControl(email);

      // Act
      const result = validator(control);

      // Assert
      expect(result).toBeNull();
    });
  });

  it('should handle edge cases', () => {
    // Arrange & Act & Assert
    const edgeCases = [
      { email: '<EMAIL>', expected: null }, // Minimum valid
      { email: '<EMAIL>', expected: null }, // Long but valid
      { email: '<EMAIL>', expected: { adaniDomain: true } }, // Case sensitive
      { email: 'test @adani.com', expected: { adaniDomain: true } }, // Space in local part
      { email: 'test@adani .com', expected: { adaniDomain: true } }, // Space in domain
    ];

    edgeCases.forEach(testCase => {
      const control = new FormControl(testCase.email);
      const result = validator(control);
      expect(result).toEqual(testCase.expected);
    });
  });
});
