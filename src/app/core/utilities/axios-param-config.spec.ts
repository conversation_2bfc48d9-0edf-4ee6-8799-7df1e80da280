import { createAxiosConfig } from './axios-param-config';

describe('createAxiosConfig', () => {
  it('should create axios config with simple params', () => {
    // Arrange
    const params = {
      page: 1,
      limit: 10,
      sort: 'name,ASC'
    };

    // Act
    const config = createAxiosConfig(params);

    // Assert
    expect(config.params).toEqual(params);
    expect(config.paramsSerializer).toBeDefined();
  });

  it('should serialize simple params correctly', () => {
    // Arrange
    const params = {
      page: 1,
      limit: 10,
      sort: 'name,ASC'
    };
    const config = createAxiosConfig(params);

    // Act
    const serialized = (config.paramsSerializer as Function)(params);

    // Assert
    expect(serialized).toBe('page=1&limit=10&sort=name%2CASC');
  });

  it('should handle array parameters correctly', () => {
    // Arrange
    const params = {
      filter: ['enabled||eq||true', 'businessUnitId||eq||0'],
      join: ['plant', 'cluster'],
      page: 1
    };
    const config = createAxiosConfig(params);

    // Act
    const serialized = (config.paramsSerializer as Function)(params);

    // Assert
    expect(serialized).toContain('filter=enabled%7C%7Ceq%7C%7Ctrue');
    expect(serialized).toContain('filter=businessUnitId%7C%7Ceq%7C%7C0');
    expect(serialized).toContain('join=plant');
    expect(serialized).toContain('join=cluster');
    expect(serialized).toContain('page=1');
  });

  it('should handle mixed array and simple parameters', () => {
    // Arrange
    const params = {
      page: 1,
      limit: 10,
      filter: ['enabled||eq||true'],
      sort: 'name,ASC'
    };
    const config = createAxiosConfig(params);

    // Act
    const serialized = (config.paramsSerializer as Function)(params);

    // Assert
    expect(serialized).toContain('page=1');
    expect(serialized).toContain('limit=10');
    expect(serialized).toContain('filter=enabled%7C%7Ceq%7C%7Ctrue');
    expect(serialized).toContain('sort=name%2CASC');
  });

  it('should handle empty array parameters', () => {
    // Arrange
    const params = {
      page: 1,
      filter: [],
      sort: 'name,ASC'
    };
    const config = createAxiosConfig(params);

    // Act
    const serialized = (config.paramsSerializer as Function)(params);

    // Assert
    expect(serialized).toBe('page=1&sort=name%2CASC');
    expect(serialized).not.toContain('filter=');
  });

  it('should handle empty object parameters', () => {
    // Arrange
    const params = {};
    const config = createAxiosConfig(params);

    // Act
    const serialized = (config.paramsSerializer as Function)(params);

    // Assert
    expect(serialized).toBe('');
  });

  it('should handle special characters in parameters', () => {
    // Arrange
    const params = {
      search: '<EMAIL>',
      filter: 'name||like||%test%',
      special: 'value with spaces & symbols!'
    };
    const config = createAxiosConfig(params);

    // Act
    const serialized = (config.paramsSerializer as Function)(params);

    // Assert
    expect(serialized).toContain('search=test%40example.com');
    expect(serialized).toContain('filter=name%7C%7Clike%7C%7C%25test%25');
    expect(serialized).toContain('special=value+with+spaces+%26+symbols%21');
  });

  it('should handle numeric and boolean values', () => {
    // Arrange
    const params = {
      page: 1,
      enabled: true,
      disabled: false,
      count: 0,
      price: 99.99
    };
    const config = createAxiosConfig(params);

    // Act
    const serialized = (config.paramsSerializer as Function)(params);

    // Assert
    expect(serialized).toContain('page=1');
    expect(serialized).toContain('enabled=true');
    expect(serialized).toContain('disabled=false');
    expect(serialized).toContain('count=0');
    expect(serialized).toContain('price=99.99');
  });

  it('should handle null and undefined values', () => {
    // Arrange
    const params = {
      page: 1,
      nullValue: null,
      undefinedValue: undefined,
      emptyString: ''
    };
    const config = createAxiosConfig(params);

    // Act
    const serialized = (config.paramsSerializer as Function)(params);

    // Assert
    expect(serialized).toContain('page=1');
    expect(serialized).toContain('nullValue=null');
    expect(serialized).toContain('undefinedValue=undefined');
    expect(serialized).toContain('emptyString=');
  });

  it('should handle complex nested array scenarios', () => {
    // Arrange
    const params = {
      filter: [
        'enabled||eq||true',
        'businessUnitId||eq||0',
        'isDeleted||eq||false'
      ],
      join: ['plant', 'cluster', 'opco'],
      sort: 'name,ASC',
      page: 1,
      limit: 10
    };
    const config = createAxiosConfig(params);

    // Act
    const serialized = (config.paramsSerializer as Function)(params);

    // Assert
    // Check that all filter values are present
    expect(serialized).toContain('filter=enabled%7C%7Ceq%7C%7Ctrue');
    expect(serialized).toContain('filter=businessUnitId%7C%7Ceq%7C%7C0');
    expect(serialized).toContain('filter=isDeleted%7C%7Ceq%7C%7Cfalse');
    
    // Check that all join values are present
    expect(serialized).toContain('join=plant');
    expect(serialized).toContain('join=cluster');
    expect(serialized).toContain('join=opco');
    
    // Check other parameters
    expect(serialized).toContain('sort=name%2CASC');
    expect(serialized).toContain('page=1');
    expect(serialized).toContain('limit=10');
  });
});
