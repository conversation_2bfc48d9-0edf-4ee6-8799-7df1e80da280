import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ToastMessageComponent } from './toast-message.component';

describe('ToastMessageComponent', () => {
  let component: ToastMessageComponent;
  let fixture: ComponentFixture<ToastMessageComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ToastMessageComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(ToastMessageComponent);
    component = fixture.componentInstance;
  });

  beforeEach(() => {
    spyOn(console, 'log');
    spyOn(console, 'error');
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Component Properties', () => {
    it('should have default values', () => {
      expect(component.showToast).toBe(false);
      expect(component.toastMsg).toBe('');
      expect(component.toastType).toBe('success');
    });

    it('should update properties correctly', () => {
      // Arrange & Act
      component.showToast = true;
      component.toastMsg = 'Test message';
      component.toastType = 'error';

      // Assert
      expect(component.showToast).toBe(true);
      expect(component.toastMsg).toBe('Test message');
      expect(component.toastType).toBe('error');
    });
  });

  describe('Toast Display', () => {
    it('should show toast when showToast is true', () => {
      // Arrange
      component.showToast = true;
      component.toastMsg = 'Test message';

      // Act
      fixture.detectChanges();

      // Assert
      const toastElement = fixture.nativeElement.querySelector('.toast');
      expect(toastElement).toBeTruthy();
    });

    it('should hide toast when showToast is false', () => {
      // Arrange
      component.showToast = false;

      // Act
      fixture.detectChanges();

      // Assert
      const toastElement = fixture.nativeElement.querySelector('.toast');
      expect(toastElement).toBeFalsy();
    });

    it('should display correct message', () => {
      // Arrange
      component.showToast = true;
      component.toastMsg = 'Success message';

      // Act
      fixture.detectChanges();

      // Assert
      const messageElement = fixture.nativeElement.querySelector('.toast-message');
      expect(messageElement?.textContent?.trim()).toBe('Success message');
    });

    it('should apply correct CSS class for toast type', () => {
      // Arrange
      component.showToast = true;
      component.toastType = 'error';

      // Act
      fixture.detectChanges();

      // Assert
      const toastElement = fixture.nativeElement.querySelector('.toast');
      expect(toastElement?.classList.contains('toast-error')).toBe(true);
    });
  });

  describe('Toast Methods', () => {
    it('should show success toast', () => {
      // Arrange
      const message = 'Success!';

      // Act
      component.showSuccessToast(message);

      // Assert
      expect(component.toastMsg).toBe(message);
      expect(component.toastType).toBe('success');
      expect(component.showToast).toBe(true);
    });

    it('should show error toast', () => {
      // Arrange
      const message = 'Error!';

      // Act
      component.showErrorToast(message);

      // Assert
      expect(component.toastMsg).toBe(message);
      expect(component.toastType).toBe('error');
      expect(component.showToast).toBe(true);
    });

    it('should show error toast with custom timeout', () => {
      // Arrange
      const message = 'Error with timeout!';
      const timeout = 5000;

      // Act
      component.showErrorToast(message, timeout);

      // Assert
      expect(component.toastMsg).toBe(message);
      expect(component.toastType).toBe('error');
      expect(component.showToast).toBe(true);
    });

    it('should reset toast', () => {
      // Arrange
      component.showToast = true;
      component.toastMsg = 'Test message';

      // Act
      component.resetToast();

      // Assert
      expect(component.showToast).toBe(false);
    });
  });

  describe('Auto Hide Functionality', () => {
    beforeEach(() => {
      jasmine.clock().install();
    });

    afterEach(() => {
      jasmine.clock().uninstall();
    });

    it('should auto hide after default duration', () => {
      // Arrange
      spyOn(component, 'resetToast');

      // Act
      component.showSuccessToast('Test message');
      jasmine.clock().tick(2001);

      // Assert
      expect(component.resetToast).toHaveBeenCalled();
    });

    it('should auto hide after custom duration', () => {
      // Arrange
      spyOn(component, 'resetToast');
      const customTimeout = 5000;

      // Act
      component.showErrorToast('Test error', customTimeout);
      jasmine.clock().tick(customTimeout + 1);

      // Assert
      expect(component.resetToast).toHaveBeenCalled();
    });

    it('should clear existing timeout when triggering new toast', () => {
      // Arrange
      spyOn(window, 'clearTimeout');

      // Act
      component.showSuccessToast('First message');
      component.showErrorToast('Second message');

      // Assert
      expect(window.clearTimeout).toHaveBeenCalled();
    });
  });

  describe('Toast Integration', () => {
    it('should display success toast in DOM', () => {
      // Arrange & Act
      component.showSuccessToast('Success message');
      fixture.detectChanges();

      // Assert
      const toastElement = fixture.nativeElement.querySelector('.toast');
      expect(toastElement).toBeTruthy();
      expect(component.toastType).toBe('success');
    });

    it('should display error toast in DOM', () => {
      // Arrange & Act
      component.showErrorToast('Error message');
      fixture.detectChanges();

      // Assert
      const toastElement = fixture.nativeElement.querySelector('.toast');
      expect(toastElement).toBeTruthy();
      expect(component.toastType).toBe('error');
    });

    it('should handle multiple toast calls', () => {
      // Arrange & Act
      component.showSuccessToast('First message');
      component.showErrorToast('Second message');
      fixture.detectChanges();

      // Assert
      expect(component.toastMsg).toBe('Second message');
      expect(component.toastType).toBe('error');
      expect(component.showToast).toBe(true);
    });
  });
});
