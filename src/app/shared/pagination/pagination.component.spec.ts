import { ComponentFixture, TestBed } from '@angular/core/testing';
import { PaginationComponent } from './pagination.component';

describe('PaginationComponent', () => {
  let component: PaginationComponent;
  let fixture: ComponentFixture<PaginationComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [PaginationComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(PaginationComponent);
    component = fixture.componentInstance;
  });

  beforeEach(() => {
    // Spy on console.log to avoid cluttering test output
    spyOn(console, 'log');
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Component Initialization', () => {
    it('should have default values', () => {
      expect(component.currentPage).toBe(1);
      expect(component.totalItems).toBe(0);
      expect(component.itemsPerPage).toBe(10);
      expect(component.totalPages).toBe(1);
      expect(component.pages).toEqual([]);
    });

    it('should initialize pagination on ngOnInit', () => {
      // Arrange
      component.totalItems = 50;
      component.itemsPerPage = 10;
      spyOn(component, 'updatePagination');

      // Act
      component.ngOnInit();

      // Assert
      expect(component.updatePagination).toHaveBeenCalled();
    });

    it('should update pagination on ngOnChanges', () => {
      // Arrange
      spyOn(component, 'updatePagination');

      // Act
      component.ngOnChanges();

      // Assert
      expect(component.updatePagination).toHaveBeenCalled();
    });
  });

  describe('updatePagination', () => {
    it('should calculate total pages correctly', () => {
      // Arrange
      component.totalItems = 25;
      component.itemsPerPage = 10;

      // Act
      component.updatePagination();

      // Assert
      expect(component.totalPages).toBe(3);
    });

    it('should handle zero total items', () => {
      // Arrange
      component.totalItems = 0;
      component.itemsPerPage = 10;

      // Act
      component.updatePagination();

      // Assert
      expect(component.totalPages).toBe(0);
    });

    it('should handle exact division', () => {
      // Arrange
      component.totalItems = 30;
      component.itemsPerPage = 10;

      // Act
      component.updatePagination();

      // Assert
      expect(component.totalPages).toBe(3);
    });

    it('should call getPages with correct parameters', () => {
      // Arrange
      component.totalItems = 50;
      component.itemsPerPage = 10;
      component.currentPage = 2;
      spyOn(component, 'getPages').and.returnValue([1, 2, 3, 4, 5]);

      // Act
      component.updatePagination();

      // Assert
      expect(component.getPages).toHaveBeenCalledWith(2, 5);
    });

    it('should log pagination information', () => {
      // Arrange
      component.totalItems = 50;
      component.itemsPerPage = 10;

      // Act
      component.updatePagination();

      // Assert
      expect(console.log).toHaveBeenCalledWith(
        'Pagination Component - Total Items: 50, Items Per Page: 10, Total Pages: 5'
      );
    });
  });

  describe('getPages', () => {
    it('should return all pages when total pages <= 5', () => {
      // Act
      const result = component.getPages(1, 3);

      // Assert
      expect(result).toEqual([1, 2, 3]);
    });

    it('should return first 5 pages when current page <= 3', () => {
      // Act
      const result = component.getPages(2, 10);

      // Assert
      expect(result).toEqual([1, 2, 3, 4, 5]);
    });

    it('should return last 5 pages when current page is near end', () => {
      // Act
      const result = component.getPages(9, 10);

      // Assert
      expect(result).toEqual([6, 7, 8, 9, 10]);
    });

    it('should return centered pages when current page is in middle', () => {
      // Act
      const result = component.getPages(5, 10);

      // Assert
      expect(result).toEqual([3, 4, 5, 6, 7]);
    });

    it('should handle edge case with exactly 5 pages', () => {
      // Act
      const result = component.getPages(3, 5);

      // Assert
      expect(result).toEqual([1, 2, 3, 4, 5]);
    });

    it('should handle single page', () => {
      // Act
      const result = component.getPages(1, 1);

      // Assert
      expect(result).toEqual([1]);
    });

    it('should handle zero pages', () => {
      // Act
      const result = component.getPages(1, 0);

      // Assert
      expect(result).toEqual([]);
    });
  });

  describe('changePage', () => {
    beforeEach(() => {
      component.totalItems = 50;
      component.itemsPerPage = 10;
      component.updatePagination();
      spyOn(component.pageChange, 'emit');
    });

    it('should change page and emit event for valid page', () => {
      // Act
      component.changePage(3);

      // Assert
      expect(component.currentPage).toBe(3);
      expect(component.pageChange.emit).toHaveBeenCalledWith(3);
    });

    it('should not change page for page less than 1', () => {
      // Arrange
      const originalPage = component.currentPage;

      // Act
      component.changePage(0);

      // Assert
      expect(component.currentPage).toBe(originalPage);
      expect(component.pageChange.emit).not.toHaveBeenCalled();
    });

    it('should not change page for page greater than total pages', () => {
      // Arrange
      const originalPage = component.currentPage;

      // Act
      component.changePage(10);

      // Assert
      expect(component.currentPage).toBe(originalPage);
      expect(component.pageChange.emit).not.toHaveBeenCalled();
    });

    it('should handle boundary values correctly', () => {
      // Test first page
      component.changePage(1);
      expect(component.currentPage).toBe(1);
      expect(component.pageChange.emit).toHaveBeenCalledWith(1);

      // Test last page
      component.changePage(5);
      expect(component.currentPage).toBe(5);
      expect(component.pageChange.emit).toHaveBeenCalledWith(5);
    });
  });
});
