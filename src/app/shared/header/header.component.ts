import { Component, ElementRef, Input, OnInit, ViewChild } from "@angular/core";
import { ToastMessageComponent } from "../toast-message/toast-message.component"; // Assuming this exists
import { CommonModule } from "@angular/common";
import { AdminModel } from "../../model/admin.model";
import { Router } from "@angular/router";
import { OffcanvasComponent } from "../offcanvas/offcanvas.component";
import { AbstractControl, FormBuilder, FormGroup, ReactiveFormsModule, ValidationErrors, Validators } from "@angular/forms";
import { UpdateService } from "../../services/update/update.service";
import { UploadService } from "../../services/upload/upload.service";
import { ManageSideMenuService } from "../../services/manage-sidemenu/manage-side-menu.service";
import { Modal } from 'bootstrap';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [
    CommonModule,
    OffcanvasComponent,
    ReactiveFormsModule,
    // Removed ImageCropperModule
  ],
  templateUrl: './header.component.html',
  styleUrl: './header.component.scss'
})
export class HeaderComponent implements OnInit {
  public admin: AdminModel | null = null;
  @Input() showElement: boolean = false;
  @ViewChild('dropdownMenuButton', { static: false }) dropdown!: ElementRef;
  @ViewChild('fileInput') fileInput!: ElementRef;
  @ViewChild('deleteConfirmationModalElement') deleteConfirmationModalElement!: ElementRef;
  @ViewChild(ToastMessageComponent) toast!: ToastMessageComponent; // Assuming ToastMessageComponent exists

  isEditUserModalOpen: boolean = false;
  editUserForm!: FormGroup;
  profileImageUrl: string | ArrayBuffer | null = null; // Shows original or selected file preview
  submitted: boolean = false;
  isSubmitting: boolean = false; // Flag for loading state during submit
  deleteConfirmationModalInstance: Modal | null = null;
  // --- Simplified File Handling ---
  selectedFile: File | null = null; // Holds the raw File object for upload
  // --- End Simplified File Handling ---


  constructor(
    private router: Router,
    private fb: FormBuilder,
    private updateService: UpdateService,
    private uploadService: UploadService,
    private manageSideMenuService: ManageSideMenuService
  ) { }

  ngOnInit(): void {
    this.loadAdminData();
    this.initializeForm();
    this.patchFormWithAdminData(); // Initial population
  }

  ngAfterViewInit(): void {
    if (this.deleteConfirmationModalElement) {
      this.deleteConfirmationModalInstance = new Modal(this.deleteConfirmationModalElement.nativeElement);
    } else { console.error("Delete confirmation modal element not found!"); }
  }

  ngOnDestroy(): void {
    this.deleteConfirmationModalInstance?.dispose();
  }

  private loadAdminData(): void {
    const user = localStorage.getItem('user');
    if (user) {
      try {
        this.admin = JSON.parse(user) as AdminModel;
        this.setInitialImage();
      } catch (error) {
        console.error('Error parsing user data from localStorage:', error);
        this.admin = null;
        this.setInitialImage();
      }
    } else {
      console.warn('No user found in localStorage');
      this.admin = null;
      this.setInitialImage();
    }
  }

  private setInitialImage(): void {
    // Set initial image URL from admin data or default
    this.profileImageUrl = this.admin?.profilePicture || '../../../assets/svg/Avatar.svg';
    this.selectedFile = null; // Ensure no file is selected initially
  }


  private initializeForm(): void {
    this.editUserForm = this.fb.group(
      {
        firstName: ['', [Validators.required]],
        lastName: ['', [Validators.required]],
        gender: ['', [Validators.required]],
        email: ['', [Validators.required, Validators.email]]
      },
      { validators: this.customEmailFormatValidator }
    );
    // Optional: Disable email if needed
    // this.editUserForm.get('email')?.disable();
  }

  private patchFormWithAdminData(): void {
    if (this.admin) {
      this.editUserForm.patchValue({
        firstName: this.admin.firstName,
        lastName: this.admin.lastName,
        email: this.admin.email,
        gender: this.admin.gender !== undefined && this.admin.gender !== null ? String(this.admin.gender) : '',
      });
      this.setInitialImage(); // Reset image preview to original/default
    } else {
      this.editUserForm.reset();
      this.setInitialImage();
    }
  }

  // Validator (keep as is)
  customEmailFormatValidator(control: AbstractControl): ValidationErrors | null {
    const emailControl = control.get('email');
    if (emailControl && emailControl.value) {
      const emailPattern = /^[a-zA-Z][a-zA-Z0-9]*(?:[._-][a-zA-Z0-9]+)*@[a-zA-Z0-9]+(?:\.[a-zA-Z0-9]+)*\.[a-zA-Z]{2,3}$/;
      if (!emailPattern.test(emailControl.value)) {
        return { validEmail: true };
      }
    }
    return null;
  }


  closeEditUserModal(): void {
    this.isEditUserModalOpen = false;
    this.submitted = false;
    this.isSubmitting = false;
    this.resetEditForm();
  }

  closeEditModal(): void { // Linked to cross icon
    this.closeEditUserModal();
  }

  // --- Updated File Input Method ---

  onFileChange(event: Event): void {
    const element = event.currentTarget as HTMLInputElement;
    let fileList: FileList | null = element.files;

    if (fileList && fileList.length > 0) {
      const file = fileList[0];

      // Basic validation
      if (!file.type.startsWith('image/')) {
        alert('Please select an image file.');
        this.resetFileInput();
        this.selectedFile = null; // Clear selection
        this.profileImageUrl = this.admin?.profilePicture || '../../../assets/svg/Avatar.svg'; // Revert preview
        return;
      }

      this.selectedFile = file; // Store the raw file object

      // Create a preview using FileReader
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          // Update the preview URL
          this.profileImageUrl = e.target.result;
        }
      };
      reader.readAsDataURL(file); // Read the file as Data URL for preview

    } else {
      // No file selected or selection cancelled
      this.resetFileInput();
      this.selectedFile = null;
      // Revert preview to the original image
      this.profileImageUrl = this.admin?.profilePicture || '../../../assets/svg/Avatar.svg';
    }
  }
  // --- Removed Cropper Methods ---


  async onEditSubmit(): Promise<void> {
    this.submitted = true;
    console.log('Form State on Submit:', this.editUserForm);

    // 1. Validate Form
    if (this.editUserForm.invalid) {
      console.log('Form is invalid. Halting submission.');
      this.editUserForm.markAllAsTouched();
      return;
    }

    // 2. Check Admin ID
    if (!this.admin?.id) {
      console.error('Admin ID is missing.');
      this.toast?.showErrorToast('Cannot update profile: User ID missing.');
      return;
    }

    this.isSubmitting = true;
    console.log('Starting profile update process...');

    let imageUrlToUpdate: string | undefined | null = this.admin?.profilePicture; // Start with existing URL

    // --- Step 1: Upload Image (if new one selected) ---
    if (this.selectedFile) {
      console.log(`New file selected ('${this.selectedFile.name}'). Uploading...`);
      const fileFormData = new FormData();
      // Ensure the key matches what the upload endpoint expects
      fileFormData.append('file', this.selectedFile, this.selectedFile.name);

      try {
        // Call the dedicated upload service method
        const uploadResponse = await this.uploadService.upload(fileFormData);
        // Extract the URL from the response - ** ADJUST PROPERTY NAME AS NEEDED **
        if (uploadResponse) {
          imageUrlToUpdate = uploadResponse;
          console.log('File uploaded successfully. New URL:', imageUrlToUpdate);
        } else {
          // Handle cases where the response might be successful but missing the URL
          console.error('File upload response successful but missing fileUrl:', uploadResponse);
          this.toast?.showErrorToast('File uploaded, but could not get URL. Please try again.');
          this.isSubmitting = false;
          return; // Stop if we didn't get the URL
        }
      } catch (uploadError) {
        console.error('Error uploading profile picture:', uploadError);
        this.toast?.showErrorToast('Failed to upload profile picture. Please try again.');
        this.isSubmitting = false; // Stop loading
        return; // Stop the process if upload fails
      }
    } else {
      console.log('No new file selected. Using existing profile picture URL:', imageUrlToUpdate);
    }

    // --- Step 2: Prepare JSON Data for Update ---
    console.log('Preparing JSON data for profile update...');
    const formValue = this.editUserForm.getRawValue(); // Get text field values

    // Create the plain JavaScript object for the 'data' field
    const jsonData: { [key: string]: any } = {
      firstName: formValue.firstName,
      lastName: formValue.lastName,
      gender: formValue.gender,
      email: formValue.email,
      // ** Assign the determined image URL (either new or existing) **
      profilePicture: imageUrlToUpdate ?? null // Use nullish coalescing to send null if undefined
    };
    console.log('JSON Data to send:', jsonData);

    // Prepare the final payload object for the update service
    const updatePayload = {
      tableName: 'admins', // Ensure correct table name
      id: this.admin.id,
      data: jsonData
    };

    // --- Step 3: Update Profile Record ---
    try {
      console.log('Calling update service with JSON payload:', updatePayload);
      // Call the service method that handles JSON update
      const updateResponse = await this.updateService.update(updatePayload); // Use the correct method name

      console.log('Profile record updated successfully', updateResponse);

      // Handle successful response (update localStorage, etc.)
      if (updateResponse && updateResponse.user) { // Adjust based on your actual update response
        console.log('Updating localStorage with user data from update response.');
        localStorage.setItem('user', JSON.stringify(updateResponse.user));
        this.loadAdminData(); // Reload header data
      } else {
        // If the update response doesn't return the full user, reload anyway
        console.log('Update response did not contain user data, reloading data.');
        this.loadAdminData();
      }

      this.toast?.showSuccessToast('Profile updated successfully!');
      this.closeEditUserModal();

    } catch (updateError) {
      console.error('Error updating profile record:', updateError);
      this.toast?.showErrorToast('Failed to update profile information. Please try again.');
    } finally {
      this.isSubmitting = false; // Stop loading indicator
      console.log('Profile update process finished.');
    }
  }


  resetEditForm(): void {
    this.submitted = false;
    this.isSubmitting = false;
    this.selectedFile = null; // Clear stored file
    this.resetFileInput(); // Clear the actual file input element
    // Removed resetting of cropper state
    this.patchFormWithAdminData(); // Repopulate with original admin data & reset image preview
    this.editUserForm.markAsPristine();
    this.editUserForm.markAsUntouched();
  }

  private resetFileInput(): void {
    if (this.fileInput) {
      this.fileInput.nativeElement.value = "";
    }
    // We don't need to reset selectedFile here as resetEditForm calls patchFormWithAdminData
    // which calls setInitialImage, resetting both profileImageUrl and selectedFile.
  }

  openUserProfileDialog(): void {
    this.resetEditForm(); // Reset/Repopulate form and image to initial state
    this.isEditUserModalOpen = true;
  }

  activateNav(arg0: string): void {
    console.log('activateNav called with:', arg0);
  }

  logout(): void {
    this.deleteConfirmationModalInstance?.show();
    // localStorage.clear();
    // sessionStorage.clear();
    // this.router.navigate(['']);
  }

  sideMenuOpenClose() {
    this.manageSideMenuService.triggerHeaderClick();
  }

  async confirmDelete(): Promise<void> {
    localStorage.clear();
    sessionStorage.clear();
    this.router.navigate(['']);
  }

  closeDeleteConfirmation(): void {
    this.deleteConfirmationModalInstance?.hide();
    // this.userToDelete = null;
  }
}