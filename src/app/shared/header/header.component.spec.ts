import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { HeaderComponent } from './header.component';
import { UpdateService } from '../../services/update/update.service';
import { UploadService } from '../../services/upload/upload.service';
import { ManageSideMenuService } from '../../services/manage-sidemenu/manage-side-menu.service';
import { AdminModel } from '../../model/admin.model';

describe('HeaderComponent', () => {
  let component: HeaderComponent;
  let fixture: ComponentFixture<HeaderComponent>;
  let router: jasmine.SpyObj<Router>;
  let updateService: jasmine.SpyObj<UpdateService>;
  let uploadService: jasmine.SpyObj<UploadService>;
  let manageSideMenuService: jasmine.SpyObj<ManageSideMenuService>;

  const mockAdmin: AdminModel = {
    id: 1,
    enabled: true,
    isDeleted: false,
    firstName: 'John',
    lastName: 'Doe',
    gender: '1',
    email: '<EMAIL>',
    contactNumber: '1234567890',
    dob: '1990-01-01',
    priority: 1,
    adminsRoleId: 1,
    dialCode: '+91',
    countryCode: 'IN',
    steps: 0,
    profilePicture: 'https://example.com/profile.jpg',
    status: 1,
    opCoId: null,
    plantIds: [1],
    plant: [{ id: 1, name: 'Test Plant' }],
    designationId: 1,
    departmentId: 1,
    otpCount: 0,
    otpTime: null,
    verifyCount: 0,
    verifyTime: null,
    otpBlock: false,
    OSVersion: 'iOS 15.0',
    pin: '1234',
    createdBy: null,
    createdTimestamp: '2024-01-01T00:00:00Z',
    accessToken: null,
    updatedBy: 1,
    wbiRoleId: 1,
    wbiDepartmentId: 1,
    updatedTimestamp: '2024-01-01T00:00:00Z'
  };

  beforeEach(async () => {
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    const updateServiceSpy = jasmine.createSpyObj('UpdateService', ['update']);
    const uploadServiceSpy = jasmine.createSpyObj('UploadService', ['upload']);
    const manageSideMenuServiceSpy = jasmine.createSpyObj('ManageSideMenuService', ['triggerHeaderClick']);

    await TestBed.configureTestingModule({
      imports: [HeaderComponent, ReactiveFormsModule],
      providers: [
        FormBuilder,
        { provide: Router, useValue: routerSpy },
        { provide: UpdateService, useValue: updateServiceSpy },
        { provide: UploadService, useValue: uploadServiceSpy },
        { provide: ManageSideMenuService, useValue: manageSideMenuServiceSpy }
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(HeaderComponent);
    component = fixture.componentInstance;
    router = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    updateService = TestBed.inject(UpdateService) as jasmine.SpyObj<UpdateService>;
    uploadService = TestBed.inject(UploadService) as jasmine.SpyObj<UploadService>;
    manageSideMenuService = TestBed.inject(ManageSideMenuService) as jasmine.SpyObj<ManageSideMenuService>;
  });

  beforeEach(() => {
    // Clear localStorage and setup spies
    localStorage.clear();
    spyOn(console, 'log');
    spyOn(console, 'error');
    spyOn(console, 'warn');
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Component Initialization', () => {
    it('should initialize with default values', () => {
      expect(component.admin).toBeNull();
      expect(component.showElement).toBe(false);
      expect(component.isEditUserModalOpen).toBe(false);
      expect(component.submitted).toBe(false);
      expect(component.isSubmitting).toBe(false);
      expect(component.selectedFile).toBeNull();
    });

    it('should load admin data and initialize form on ngOnInit', () => {
      // Arrange
      localStorage.setItem('user', JSON.stringify(mockAdmin));
      spyOn(component as any, 'loadAdminData').and.callThrough();
      spyOn(component as any, 'initializeForm').and.callThrough();
      spyOn(component as any, 'patchFormWithAdminData').and.callThrough();

      // Act
      component.ngOnInit();

      // Assert
      expect((component as any).loadAdminData).toHaveBeenCalled();
      expect((component as any).initializeForm).toHaveBeenCalled();
      expect((component as any).patchFormWithAdminData).toHaveBeenCalled();
    });
  });

  describe('loadAdminData', () => {
    it('should load admin data from localStorage', () => {
      // Arrange
      localStorage.setItem('user', JSON.stringify(mockAdmin));

      // Act
      component['loadAdminData']();

      // Assert
      expect(component.admin).toEqual(mockAdmin);
      expect(component.profileImageUrl).toBe(mockAdmin.profilePicture);
    });

    it('should handle invalid JSON in localStorage', () => {
      // Arrange
      localStorage.setItem('user', 'invalid-json');

      // Act
      component['loadAdminData']();

      // Assert
      expect(component.admin).toBeNull();
      expect(console.error).toHaveBeenCalledWith('Error parsing user data from localStorage:', jasmine.any(Error));
      expect(component.profileImageUrl).toBe('../../../assets/svg/Avatar.svg');
    });

    it('should handle missing user in localStorage', () => {
      // Act
      component['loadAdminData']();

      // Assert
      expect(component.admin).toBeNull();
      expect(console.warn).toHaveBeenCalledWith('No user found in localStorage');
      expect(component.profileImageUrl).toBe('../../../assets/svg/Avatar.svg');
    });
  });

  describe('Form Initialization and Validation', () => {
    beforeEach(() => {
      component.ngOnInit();
    });

    it('should initialize form with required validators', () => {
      expect(component.editUserForm).toBeDefined();
      expect(component.editUserForm.get('firstName')?.hasError('required')).toBe(true);
      expect(component.editUserForm.get('lastName')?.hasError('required')).toBe(true);
      expect(component.editUserForm.get('email')?.hasError('required')).toBe(true);
      expect(component.editUserForm.get('gender')?.hasError('required')).toBe(true);
    });

    it('should patch form with admin data', () => {
      // Arrange
      component.admin = mockAdmin;

      // Act
      component['patchFormWithAdminData']();

      // Assert
      expect(component.editUserForm.get('firstName')?.value).toBe(mockAdmin.firstName);
      expect(component.editUserForm.get('lastName')?.value).toBe(mockAdmin.lastName);
      expect(component.editUserForm.get('email')?.value).toBe(mockAdmin.email);
      expect(component.editUserForm.get('gender')?.value).toBe(String(mockAdmin.gender));
    });

    it('should validate email format correctly', () => {
      // Valid emails
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ];

      validEmails.forEach(email => {
        component.editUserForm.patchValue({ email });
        const errors = component.customEmailFormatValidator(component.editUserForm);
        expect(errors).toBeNull();
      });
    });

    it('should invalidate incorrect email formats', () => {
      // Invalid emails
      const invalidEmails = [
        '<EMAIL>', // Starts with number
        'test@',
        '@example.com',
        'test.example.com'
      ];

      invalidEmails.forEach(email => {
        component.editUserForm.patchValue({ email });
        const errors = component.customEmailFormatValidator(component.editUserForm);
        expect(errors).toEqual({ validEmail: true });
      });
    });
  });

  describe('Modal Management', () => {
    it('should open user profile dialog', () => {
      // Arrange
      spyOn(component, 'resetEditForm');

      // Act
      component.openUserProfileDialog();

      // Assert
      expect(component.resetEditForm).toHaveBeenCalled();
      expect(component.isEditUserModalOpen).toBe(true);
    });

    it('should close edit user modal', () => {
      // Arrange
      component.isEditUserModalOpen = true;
      component.submitted = true;
      component.isSubmitting = true;
      spyOn(component, 'resetEditForm');

      // Act
      component.closeEditUserModal();

      // Assert
      expect(component.isEditUserModalOpen).toBe(false);
      expect(component.submitted).toBe(false);
      expect(component.isSubmitting).toBe(false);
      expect(component.resetEditForm).toHaveBeenCalled();
    });

    it('should close edit modal via cross icon', () => {
      // Arrange
      spyOn(component, 'closeEditUserModal');

      // Act
      component.closeEditModal();

      // Assert
      expect(component.closeEditUserModal).toHaveBeenCalled();
    });
  });

  describe('File Handling', () => {
    beforeEach(() => {
      component.ngOnInit();
      component.admin = mockAdmin;
    });

    it('should handle valid image file selection', () => {
      // Arrange
      const mockFile = new File([''], 'test.jpg', { type: 'image/jpeg' });
      const mockEvent = {
        currentTarget: {
          files: [mockFile]
        }
      } as any;

      spyOn(window, 'FileReader').and.returnValue({
        readAsDataURL: jasmine.createSpy('readAsDataURL').and.callFake(function(this: any) {
          this.onload({ target: { result: 'data:image/jpeg;base64,test' } });
        }),
        onload: null,
        result: 'data:image/jpeg;base64,test'
      } as any);

      // Act
      component.onFileChange(mockEvent);

      // Assert
      expect(component.selectedFile).toBe(mockFile);
      expect(component.profileImageUrl).toBe('data:image/jpeg;base64,test');
    });

    it('should reject non-image files', () => {
      // Arrange
      const mockFile = new File([''], 'test.txt', { type: 'text/plain' });
      const mockEvent = {
        currentTarget: {
          files: [mockFile]
        }
      } as any;
      spyOn(window, 'alert');
      spyOn(component as any, 'resetFileInput');

      // Act
      component.onFileChange(mockEvent);

      // Assert
      expect(window.alert).toHaveBeenCalledWith('Please select an image file.');
      expect((component as any).resetFileInput).toHaveBeenCalled();
      expect(component.selectedFile).toBeNull();
      expect(component.profileImageUrl).toBe(mockAdmin.profilePicture);
    });

    it('should handle no file selection', () => {
      // Arrange
      const mockEvent = {
        currentTarget: {
          files: []
        }
      } as any;
      spyOn(component as any, 'resetFileInput');

      // Act
      component.onFileChange(mockEvent);

      // Assert
      expect((component as any).resetFileInput).toHaveBeenCalled();
      expect(component.selectedFile).toBeNull();
      expect(component.profileImageUrl).toBe(mockAdmin.profilePicture);
    });
  });

  describe('Form Submission', () => {
    beforeEach(() => {
      component.ngOnInit();
      component.admin = mockAdmin;
      component['patchFormWithAdminData']();
    });

    it('should not submit invalid form', async () => {
      // Arrange
      component.editUserForm.patchValue({
        firstName: '',
        lastName: '',
        email: '',
        gender: ''
      });

      // Act
      await component.onEditSubmit();

      // Assert
      expect(component.submitted).toBe(true);
      expect(component.isSubmitting).toBe(false);
      expect(updateService.update).not.toHaveBeenCalled();
    });

    it('should not submit when admin ID is missing', async () => {
      // Arrange
      component.admin = { ...mockAdmin, id: undefined } as any;
      component.editUserForm.patchValue({
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        gender: '1'
      });

      // Act
      await component.onEditSubmit();

      // Assert
      expect(console.error).toHaveBeenCalledWith('Admin ID is missing.');
      expect(updateService.update).not.toHaveBeenCalled();
    });

    it('should submit form successfully without file upload', async () => {
      // Arrange
      component.editUserForm.patchValue({
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        gender: '1'
      });
      updateService.update.and.returnValue(Promise.resolve({ user: mockAdmin }));
      spyOn(component, 'closeEditUserModal');

      // Act
      await component.onEditSubmit();

      // Assert
      expect(updateService.update).toHaveBeenCalledWith({
        tableName: 'admins',
        id: mockAdmin.id,
        data: {
          firstName: 'John',
          lastName: 'Doe',
          gender: '1',
          email: '<EMAIL>',
          profilePicture: mockAdmin.profilePicture
        }
      });
      expect(component.closeEditUserModal).toHaveBeenCalled();
    });

    it('should handle file upload and form submission', async () => {
      // Arrange
      const mockFile = new File([''], 'test.jpg', { type: 'image/jpeg' });
      component.selectedFile = mockFile;
      component.editUserForm.patchValue({
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        gender: '1'
      });

      uploadService.upload.and.returnValue(Promise.resolve('https://example.com/new-image.jpg'));
      updateService.update.and.returnValue(Promise.resolve({ user: mockAdmin }));
      spyOn(component, 'closeEditUserModal');

      // Act
      await component.onEditSubmit();

      // Assert
      expect(uploadService.upload).toHaveBeenCalled();
      expect(updateService.update).toHaveBeenCalledWith({
        tableName: 'admins',
        id: mockAdmin.id,
        data: {
          firstName: 'John',
          lastName: 'Doe',
          gender: '1',
          email: '<EMAIL>',
          profilePicture: 'https://example.com/new-image.jpg'
        }
      });
      expect(component.closeEditUserModal).toHaveBeenCalled();
    });

    it('should handle upload error', async () => {
      // Arrange
      const mockFile = new File([''], 'test.jpg', { type: 'image/jpeg' });
      component.selectedFile = mockFile;
      component.editUserForm.patchValue({
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        gender: '1'
      });

      uploadService.upload.and.returnValue(Promise.reject(new Error('Upload failed')));

      // Act
      await component.onEditSubmit();

      // Assert
      expect(uploadService.upload).toHaveBeenCalled();
      expect(updateService.update).not.toHaveBeenCalled();
      expect(component.isSubmitting).toBe(false);
      expect(console.error).toHaveBeenCalledWith('Error uploading profile picture:', jasmine.any(Error));
    });

    it('should handle update error', async () => {
      // Arrange
      component.editUserForm.patchValue({
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        gender: '1'
      });
      updateService.update.and.returnValue(Promise.reject(new Error('Update failed')));

      // Act
      await component.onEditSubmit();

      // Assert
      expect(updateService.update).toHaveBeenCalled();
      expect(component.isSubmitting).toBe(false);
      expect(console.error).toHaveBeenCalledWith('Error updating profile record:', jasmine.any(Error));
    });
  });

  describe('Utility Methods', () => {
    it('should reset edit form', () => {
      // Arrange
      component.ngOnInit();
      component.admin = mockAdmin;
      component.submitted = true;
      component.isSubmitting = true;
      component.selectedFile = new File([''], 'test.jpg', { type: 'image/jpeg' });
      spyOn(component as any, 'resetFileInput');
      spyOn(component as any, 'patchFormWithAdminData');

      // Act
      component.resetEditForm();

      // Assert
      expect(component.submitted).toBe(false);
      expect(component.isSubmitting).toBe(false);
      expect(component.selectedFile).toBeNull();
      expect((component as any).resetFileInput).toHaveBeenCalled();
      expect((component as any).patchFormWithAdminData).toHaveBeenCalled();
      expect(component.editUserForm.pristine).toBe(true);
      expect(component.editUserForm.untouched).toBe(true);
    });

    it('should activate navigation', () => {
      // Act
      component.activateNav('test-nav');

      // Assert
      expect(console.log).toHaveBeenCalledWith('activateNav called with:', 'test-nav');
    });

    it('should trigger side menu open/close', () => {
      // Act
      component.sideMenuOpenClose();

      // Assert
      expect(manageSideMenuService.triggerHeaderClick).toHaveBeenCalled();
    });

    it('should confirm delete and logout', async () => {
      // Act
      await component.confirmDelete();

      // Assert
      expect(router.navigate).toHaveBeenCalledWith(['']);
      expect(localStorage.getItem('user')).toBeNull();
    });
  });
});
