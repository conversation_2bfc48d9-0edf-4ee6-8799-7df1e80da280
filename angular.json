{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"BOG_ADANI_ANGULAR": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/bog-adani-angular", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["node_modules/bootstrap/dist/css/bootstrap.min.css", "node_modules/bootstrap-icons/font/bootstrap-icons.css", "node_modules/font-awesome/css/font-awesome.min.css", "node_modules/quill/dist/quill.snow.css", "src/styles.scss"], "scripts": ["node_modules/bootstrap/dist/js/bootstrap.min.js"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "3.80mb", "maximumError": "3.90mb"}, {"type": "anyComponentStyle", "maximumWarning": "10kb", "maximumError": "25kb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "BOG_ADANI_ANGULAR:build:production"}, "development": {"buildTarget": "BOG_ADANI_ANGULAR:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "BOG_ADANI_ANGULAR:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss", "node_modules/quill/dist/quill.snow.css"], "scripts": []}}}}}, "cli": {"analytics": false}}